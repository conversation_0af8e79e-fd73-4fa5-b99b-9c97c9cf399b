#!/usr/bin/env python3

import requests
import json

def test_streaming():
    url = "http://localhost:8000/api/v1/chat/stream"
    headers = {
        "Content-Type": "application/json",
        "X-Device-ID": "test-device",
        "X-User-ID": "test-user"
    }
    data = {
        "content": "Hi",
        "is_user": True
    }
    
    print("Testing streaming endpoint...")
    print(f"URL: {url}")
    print(f"Headers: {headers}")
    print(f"Data: {data}")
    print("\nStreaming response:")
    print("-" * 50)
    
    try:
        response = requests.post(url, headers=headers, json=data, stream=True)
        print(f"Status Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        print("\nStream content:")
        
        for line in response.iter_lines(decode_unicode=True):
            if line:
                print(f"Line: {repr(line)}")
                if line.startswith("data: "):
                    data_part = line[6:]  # Remove "data: " prefix
                    if data_part == "[DONE]":
                        print("Stream completed!")
                        break
                    else:
                        try:
                            parsed = json.loads(data_part)
                            print(f"Parsed JSON: {parsed}")
                            if "content" in parsed:
                                print(f"Content: '{parsed['content']}'")
                        except json.JSONDecodeError as e:
                            print(f"JSON decode error: {e}")
                            print(f"Raw data: {repr(data_part)}")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_streaming()
