#!/bin/bash

# Close Xcode if it's open
killall Xcode || true

# Clean derived data
rm -rf ~/Library/Developer/Xcode/DerivedData/* || true

# Remove existing package cache
rm -rf "Pluto AI - Chat <PERSON>t Assistant.xcodeproj/project.xcworkspace/xcshareddata/swiftpm" || true

# Open Xcode project
open "Pluto AI - Chat Bot Assistant.xcodeproj"

echo "Xcode has been reopened. Please follow these steps:"
echo "1. In Xcode, select your project in the Project Navigator (left sidebar)"
echo "2. Select File > Add Packages..."
echo "3. In the search bar, enter: https://github.com/supabase/supabase-swift"
echo "4. Click Add Package"
echo "5. Select all the Supabase products you need (Auth, Functions, PostgREST, Realtime, Storage)"
echo "6. Click Add Package"
echo "7. Repeat the process for RevenueCat by searching for: https://github.com/RevenueCat/purchases-ios"
echo "8. Build the project (Command+B)"
