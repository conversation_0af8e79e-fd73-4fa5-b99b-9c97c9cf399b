#!/bin/bash

# Path to the assistant images folder
ASSISTANT_FOLDER="/Users/<USER>/Documents/GitHub/Pluto-AI---<PERSON><PERSON>-<PERSON><PERSON>-Assistant/frontend/Pluto AI - Chat <PERSON>t Assistant/Assets.xcassets/assistant"

# Create image assets for each JPG file
for jpg_file in "$ASSISTANT_FOLDER"/*.jpg; do
    # Get the base name without extension
    base_name=$(basename "$jpg_file" .jpg)

    # Create the imageset folder
    mkdir -p "$ASSISTANT_FOLDER/$base_name.imageset"

    # Create the Contents.json file
    cat > "$ASSISTANT_FOLDER/$base_name.imageset/Contents.json" << EOF
{
  "images" : [
    {
      "filename" : "$base_name.jpg",
      "idiom" : "universal",
      "scale" : "1x"
    }
  ],
  "info" : {
    "author" : "xcode",
    "version" : 1
  }
}
EOF

    # Copy the JPG file to the imageset folder
    cp "$jpg_file" "$ASSISTANT_FOLDER/$base_name.imageset/$base_name.jpg"
done

# Remove the original JPG files
rm -f "$ASSISTANT_FOLDER"/*.jpg

# Create a simple mapping for assistants that don't have images
create_symlink() {
    local source="$1"
    local target="$2"
    if [ ! -d "$ASSISTANT_FOLDER/$target.imageset" ] && [ -d "$ASSISTANT_FOLDER/$source.imageset" ]; then
        echo "Creating symbolic link for $target -> $source"
        ln -sf "$ASSISTANT_FOLDER/$source.imageset" "$ASSISTANT_FOLDER/$target.imageset"
    fi
}

# List of assistants from the backend
declare -a backend_assistants=(
    "Artist"
    "Logo Designer"
    "Creative Writer"
    "Business Planner"
    "Study Helper"
    "Language Teacher"
    "Chef"
    "Financial Analyst"
    "Lawyer"
    "Relationship Coach"
    "Marketing Expert"
    "Zodiac Expert"
    "Tattoo Artist"
    "Routine Planner"
    "Interviewer"
    "Wellness Advisor"
    "Research Assistant"
    "Fitness & Health Coach"
    "Travel Guide"
    "News Reporter"
)

# Create symbolic links for missing assistants
create_symlink "Artist" "AI Image Generator"

# Check for missing assistants
for assistant in "${backend_assistants[@]}"; do
    if [ ! -d "$ASSISTANT_FOLDER/$assistant.imageset" ]; then
        echo "Missing image for assistant: $assistant"
    fi
done

echo "Conversion complete!"
