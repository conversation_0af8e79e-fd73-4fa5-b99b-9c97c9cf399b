# 🚀 Pluto AI Memory System Setup Guide

This guide will help you set up the comprehensive memory system for your Pluto AI chatbot backend.

## 📋 Prerequisites

- Python 3.8+
- FastAPI backend already running
- OpenAI API key (for memory extraction)
- Database (SQLite for development, PostgreSQL for production)

## 🛠️ Installation Steps

### 1. Install Dependencies

The memory system uses the existing dependencies. Make sure you have:

```bash
pip install fastapi sqlalchemy openai httpx python-multipart
```

### 2. Set Environment Variables

Add to your `.env` file:

```bash
# Required for memory extraction
OPENAI_API_KEY=your_openai_api_key_here

# Database (SQLite for development)
DATABASE_URL=sqlite:///./pluto_ai.db

# Or PostgreSQL for production
# DATABASE_URL=postgresql://user:password@localhost/pluto_ai

# Optional
DEBUG=true
```

### 3. Initialize Database

Run the database initialization script:

```bash
cd backend
python init_memory_db.py
```

This will create all necessary tables:
- `user_memories` - Core memory storage
- `memory_interactions` - Usage tracking
- `conversation_contexts` - Conversation summaries
- `memory_preferences` - User privacy settings

### 4. Test the System

Run the test script to verify everything works:

```bash
python test_memory_system.py
```

### 5. Start the Server

```bash
uvicorn main:app --reload
```

## 🧠 How It Works

### Memory Integration Flow

1. **User sends message** → Chat endpoint receives request
2. **Memory retrieval** → System finds relevant memories for context
3. **Enhanced prompt** → AI gets personalized context from memories
4. **AI response** → Generated with user's personal context
5. **Memory extraction** → New memories extracted from conversation
6. **Storage** → Memories saved for future conversations

### Memory Types

- **Personal Facts** (`personal_fact`) - Name, job, location, family
- **Preferences** (`preference`) - Likes, dislikes, communication style
- **Goals** (`goal`) - Career goals, projects, learning objectives
- **Context** (`context`) - Recent events, conversation history
- **Behavior** (`behavior`) - Communication patterns, response preferences
- **Technical** (`technical`) - Programming languages, tools, skills

## 📡 API Usage Examples

### Create a Memory

```bash
curl -X POST "http://localhost:8000/api/v1/memory/create" \
  -H "X-Device-ID: your_device_id" \
  -H "X-User-ID: your_user_id" \
  -H "Content-Type: application/json" \
  -d '{
    "memory_type": "personal_fact",
    "category": "work",
    "title": "Software Engineer",
    "content": "User is a software engineer specializing in Python",
    "keywords": "software engineer, Python",
    "importance_score": 0.8
  }'
```

### Search Memories

```bash
curl -X POST "http://localhost:8000/api/v1/memory/search" \
  -H "X-Device-ID: your_device_id" \
  -H "X-User-ID: your_user_id" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "Python",
    "memory_types": ["personal_fact", "technical"],
    "limit": 10
  }'
```

### Extract Memories from Conversation

```bash
curl -X POST "http://localhost:8000/api/v1/memory/extract" \
  -H "X-Device-ID: your_device_id" \
  -H "X-User-ID: your_user_id" \
  -H "Content-Type: application/json" \
  -d '{
    "conversation_text": "User: I am a Python developer...\nAssistant: Great to meet you!",
    "context": "Introduction",
    "assistant_id": "general"
  }'
```

### Get Memory Statistics

```bash
curl -X GET "http://localhost:8000/api/v1/memory/stats" \
  -H "X-Device-ID: your_device_id" \
  -H "X-User-ID: your_user_id"
```

## 🎯 Chat Integration

The memory system automatically enhances your chat responses. When users chat:

1. **Automatic Context**: Relevant memories are retrieved based on the conversation
2. **Personalized Responses**: AI responses include user-specific context
3. **Continuous Learning**: New memories are extracted and stored
4. **Privacy Controlled**: Users can control memory storage preferences

### Example Enhanced Response

**Without Memory:**
```
User: "Help me with Python"
AI: "I'd be happy to help with Python! What specific topic would you like assistance with?"
```

**With Memory:**
```
User: "Help me with Python"
AI: "I'd be happy to help with your Python development! Since you're a software engineer working on AI projects, would you like assistance with machine learning libraries like TensorFlow or PyTorch, or something else specific to your current work?"
```

## 🔧 Configuration Options

### Memory Preferences (per user)

```python
{
    "memory_enabled": true,           # Enable/disable memory storage
    "auto_extract": true,             # Automatically extract from conversations
    "share_across_assistants": true,  # Share memories between assistants
    "max_memories": 1000,             # Maximum memories to store
    "retention_days": 365,            # How long to keep memories
    "personalization_level": "medium", # low/medium/high
    "memory_importance_threshold": 0.3 # Minimum importance to keep
}
```

### Extraction Settings

Modify `MemoryExtractionService._create_extraction_prompt()` to:
- Adjust extraction criteria
- Add new memory categories
- Change importance scoring
- Customize extraction prompts

## 📊 Monitoring & Analytics

### Memory Statistics Available

- Total memories by type
- Most accessed memories
- Memory growth trends over time
- User interaction patterns
- Memory relevance scores

### Performance Metrics

- Memory retrieval speed
- Extraction accuracy
- Storage efficiency
- User engagement improvement

## 🔒 Privacy & Security

### Built-in Privacy Features

- **Device-based authentication** - No account required
- **User-controlled storage** - Enable/disable memory
- **Selective sharing** - Control cross-assistant memory access
- **Automatic cleanup** - Configurable retention policies
- **Data encryption** - Secure memory storage

### GDPR Compliance

- Users can view all stored memories
- Complete memory deletion available
- Data export functionality
- Consent-based storage

## 🚨 Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check DATABASE_URL in .env
   - Ensure database is running
   - Verify permissions

2. **Memory Extraction Fails**
   - Check OPENAI_API_KEY is set
   - Verify API key has sufficient credits
   - Check network connectivity

3. **No Memories Retrieved**
   - Verify user_id and device_id are consistent
   - Check memory importance thresholds
   - Ensure memories exist for the user

4. **Slow Memory Retrieval**
   - Add database indexes (already included)
   - Consider memory cleanup
   - Optimize search queries

### Debug Mode

Enable debug logging:

```bash
DEBUG=true uvicorn main:app --reload
```

## 🎉 Success!

Your Pluto AI chatbot now has a comprehensive memory system! Users will experience:

- **Personalized conversations** that remember their preferences
- **Contextual responses** based on their background and goals
- **Continuous improvement** as the AI learns more about them
- **Privacy control** over their personal information

The memory system will make your chatbot feel more human and provide significantly better user experiences, similar to ChatGPT's memory feature.

## 📞 Support

If you encounter any issues:

1. Check the logs for error messages
2. Verify all environment variables are set
3. Test individual API endpoints
4. Run the test script to isolate issues
5. Check database connectivity and permissions

Happy chatting with your memory-enabled AI! 🤖✨
