# fly.toml app configuration file generated for pluto-ai-chatbot on 2025-05-24T17:39:27+08:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'pluto-ai-chatbot'
primary_region = 'sjc'

[build]

[http_service]
  internal_port = 8000
  force_https = true
  auto_stop_machines = 'stop'
  auto_start_machines = true
  min_machines_running = 0
  processes = ['app']

[[vm]]
  size = 'shared-cpu-1x'
