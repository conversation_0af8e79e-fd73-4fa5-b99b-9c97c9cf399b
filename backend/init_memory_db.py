#!/usr/bin/env python3
"""
Database initialization script for Pluto AI Memory System
Creates all necessary tables for user memory storage and management.
"""

import os
import sys
from pathlib import Path

# Add the app directory to the Python path
sys.path.append(str(Path(__file__).parent))

from app.database import create_tables, engine, Base
from app.models import MemoryType

def init_database():
    """Initialize the database with memory tables"""
    print("🚀 Initializing Pluto AI Memory Database...")
    
    try:
        # Create all tables
        print("📊 Creating database tables...")
        create_tables()
        print("✅ Database tables created successfully!")
        
        # Print table information
        print("\n📋 Created tables:")
        for table_name in Base.metadata.tables.keys():
            print(f"  - {table_name}")
        
        print("\n🧠 Memory System Features:")
        print("  ✅ User Memory Storage")
        print("  ✅ Memory Categorization (Personal Facts, Preferences, Goals, etc.)")
        print("  ✅ Intelligent Memory Retrieval")
        print("  ✅ Conversation Context Tracking")
        print("  ✅ Memory Analytics & Insights")
        print("  ✅ Privacy Controls & Preferences")
        
        print("\n🔧 Memory Types Available:")
        for memory_type in MemoryType:
            print(f"  - {memory_type.value.replace('_', ' ').title()}")
        
        print("\n🌟 Memory System Ready!")
        print("Your Pluto AI chatbot can now provide personalized responses based on user memories!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error initializing database: {e}")
        return False

def check_database_connection():
    """Check if database connection is working"""
    try:
        # Try to connect to the database
        with engine.connect() as connection:
            print("✅ Database connection successful!")
            return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        print("\n💡 Make sure your database is running and the DATABASE_URL is correct.")
        return False

def main():
    """Main initialization function"""
    print("=" * 60)
    print("🤖 PLUTO AI MEMORY SYSTEM INITIALIZATION")
    print("=" * 60)
    
    # Check database connection first
    if not check_database_connection():
        sys.exit(1)
    
    # Initialize the database
    if init_database():
        print("\n" + "=" * 60)
        print("🎉 INITIALIZATION COMPLETE!")
        print("=" * 60)
        print("\n📚 Next Steps:")
        print("1. Start your FastAPI server: uvicorn main:app --reload")
        print("2. Test the memory endpoints at: http://localhost:8000/docs")
        print("3. Start chatting with personalized AI responses!")
        print("\n🔗 Memory API Endpoints:")
        print("  - POST /api/v1/memory/create - Create new memories")
        print("  - GET  /api/v1/memory/search - Search memories")
        print("  - POST /api/v1/memory/extract - Extract memories from conversations")
        print("  - GET  /api/v1/memory/stats - View memory analytics")
        print("  - GET  /api/v1/memory/relevant/context - Get relevant memories")
    else:
        print("\n❌ Initialization failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
