from fastapi import <PERSON><PERSON><PERSON>, Depends
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

from app.config import API_V1_PREFIX
from app.routers import auth, chat, subscription, assistant, ai_model, image, memory

# Create FastAPI app
app = FastAPI(
    title="Pluto AI Chatbot API",
    description="Backend API for Pluto AI Chatbot Assistant",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(auth.router, prefix=API_V1_PREFIX)
app.include_router(chat.router, prefix=API_V1_PREFIX)
app.include_router(subscription.router, prefix=API_V1_PREFIX)
app.include_router(assistant.router, prefix=API_V1_PREFIX)
app.include_router(ai_model.router, prefix=API_V1_PREFIX)
app.include_router(image.router, prefix=API_V1_PREFIX)
app.include_router(memory.router, prefix=API_V1_PREFIX)

@app.get("/")
async def root():
    return {
        "message": "Welcome to Pluto AI Chatbot API",
        "docs": "/docs",
        "version": "1.0.0"
    }

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)
