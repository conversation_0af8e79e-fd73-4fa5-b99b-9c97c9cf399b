from pydantic import BaseModel, Field, EmailStr
from typing import List, Optional, Dict, Any
from datetime import datetime
import uuid
from enum import Enum

# Authentication models
class UserCreate(BaseModel):
    email: EmailStr
    password: str
    name: Optional[str] = None

class UserLogin(BaseModel):
    email: EmailStr
    password: str

class UserResponse(BaseModel):
    id: str
    email: EmailStr
    name: Optional[str] = None
    is_pro: bool = False
    created_at: datetime

class Token(BaseModel):
    access_token: str
    token_type: str

# Chat models
class MessageCreate(BaseModel):
    content: str
    is_user: bool = True

class ConversationMessage(BaseModel):
    content: str
    is_user: bool

class ConversationRequest(BaseModel):
    messages: List[ConversationMessage]

class MessageResponse(BaseModel):
    id: str
    content: str
    is_user: bool
    created_at: datetime

class ChatCreate(BaseModel):
    assistant_id: str
    title: str = "New Chat"
    device_id: Optional[str] = None
    user_id: Optional[str] = None

class ChatResponse(BaseModel):
    id: str
    user_id: str
    assistant_id: str
    title: str
    created_at: datetime
    messages: Optional[List[MessageResponse]] = None

# OpenAI models
class ChatMessage(BaseModel):
    role: str
    content: str

class ChatCompletionRequest(BaseModel):
    model: str
    messages: List[ChatMessage]
    temperature: float = 0.7
    max_tokens: Optional[int] = None
    max_completion_tokens: Optional[int] = None
    stream: Optional[bool] = False

    def model_dump(self, **kwargs):
        """Custom model dump that excludes None values"""
        data = super().model_dump(**kwargs)
        # Remove None values to avoid API errors
        return {k: v for k, v in data.items() if v is not None}

class ChatCompletionChoice(BaseModel):
    message: ChatMessage
    finish_reason: Optional[str] = None
    index: int = 0

class ChatCompletionUsage(BaseModel):
    prompt_tokens: int
    completion_tokens: int
    total_tokens: int

class ChatCompletionResponse(BaseModel):
    id: str = Field(default_factory=lambda: f"chatcmpl-{uuid.uuid4()}")
    object: str = "chat.completion"
    created: int = Field(default_factory=lambda: int(datetime.now().timestamp()))
    model: str
    choices: List[ChatCompletionChoice]
    usage: ChatCompletionUsage

# AI Model models
class AIModel(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    is_pro: bool
    icon_name: str

class AIModelResponse(BaseModel):
    models: List[AIModel]

# Subscription models
class SubscriptionVerify(BaseModel):
    receipt: str
    product_id: str

class SubscriptionResponse(BaseModel):
    is_active: bool
    expiration_date: Optional[datetime] = None
    product_id: Optional[str] = None

# Assistant models
class Color(BaseModel):
    red: float = 0.0
    green: float = 0.0
    blue: float = 0.0
    opacity: float = 1.0

class SuggestedPrompt(BaseModel):
    text: str
    icon: str
    description: Optional[str] = None

class Assistant(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    description: str
    icon_name: str
    background_color: Color
    system_message: str
    suggested_prompts: Optional[List[SuggestedPrompt]] = None

class AssistantResponse(BaseModel):
    assistants: List[Assistant]

# Tool models
class Tool(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    description: str
    icon: str
    is_new: bool
    icon_background_color: Color
    system_message: str

class ToolResponse(BaseModel):
    tools: List[Tool]

# Suggestion models
class SuggestionCategory(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    icon: str
    color: Color
    system_message: str

class Suggestion(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    text: str
    icon: str
    category: str
    system_message: Optional[str] = None

class SuggestionCategoryResponse(BaseModel):
    categories: List[SuggestionCategory]

class SuggestionResponse(BaseModel):
    suggestions: List[Suggestion]

# Image generation models
class ImageGenerationRequest(BaseModel):
    prompt: str
    model: Optional[str] = "dall-e-3"
    size: Optional[str] = "1024x1024"
    quality: Optional[str] = "standard"
    n: Optional[int] = 1

class ImageGenerationResponse(BaseModel):
    created: int = Field(default_factory=lambda: int(datetime.now().timestamp()))
    data: List[Dict[str, str]]

# Memory models
class MemoryType(str, Enum):
    PERSONAL_FACT = "personal_fact"
    PREFERENCE = "preference"
    GOAL = "goal"
    CONTEXT = "context"
    BEHAVIOR = "behavior"
    TECHNICAL = "technical"

class MemoryCreate(BaseModel):
    memory_type: MemoryType
    category: Optional[str] = None
    title: str
    content: str
    keywords: Optional[str] = None
    importance_score: Optional[float] = Field(default=0.5, ge=0.0, le=1.0)
    confidence_score: Optional[float] = Field(default=1.0, ge=0.0, le=1.0)

class MemoryUpdate(BaseModel):
    title: Optional[str] = None
    content: Optional[str] = None
    keywords: Optional[str] = None
    importance_score: Optional[float] = Field(default=None, ge=0.0, le=1.0)
    confidence_score: Optional[float] = Field(default=None, ge=0.0, le=1.0)

class MemoryResponse(BaseModel):
    id: str
    memory_type: str
    category: Optional[str] = None
    title: str
    content: str
    keywords: Optional[str] = None
    importance_score: float
    confidence_score: float
    access_count: int
    last_accessed: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

class MemorySearchRequest(BaseModel):
    query: Optional[str] = None
    memory_types: Optional[List[MemoryType]] = None
    categories: Optional[List[str]] = None
    min_importance: Optional[float] = Field(default=0.0, ge=0.0, le=1.0)
    limit: Optional[int] = Field(default=20, ge=1, le=100)

class MemorySearchResponse(BaseModel):
    memories: List[MemoryResponse]
    total_count: int
    query_used: Optional[str] = None

class MemoryExtractRequest(BaseModel):
    conversation_text: str
    context: Optional[str] = None
    assistant_id: Optional[str] = None

class ExtractedMemory(BaseModel):
    memory_type: MemoryType
    category: Optional[str] = None
    title: str
    content: str
    keywords: Optional[str] = None
    importance_score: float
    confidence_score: float
    reasoning: str  # Why this was extracted as a memory

class MemoryExtractionResponse(BaseModel):
    extracted_memories: List[ExtractedMemory]
    conversation_summary: Optional[str] = None

class MemoryStatsResponse(BaseModel):
    total_memories: int
    memories_by_type: Dict[str, int]
    most_accessed_memories: List[MemoryResponse]
    recent_memories: List[MemoryResponse]
    memory_growth_trend: List[Dict[str, Any]]  # Date and count data

class MemoryPreferencesUpdate(BaseModel):
    memory_enabled: Optional[bool] = None
    auto_extract: Optional[bool] = None
    share_across_assistants: Optional[bool] = None
    max_memories: Optional[int] = Field(default=None, ge=100, le=10000)
    retention_days: Optional[int] = Field(default=None, ge=30, le=3650)
    personalization_level: Optional[str] = Field(default=None, pattern="^(low|medium|high)$")
    memory_importance_threshold: Optional[float] = Field(default=None, ge=0.0, le=1.0)

class MemoryPreferencesResponse(BaseModel):
    memory_enabled: bool
    auto_extract: bool
    share_across_assistants: bool
    max_memories: int
    retention_days: int
    personalization_level: str
    memory_importance_threshold: float
    created_at: datetime
    updated_at: datetime
