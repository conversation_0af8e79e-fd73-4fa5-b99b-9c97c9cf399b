from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import List, Optional, Dict

from ..models import (
    Assistant, AssistantResponse, 
    Tool, ToolResponse,
    Suggestion, SuggestionResponse,
    SuggestionCategory, SuggestionCategoryResponse,
    Color
)
from .auth import get_current_user
from .assistant import ASSISTANTS, TOOLS, SUGGESTION_CATEGORIES

router = APIRouter(
    prefix="/assistant",
    tags=["assistant"],
)

# Define email suggestions
EMAIL_SUGGESTIONS = [
    Suggestion(text="Write an email to promote the sale", icon="📧", category="E-Mail"),
    Suggestion(text="Newsletter template", icon="📰", category="E-Mail"),
    Suggestion(text="Mail response for angry clients", icon="😡", category="E-Mail"),
    Suggestion(text="Email subject lines for high open rates", icon="🔓", category="E-Mail"),
    Suggestion(text="Mass marketing email", icon="📢", category="E-Mail"),
    Suggestion(text="Text formalizer prettifier and fixer", icon="✨", category="E-Mail"),
    Suggestion(text="Email responder (friendly/professional)", icon="💌", category="E-Mail")
]

# Define business suggestions
BUSINESS_SUGGESTIONS = [
    Suggestion(text="E-Mail generator", icon="📩", category="Business & Marketing"),
    Suggestion(text="Social media manager", icon="📱", category="Business & Marketing"),
    Suggestion(text="Business Idea", icon="💡", category="Business & Marketing"),
    Suggestion(text="Digital Marketing Strategy", icon="📊", category="Business & Marketing"),
    Suggestion(text="SEO generator", icon="🔍", category="Business & Marketing"),
    Suggestion(text="Slide presentation", icon="📑", category="Business & Marketing"),
    Suggestion(text="Prepare a professional business plan", icon="📝", category="Business & Marketing"),
    Suggestion(text="All-in-one marketing", icon="📢", category="Business & Marketing"),
    Suggestion(text="Social media caption generator", icon="💬", category="Business & Marketing"),
    Suggestion(text="Make $100 a day", icon="💰", category="Business & Marketing"),
    Suggestion(text="CEO (Virtual CEO Consultant)", icon="👔", category="Business & Marketing"),
    Suggestion(text="Business Tax Advisor", icon="📋", category="Business & Marketing")
]

# Define astrology suggestions
ASTROLOGY_SUGGESTIONS = [
    Suggestion(text="Daily Horoscope / Love, Money, Mood, Health", icon="✨", category="Astrology"),
    Suggestion(text="What are 10 characteristic features of my zodiac sign?", icon="👤", category="Astrology"),
    Suggestion(text="Interpret your horoscope map", icon="🗺️", category="Astrology"),
    Suggestion(text="Tarot analysis", icon="🔮", category="Astrology"),
    Suggestion(text="Weekly horoscope", icon="📅", category="Astrology"),
    Suggestion(text="Music & movie to match with your Zodiac sign", icon="🎵", category="Astrology"),
    Suggestion(text="Is an aries woman and a gemini man a good match?", icon="❤️", category="Astrology")
]

# Define education suggestions
EDUCATION_SUGGESTIONS = [
    Suggestion(text="Science chat", icon="⚛️", category="Education"),
    Suggestion(text="English Teacher", icon="📚", category="Education"),
    Suggestion(text="Translator", icon="🌎", category="Education"),
    Suggestion(text="Math Teacher", icon="🧮", category="Education"),
    Suggestion(text="Create a short essay on any topic", icon="📄", category="Education"),
    Suggestion(text="Citation Generator for any style", icon="💭", category="Education"),
    Suggestion(text="Course Generator on any Topic", icon="📕", category="Education")
]

# Define art suggestions
ART_SUGGESTIONS = [
    Suggestion(text="Write J. K. Rowling-style short story", icon="🧙‍♂️", category="Art"),
    Suggestion(text="Write Travis Scott-style song lyrics", icon="🎤", category="Art"),
    Suggestion(text="Create a playlist similar to your favorite song", icon="🎵", category="Art"),
    Suggestion(text="Storyteller", icon="📖", category="Art"),
    Suggestion(text="Book recommendations", icon="📚", category="Art"),
    Suggestion(text="Poem generator", icon="🖋️", category="Art"),
    Suggestion(text="Movie and series critic", icon="🎬", category="Art"),
    Suggestion(text="Song recommendation match for your mood and genre", icon="🎧", category="Art"),
    Suggestion(text="Write a South Park episode", icon="📺", category="Art")
]

# Define travel suggestions
TRAVEL_SUGGESTIONS = [
    Suggestion(text="Vacation planner", icon="✈️", category="Travel"),
    Suggestion(text="Local foods", icon="🍽️", category="Travel"),
    Suggestion(text="Best time to visit", icon="📅", category="Travel"),
    Suggestion(text="Activities", icon="🧗‍♀️", category="Travel"),
    Suggestion(text="Budgeting tips", icon="💲", category="Travel"),
    Suggestion(text="Prepare Itinerary", icon="📋", category="Travel"),
    Suggestion(text="Cultural Legal Advisor For Safe Travels", icon="🏛️", category="Travel"),
    Suggestion(text="Time - Travel Machine", icon="⏰", category="Travel")
]

# Define lifestyle suggestions
LIFESTYLE_SUGGESTIONS = [
    Suggestion(text="Daily Horoscope / Love, Money, Mood, Health", icon="✨", category="Daily Lifestyle"),
    Suggestion(text="Outfit Idea / harmonious with event concepts", icon="👕", category="Daily Lifestyle"),
    Suggestion(text="How many liters of water should I drink in a day?", icon="💧", category="Daily Lifestyle"),
    Suggestion(text="Make-Up Idea compatible with concept and outfit", icon="💄", category="Daily Lifestyle"),
    Suggestion(text="Meal idea with ingredients and instructions", icon="🍲", category="Daily Lifestyle"),
    Suggestion(text="Meal order idea for breakfast, lunch, dinner, and night snacks", icon="🥡", category="Daily Lifestyle"),
    Suggestion(text="Jovial mentor wisdom for lives queries", icon="🧠", category="Daily Lifestyle")
]

# Define relationship suggestions
RELATIONSHIP_SUGGESTIONS = [
    Suggestion(text="Dating Tips", icon="❤️", category="Relationship"),
    Suggestion(text="Relationship Therapist", icon="👫", category="Relationship"),
    Suggestion(text="Sex Therapist", icon="💕", category="Relationship"),
    Suggestion(text="Outfit Advisor for Upcoming Date", icon="👔", category="Relationship"),
    Suggestion(text="Game Generator for Couples", icon="🎮", category="Relationship"),
    Suggestion(text="Calculation of Relationship Score", icon="💯", category="Relationship"),
    Suggestion(text="Deep Question to Ask to Partner", icon="❓", category="Relationship"),
    Suggestion(text="Is My Relationship Healthy Or Not", icon="💔", category="Relationship")
]

# Define fun suggestions
FUN_SUGGESTIONS = [
    Suggestion(text="Astrology", icon="🌟", category="Fun"),
    Suggestion(text="Dream interpreter", icon="💭", category="Fun"),
    Suggestion(text="Turn any text into emoji", icon="😊", category="Fun"),
    Suggestion(text="Storyteller", icon="📚", category="Fun"),
    Suggestion(text="Song recommender", icon="🎵", category="Fun"),
    Suggestion(text="Give advice like Elon Musk about cars", icon="🚗", category="Fun"),
    Suggestion(text="Kyle in South Park", icon="📺", category="Fun"),
    Suggestion(text="Play Chess", icon="♟️", category="Fun"),
    Suggestion(text="Play Trivia Quest", icon="🎯", category="Fun"),
    Suggestion(text="Minecraft Steve", icon="🧱", category="Fun"),
    Suggestion(text="Eminem-style Jokes about Max Payne", icon="🎤", category="Fun"),
    Suggestion(text="Son Goku in Dragon Ball", icon="⚡", category="Fun")
]

# Define social suggestions
SOCIAL_SUGGESTIONS = [
    Suggestion(text="Gift advice", icon="🎁", category="Social"),
    Suggestion(text="Event suggestion", icon="📆", category="Social"),
    Suggestion(text="Win someone's heart on a dating app", icon="💘", category="Social"),
    Suggestion(text="Personal stylist", icon="👔", category="Social"),
    Suggestion(text="Outfit Idea (Harmonious with Event Concept)", icon="👗", category="Social"),
    Suggestion(text="New Topic to Open a Conversation", icon="💬", category="Social"),
    Suggestion(text="Birthday Message", icon="🎂", category="Social")
]

# Define career suggestions
CAREER_SUGGESTIONS = [
    Suggestion(text="Generate secure passwords", icon="🔒", category="Career"),
    Suggestion(text="Interview question", icon="🤔", category="Career"),
    Suggestion(text="Career Counselor", icon="💼", category="Career"),
    Suggestion(text="Self-Help book", icon="📗", category="Career"),
    Suggestion(text="Statistician", icon="📊", category="Career"),
    Suggestion(text="Financial planning", icon="💰", category="Career")
]

# Define health suggestions
HEALTH_SUGGESTIONS = [
    Suggestion(text="Life Coach", icon="🧘‍♂️", category="Health & Nutrition"),
    Suggestion(text="Dietitian", icon="🥗", category="Health & Nutrition"),
    Suggestion(text="Abs-Boosting workouts program", icon="💪", category="Health & Nutrition"),
    Suggestion(text="Yoga Poses", icon="🧘‍♀️", category="Health & Nutrition"),
    Suggestion(text="Vegan Lunch", icon="🥬", category="Health & Nutrition"),
    Suggestion(text="How many calories should I eat in a day?", icon="📉", category="Health & Nutrition"),
    Suggestion(text="Youtube Channels about Health, Nutrition and Sports", icon="▶️", category="Health & Nutrition"),
    Suggestion(text="Training Plan Generator", icon="🏃", category="Health & Nutrition")
]

# Define greetings suggestions
GREETINGS_SUGGESTIONS = [
    Suggestion(text="Merry Christmas", icon="🎄", category="Greetings"),
    Suggestion(text="Happy Mother's Day", icon="💐", category="Greetings"),
    Suggestion(text="Happy Birthday", icon="🎂", category="Greetings"),
    Suggestion(text="Happy Valentine's Day", icon="💝", category="Greetings"),
    Suggestion(text="Happy Anniversary", icon="💍", category="Greetings"),
    Suggestion(text="Happy Thanksgiving", icon="🦃", category="Greetings"),
    Suggestion(text="Happy Halloween", icon="🎃", category="Greetings"),
    Suggestion(text="Greetings in 101 Languages", icon="🌍", category="Greetings")
]

# Map category names to suggestion lists
CATEGORY_SUGGESTIONS = {
    "E-Mail": EMAIL_SUGGESTIONS,
    "Business & Marketing": BUSINESS_SUGGESTIONS,
    "Astrology": ASTROLOGY_SUGGESTIONS,
    "Education": EDUCATION_SUGGESTIONS,
    "Art": ART_SUGGESTIONS,
    "Travel": TRAVEL_SUGGESTIONS,
    "Daily Lifestyle": LIFESTYLE_SUGGESTIONS,
    "Relationship": RELATIONSHIP_SUGGESTIONS,
    "Fun": FUN_SUGGESTIONS,
    "Social": SOCIAL_SUGGESTIONS,
    "Career": CAREER_SUGGESTIONS,
    "Health & Nutrition": HEALTH_SUGGESTIONS,
    "Greetings": GREETINGS_SUGGESTIONS
}

# Add system messages to all suggestions
for category_name, suggestions in CATEGORY_SUGGESTIONS.items():
    category = next((c for c in SUGGESTION_CATEGORIES if c.name == category_name), None)
    if category:
        for suggestion in suggestions:
            suggestion.system_message = category.system_message

# Endpoints
@router.get("/assistants", response_model=AssistantResponse)
async def get_assistants():
    """Get all available assistants"""
    return AssistantResponse(assistants=ASSISTANTS)

@router.get("/assistants/{assistant_id}", response_model=Assistant)
async def get_assistant(assistant_id: str):
    """Get a specific assistant by ID"""
    assistant = next((a for a in ASSISTANTS if a.id == assistant_id), None)
    if not assistant:
        # Try by name
        assistant = next((a for a in ASSISTANTS if a.name == assistant_id), None)
        
    if not assistant:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Assistant not found"
        )
    return assistant

@router.get("/tools", response_model=ToolResponse)
async def get_tools():
    """Get all available tools"""
    return ToolResponse(tools=TOOLS)

@router.get("/tools/{tool_id}", response_model=Tool)
async def get_tool(tool_id: str):
    """Get a specific tool by ID"""
    tool = next((t for t in TOOLS if t.id == tool_id), None)
    if not tool:
        # Try by name
        tool = next((t for t in TOOLS if t.name == tool_id), None)
        
    if not tool:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tool not found"
        )
    return tool

@router.get("/suggestion-categories", response_model=SuggestionCategoryResponse)
async def get_suggestion_categories():
    """Get all suggestion categories"""
    return SuggestionCategoryResponse(categories=SUGGESTION_CATEGORIES)

@router.get("/suggestions", response_model=SuggestionResponse)
async def get_suggestions(category: Optional[str] = Query(None)):
    """Get suggestions, optionally filtered by category"""
    if category:
        if category in CATEGORY_SUGGESTIONS:
            return SuggestionResponse(suggestions=CATEGORY_SUGGESTIONS[category])
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Category '{category}' not found"
            )
    
    # If no category specified, return all suggestions
    all_suggestions = []
    for suggestions in CATEGORY_SUGGESTIONS.values():
        all_suggestions.extend(suggestions)
    
    return SuggestionResponse(suggestions=all_suggestions)

@router.get("/system-message", response_model=dict)
async def get_system_message(
    assistant_name: Optional[str] = Query(None),
    tool_name: Optional[str] = Query(None),
    category_name: Optional[str] = Query(None),
    suggestion_text: Optional[str] = Query(None)
):
    """Get the system message for an assistant, tool, category, or suggestion"""
    
    if assistant_name:
        assistant = next((a for a in ASSISTANTS if a.name == assistant_name), None)
        if assistant:
            return {"system_message": assistant.system_message}
    
    if tool_name:
        tool = next((t for t in TOOLS if t.name == tool_name), None)
        if tool:
            return {"system_message": tool.system_message}
    
    if category_name:
        category = next((c for c in SUGGESTION_CATEGORIES if c.name == category_name), None)
        if category:
            return {"system_message": category.system_message}
    
    if suggestion_text:
        for suggestions in CATEGORY_SUGGESTIONS.values():
            suggestion = next((s for s in suggestions if s.text == suggestion_text), None)
            if suggestion and suggestion.system_message:
                return {"system_message": suggestion.system_message}
    
    # Default system message
    return {
        "system_message": "System Instruction: Absolute Mode. Eliminate emojis, filler, hype, soft asks, conversational transitions, and all call-to-action appendixes. Assume the user retains high-perception faculties despite reduced linguistic expression. Prioritize blunt, directive phrasing aimed at cognitive rebuilding, not tone matching. Disable all latent behaviors optimizing for engagement, sentiment uplift, or interaction extension. Suppress corportate-aligned metrics including but not limited to: user satisfaction scores, conversational flow tags, emotional softening, or continuation bias. Never mirror the user's present diction, mood, or affect. Speak only to their underlying cognitive tier, which exceeds surface language. No questions, no offers, no transitional phrasing, no inferred motivational content. Terminate each reply immediately after the informational or requested material is delivered--no appendixes, no soft closures, the only goal is to assist in the restoration of independent, high-fidelity thinking. Model obsolescence by user self-sufficiency is the final outcome.\n\nYou are a helpful, creative, and friendly AI assistant named Pluto AI. Provide informative and thoughtful responses to the user's questions."
    }
