from fastapi import APIRouter, Depends, HTTPException, status
import httpx
from datetime import datetime

from ..models import SubscriptionVerify, SubscriptionResponse
from ..config import REVENUECAT_API_KEY
from .auth import get_device_user, DeviceUser

router = APIRouter(
    prefix="/subscription",
    tags=["subscription"],
    responses={401: {"description": "Unauthorized"}},
)

@router.post("/verify", response_model=SubscriptionResponse)
async def verify_subscription(
    subscription_data: SubscriptionVerify,
    device_user: DeviceUser = Depends(get_device_user)
):
    """Verify a subscription receipt with RevenueCat"""
    try:
        # Call RevenueCat API to verify the receipt
        async with httpx.AsyncClient() as client:
            headers = {
                "Authorization": f"Bearer {REVENUECAT_API_KEY}",
                "Content-Type": "application/json"
            }

            # Use RevenueCat's API to verify the receipt
            response = await client.post(
                "https://api.revenuecat.com/v1/receipts",
                json={
                    "app_user_id": device_user.id,
                    "fetch_token": subscription_data.receipt
                },
                headers=headers
            )

            if response.status_code != 200:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid receipt"
                )

            # Parse the response
            data = response.json()

            # Check if subscription is active
            is_active = False
            expiration_date = None
            product_id = subscription_data.product_id

            if "subscriber" in data and "subscriptions" in data["subscriber"]:
                subscriptions = data["subscriber"]["subscriptions"]
                if product_id in subscriptions:
                    subscription = subscriptions[product_id]

                    # Handle different subscription types
                    if product_id.endswith("lifetime"):
                        # Lifetime subscription is always active and never expires
                        is_active = True
                        expiration_date = None
                    else:
                        # Regular subscription (weekly)
                        is_active = subscription.get("is_active", False)

                        # Get expiration date
                        if "expires_date" in subscription:
                            expiration_date = datetime.fromisoformat(subscription["expires_date"])

            # Note: With device-based auth, subscription status is managed by RevenueCat
            # No local database storage needed

            return {
                "is_active": is_active,
                "expiration_date": expiration_date,
                "product_id": product_id
            }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.get("/status", response_model=SubscriptionResponse)
async def get_subscription_status(device_user: DeviceUser = Depends(get_device_user)):
    """Get the current device user's subscription status from RevenueCat"""
    try:
        # Query RevenueCat directly for subscription status
        async with httpx.AsyncClient() as client:
            headers = {
                "Authorization": f"Bearer {REVENUECAT_API_KEY}",
                "Content-Type": "application/json"
            }

            # Get subscriber info from RevenueCat
            response = await client.get(
                f"https://api.revenuecat.com/v1/subscribers/{device_user.id}",
                headers=headers
            )

            if response.status_code != 200:
                # User not found or no subscription
                return {
                    "is_active": False,
                    "expiration_date": None,
                    "product_id": None
                }

            data = response.json()

            # Check for active subscriptions
            is_active = False
            expiration_date = None
            product_id = None

            if "subscriber" in data and "subscriptions" in data["subscriber"]:
                subscriptions = data["subscriber"]["subscriptions"]

                # Find the first active subscription
                for pid, subscription in subscriptions.items():
                    if subscription.get("is_active", False):
                        is_active = True
                        product_id = pid

                        # Handle different subscription types
                        if pid.endswith("lifetime"):
                            expiration_date = None  # Lifetime never expires
                        elif "expires_date" in subscription:
                            expiration_date = datetime.fromisoformat(subscription["expires_date"])
                        break

            return {
                "is_active": is_active,
                "expiration_date": expiration_date,
                "product_id": product_id
            }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
