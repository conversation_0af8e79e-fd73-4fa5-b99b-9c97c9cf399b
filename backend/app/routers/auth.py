from fastapi import <PERSON>Router, HTTPException, status, Header, Depends
from typing import Annotated, Union
from pydantic import BaseModel

router = APIRouter(
    prefix="/auth",
    tags=["device-authentication"],
    responses={401: {"description": "Unauthorized"}},
)

# Pydantic models for API responses
class DeviceUserResponse(BaseModel):
    """Response model for device user"""
    id: str
    device_id: str
    email: str
    is_pro: bool

class AuthStatusResponse(BaseModel):
    """Response model for authentication status"""
    status: str
    user: DeviceUserResponse

# Device-based authentication for apps without login
class DeviceUser:
    """Represents a device-based user for authentication"""
    def __init__(self, device_id: str, user_id: str):
        self.id = user_id
        self.device_id = device_id
        self.email = f"{user_id}@device.local"
        self.is_pro = False  # Default to free tier

    def to_dict(self):
        """Convert to dictionary for API responses"""
        return {
            "id": self.id,
            "device_id": self.device_id,
            "email": self.email,
            "is_pro": self.is_pro
        }

    def to_response(self) -> DeviceUserResponse:
        """Convert to Pydantic response model"""
        return DeviceUserResponse(
            id=self.id,
            device_id=self.device_id,
            email=self.email,
            is_pro=self.is_pro
        )

async def get_device_user(
    x_device_id: Annotated[Union[str, None], Header()] = None,
    x_user_id: Annotated[Union[str, None], Header()] = None
):
    """Get user from device ID and user ID headers"""
    if not x_device_id or not x_user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Device ID and User ID headers required",
        )

    return DeviceUser(device_id=x_device_id, user_id=x_user_id)

@router.get("/device/verify", response_model=AuthStatusResponse)
async def verify_device(device_user: DeviceUser = Depends(get_device_user)):
    """Verify device authentication"""
    return AuthStatusResponse(
        status="authenticated",
        user=device_user.to_response()
    )
