from fastapi import APIRouter, Depends, HTTPException, status, File, UploadFile, Form

from ..models import ImageGenerationRequest, ImageGenerationResponse
from ..services.image_service import ImageService
from .auth import get_device_user

router = APIRouter(
    prefix="/image",
    tags=["image"],
    responses={401: {"description": "Unauthorized"}},
)

@router.post("/generate", response_model=ImageGenerationResponse)
async def generate_image(
    request: ImageGenerationRequest,
    current_user = Depends(get_device_user)
):
    """Generate an image using AI"""

    # For device users, default to standard quality (can be upgraded later)
    quality = "standard"

    try:
        # Generate the image
        image_data = await ImageService.generate_image(
            prompt=request.prompt,
            model=request.model,
            size=request.size,
            quality=quality,
            n=request.n
        )

        # Return the response
        return ImageGenerationResponse(
            data=image_data
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.post("/edit", response_model=ImageGenerationResponse)
async def edit_image(
    image: UploadFile = File(...),
    prompt: str = Form(...),
    model: str = Form("dall-e-2"),
    size: str = Form("1024x1024"),
    n: int = Form(1),
    current_user = Depends(get_device_user)
):
    """Edit an image using AI"""

    try:
        # Read the image file
        image_data = await image.read()

        # Edit the image
        image_data_result = await ImageService.edit_image(
            image_data=image_data,
            prompt=prompt,
            model=model,
            size=size,
            n=n
        )

        # Return the response
        return ImageGenerationResponse(
            data=image_data_result
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
