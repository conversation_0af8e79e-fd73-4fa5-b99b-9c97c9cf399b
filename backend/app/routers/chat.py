from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import StreamingResponse
from typing import List, Optional, Dict
import json

from ..models import (
    ChatCreate, ChatResponse, MessageCreate, MessageResponse,
    ChatMessage, ConversationRequest, ConversationMessage
)
from ..services.chat_service import ChatService
from ..services.enhanced_chat_service import EnhancedChatService
from ..services.deepseek_service import DeepSeekService
from ..services.claude_service import ClaudeService
from ..services.gemini_service import GeminiService
from ..services.chat_history_service import ChatHistoryService
from ..database import get_db
from sqlalchemy.orm import Session
from .auth import get_device_user, Device<PERSON>ser
from .assistant import ASSISTANTS

router = APIRouter(
    prefix="/chat",
    tags=["chat"],
    responses={401: {"description": "Unauthorized"}},
)

@router.post("", response_model=ChatResponse)
async def create_new_chat(
    chat_data: ChatCreate,
    device_user: DeviceUser = Depends(get_device_user),
    db: Session = Depends(get_db)
):
    """Create a new chat (device-based authentication)"""
    try:
        chat = ChatHistoryService.create_chat(
            db=db,
            chat_data=chat_data,
            user_id=device_user.id,
            device_id=device_user.device_id
        )
        return chat
    except Exception as e:
        print(f"Error creating chat: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to create chat")

@router.get("", response_model=List[ChatResponse])
async def get_user_chats(
    device_user: DeviceUser = Depends(get_device_user),
    db: Session = Depends(get_db)
):
    """Get all chats for the current user (device-based authentication)"""
    try:
        chats = ChatHistoryService.get_user_chats(
            db=db,
            user_id=device_user.id,
            device_id=device_user.device_id
        )
        return chats
    except Exception as e:
        print(f"Error getting user chats: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get chats")

@router.get("/{chat_id}", response_model=ChatResponse)
async def get_chat(
    chat_id: str,
    device_user: DeviceUser = Depends(get_device_user),
    db: Session = Depends(get_db)
):
    """Get a specific chat with messages (device-based authentication)"""
    try:
        chat = ChatHistoryService.get_chat_with_messages(
            db=db,
            chat_id=chat_id,
            user_id=device_user.id,
            device_id=device_user.device_id
        )

        if not chat:
            raise HTTPException(status_code=404, detail="Chat not found")

        return chat
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error getting chat: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get chat")

@router.post("/{chat_id}/messages", response_model=MessageResponse)
async def add_chat_message(
    chat_id: str,
    message_data: MessageCreate,
    device_user: DeviceUser = Depends(get_device_user),
    db: Session = Depends(get_db)
):
    """Add a message to a chat and get AI response (device-based authentication)"""
    import time

    try:
        # First, save the user message to the database
        user_message = ChatHistoryService.add_message(
            db=db,
            chat_id=chat_id,
            content=message_data.content,
            is_user=True,
            user_id=device_user.id,
            device_id=device_user.device_id
        )

        if not user_message:
            raise HTTPException(status_code=404, detail="Chat not found")

        # Get chat history for AI context
        message_history = ChatHistoryService.get_chat_history_for_ai(
            db=db,
            chat_id=chat_id,
            user_id=device_user.id,
            device_id=device_user.device_id,
            limit=20
        )

        # Check if user has pro subscription for certain models
        is_pro = device_user.is_pro

        # Get chat details to determine assistant
        chat = ChatHistoryService.get_chat_with_messages(
            db=db,
            chat_id=chat_id,
            user_id=device_user.id,
            device_id=device_user.device_id
        )

        assistant_id = chat.get("assistant_id", "GPT-4o mini") if chat else "GPT-4o mini"

        # Determine model based on subscription
        model = "gpt-4o" if is_pro else "gpt-4o-mini"

        # Get the system message for this assistant
        system_message = None
        for assistant in ASSISTANTS:
            if assistant.name == assistant_id:
                system_message = assistant.system_message
                break

        # Convert to ChatMessage format
        api_messages = ChatService.convert_messages(message_history, assistant_id, system_message)

        start_time = time.time()

        # Determine which service to use based on the model
        if model.startswith("deepseek"):
            ai_response = await DeepSeekService.generate_response(api_messages, model)
        elif model.startswith("claude"):
            ai_response = await ClaudeService.generate_response(api_messages, model)
        elif model.startswith("gemini"):
            ai_response = await GeminiService.generate_response(api_messages, model)
        else:
            ai_response = await ChatService.generate_response(api_messages, model)

        response_time = time.time() - start_time

        # Save the AI response to the database
        ai_message = ChatHistoryService.add_message(
            db=db,
            chat_id=chat_id,
            content=ai_response,
            is_user=False,
            user_id=device_user.id,
            device_id=device_user.device_id,
            model_used=model,
            response_time=response_time
        )

        return ai_message

    except HTTPException:
        raise
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"Error in add_chat_message: {str(e)}")
        print(f"Full traceback: {error_details}")
        raise HTTPException(status_code=500, detail=f"Failed to process message: {str(e)}")

@router.get("/{chat_id}/messages", response_model=List[MessageResponse])
async def get_chat_messages(
    chat_id: str,
    device_user: DeviceUser = Depends(get_device_user),
    db: Session = Depends(get_db)
):
    """Get all messages for a chat (device-based authentication)"""
    try:
        messages = ChatHistoryService.get_chat_messages(
            db=db,
            chat_id=chat_id,
            user_id=device_user.id,
            device_id=device_user.device_id
        )
        return messages
    except Exception as e:
        print(f"Error getting chat messages: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get messages")

@router.post("/stream")
async def stream_chat_response(
    conversation_data: ConversationRequest,
    model_name: str = "GPT-4.1 nano",  # Default to GPT-4.1 nano
    device_user: DeviceUser = Depends(get_device_user),
    db: Session = Depends(get_db)
):
    """Stream AI response for a conversation (device-based authentication)"""

    # Check if user has pro subscription (default to free for device users)
    is_pro = device_user.is_pro

    # Get the actual model ID from the display name
    from .ai_model import MODEL_IDS
    model = MODEL_IDS.get(model_name, "gpt-4.1-nano-2025-04-14")  # Default to GPT-4.1 nano if not found

    # For now, use the selected model as assistant_id
    assistant_id = model_name

    # Get the system message for this assistant
    system_message = None
    for assistant in ASSISTANTS:
        if assistant.name == assistant_id:
            system_message = assistant.system_message
            break

    # Convert conversation messages to the expected format
    message_history = [{"content": msg.content, "is_user": msg.is_user} for msg in conversation_data.messages]

    # Convert to ChatMessage format
    api_messages = ChatService.convert_messages(message_history, assistant_id, system_message)

    # Initialize enhanced chat service for memory integration
    enhanced_chat_service = EnhancedChatService(db)

    async def generate_stream():
        try:
            print(f"Starting stream for model: {model}")
            chunk_count = 0

            # Send initial connection confirmation
            json_data = json.dumps({'type': 'start', 'message': 'Stream started'}, ensure_ascii=False)
            yield f"data: {json_data}\n\n"

            # Determine which service to use based on the model
            if model.startswith("deepseek"):
                # Use DeepSeek service
                print(f"Using DeepSeek service for model: {model}")
                async for chunk in DeepSeekService.generate_streaming_response(api_messages, model):
                    if chunk and chunk.strip():  # Only send non-empty chunks
                        chunk_count += 1
                        print(f"DeepSeek chunk {chunk_count}: '{chunk}'")
                        json_data = json.dumps({'type': 'token', 'content': chunk}, ensure_ascii=False)
                        yield f"data: {json_data}\n\n"
            elif model.startswith("claude"):
                # Use Claude service
                print(f"Using Claude service for model: {model}")
                async for chunk in ClaudeService.generate_streaming_response(api_messages, model):
                    if chunk and chunk.strip():  # Only send non-empty chunks
                        chunk_count += 1
                        print(f"Claude chunk {chunk_count}: '{chunk}'")
                        json_data = json.dumps({'type': 'token', 'content': chunk}, ensure_ascii=False)
                        yield f"data: {json_data}\n\n"
            elif model.startswith("gemini"):
                # Use Gemini service
                print(f"Using Gemini service for model: {model}")
                async for chunk in GeminiService.generate_streaming_response(api_messages, model):
                    if chunk and chunk.strip():  # Only send non-empty chunks
                        chunk_count += 1
                        print(f"Gemini chunk {chunk_count}: '{chunk}'")
                        json_data = json.dumps({'type': 'token', 'content': chunk}, ensure_ascii=False)
                        yield f"data: {json_data}\n\n"
            else:
                # Use Enhanced OpenAI service with memory integration (default for all OpenAI models)
                print(f"Using Enhanced OpenAI service with memory for model: {model}")
                async for chunk in enhanced_chat_service.generate_personalized_streaming_response(
                    user_id=device_user.id,
                    device_id=device_user.device_id,
                    messages=api_messages,
                    model=model,
                    assistant_id=assistant_id,
                    extract_memories=True
                ):
                    if chunk and chunk.strip():  # Only send non-empty chunks
                        chunk_count += 1
                        print(f"Enhanced OpenAI chunk {chunk_count}: '{chunk}'")
                        # Ensure proper JSON encoding with ensure_ascii=False for better Unicode support
                        json_data = json.dumps({'type': 'token', 'content': chunk}, ensure_ascii=False)
                        yield f"data: {json_data}\n\n"

            # Send completion signal
            print(f"Stream completed with {chunk_count} chunks")
            json_data = json.dumps({'type': 'done', 'message': 'Stream completed'}, ensure_ascii=False)
            yield f"data: {json_data}\n\n"

        except Exception as e:
            # Send error message
            error_msg = f"Error generating response: {str(e)}"
            print(f"Stream error: {error_msg}")
            json_data = json.dumps({'type': 'error', 'error': error_msg}, ensure_ascii=False)
            yield f"data: {json_data}\n\n"

    return StreamingResponse(
        generate_stream(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
            "Access-Control-Allow-Headers": "Content-Type, Authorization, X-Device-ID, X-User-ID"
        }
    )

@router.post("/stream-single")
async def stream_chat_response_single(
    message_data: MessageCreate,
    model_name: str = "GPT-4.1 nano",  # Default to GPT-4.1 nano
    device_user: DeviceUser = Depends(get_device_user)
):
    """Stream AI response for a single message (backward compatibility)"""

    # Convert single message to conversation format
    conversation_message = ConversationMessage(
        content=message_data.content,
        is_user=message_data.is_user
    )
    conversation_data = ConversationRequest(messages=[conversation_message])

    # Call the main streaming endpoint
    return await stream_chat_response(conversation_data, model_name, device_user)
