from fastapi import APIRouter, HTTPException, status, Depends
from sqlalchemy.orm import Session
from typing import List, Optional
from app.database import get_db
from app.routers.auth import get_device_user, DeviceUser
from app.services.memory_service import MemoryService, MemoryExtractionService
from app.models import (
    MemoryCreate, MemoryUpdate, MemoryResponse, MemorySearchRequest, 
    MemorySearchResponse, MemoryExtractRequest, MemoryExtractionResponse,
    MemoryStatsResponse, MemoryPreferencesUpdate, MemoryPreferencesResponse
)

router = APIRouter(
    prefix="/memory",
    tags=["memory"],
    responses={401: {"description": "Unauthorized"}},
)

@router.post("/create", response_model=MemoryResponse)
async def create_memory(
    memory_data: MemoryCreate,
    device_user: DeviceUser = Depends(get_device_user),
    db: Session = Depends(get_db)
):
    """Create a new memory for the user"""
    memory_service = MemoryService(db)
    try:
        return memory_service.create_memory(device_user.id, device_user.device_id, memory_data)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create memory: {str(e)}"
        )

@router.get("/{memory_id}", response_model=MemoryResponse)
async def get_memory(
    memory_id: str,
    device_user: DeviceUser = Depends(get_device_user),
    db: Session = Depends(get_db)
):
    """Get a specific memory by ID"""
    memory_service = MemoryService(db)
    memory = memory_service.get_memory(device_user.id, memory_id)
    
    if not memory:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Memory not found"
        )
    
    return memory

@router.put("/{memory_id}", response_model=MemoryResponse)
async def update_memory(
    memory_id: str,
    update_data: MemoryUpdate,
    device_user: DeviceUser = Depends(get_device_user),
    db: Session = Depends(get_db)
):
    """Update an existing memory"""
    memory_service = MemoryService(db)
    memory = memory_service.update_memory(device_user.id, memory_id, update_data)
    
    if not memory:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Memory not found"
        )
    
    return memory

@router.delete("/{memory_id}")
async def delete_memory(
    memory_id: str,
    device_user: DeviceUser = Depends(get_device_user),
    db: Session = Depends(get_db)
):
    """Delete a memory"""
    memory_service = MemoryService(db)
    success = memory_service.delete_memory(device_user.id, memory_id)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Memory not found"
        )
    
    return {"message": "Memory deleted successfully"}

@router.post("/search", response_model=MemorySearchResponse)
async def search_memories(
    search_request: MemorySearchRequest,
    device_user: DeviceUser = Depends(get_device_user),
    db: Session = Depends(get_db)
):
    """Search memories with filters"""
    memory_service = MemoryService(db)
    result = memory_service.search_memories(device_user.id, search_request)
    
    return MemorySearchResponse(
        memories=result["memories"],
        total_count=result["total_count"],
        query_used=result["query_used"]
    )

@router.get("/relevant/context")
async def get_relevant_memories(
    context: str,
    limit: int = 10,
    device_user: DeviceUser = Depends(get_device_user),
    db: Session = Depends(get_db)
):
    """Get memories relevant to a conversation context"""
    memory_service = MemoryService(db)
    memories = memory_service.get_relevant_memories(device_user.id, context, limit)
    
    return {"memories": memories, "context_used": context}

@router.post("/extract", response_model=MemoryExtractionResponse)
async def extract_memories(
    extract_request: MemoryExtractRequest,
    device_user: DeviceUser = Depends(get_device_user),
    db: Session = Depends(get_db)
):
    """Extract memories from conversation text using AI"""
    extraction_service = MemoryExtractionService(db)
    
    try:
        result = await extraction_service.extract_memories_from_conversation(
            user_id=device_user.id,
            device_id=device_user.device_id,
            conversation_text=extract_request.conversation_text,
            context=extract_request.context,
            assistant_id=extract_request.assistant_id
        )
        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to extract memories: {str(e)}"
        )

@router.get("/stats", response_model=MemoryStatsResponse)
async def get_memory_stats(
    device_user: DeviceUser = Depends(get_device_user),
    db: Session = Depends(get_db)
):
    """Get memory statistics and analytics"""
    from app.database import UserMemory
    from sqlalchemy import func, desc
    from datetime import datetime, timedelta
    
    # Get total memories
    total_memories = db.query(UserMemory).filter(UserMemory.user_id == device_user.id).count()
    
    # Get memories by type
    memories_by_type = {}
    type_counts = db.query(
        UserMemory.memory_type, 
        func.count(UserMemory.id)
    ).filter(
        UserMemory.user_id == device_user.id
    ).group_by(UserMemory.memory_type).all()
    
    for memory_type, count in type_counts:
        memories_by_type[memory_type] = count
    
    # Get most accessed memories
    most_accessed = db.query(UserMemory).filter(
        UserMemory.user_id == device_user.id
    ).order_by(desc(UserMemory.access_count)).limit(5).all()
    
    # Get recent memories
    recent_memories = db.query(UserMemory).filter(
        UserMemory.user_id == device_user.id
    ).order_by(desc(UserMemory.created_at)).limit(5).all()
    
    # Get memory growth trend (last 30 days)
    thirty_days_ago = datetime.utcnow() - timedelta(days=30)
    growth_data = []
    for i in range(30):
        date = thirty_days_ago + timedelta(days=i)
        count = db.query(UserMemory).filter(
            UserMemory.user_id == device_user.id,
            func.date(UserMemory.created_at) == date.date()
        ).count()
        growth_data.append({
            "date": date.strftime("%Y-%m-%d"),
            "count": count
        })
    
    memory_service = MemoryService(db)
    
    return MemoryStatsResponse(
        total_memories=total_memories,
        memories_by_type=memories_by_type,
        most_accessed_memories=[memory_service._memory_to_response(m) for m in most_accessed],
        recent_memories=[memory_service._memory_to_response(m) for m in recent_memories],
        memory_growth_trend=growth_data
    )

@router.get("/list")
async def list_memories(
    memory_type: Optional[str] = None,
    category: Optional[str] = None,
    limit: int = 50,
    offset: int = 0,
    device_user: DeviceUser = Depends(get_device_user),
    db: Session = Depends(get_db)
):
    """List memories with pagination"""
    from app.database import UserMemory
    from sqlalchemy import desc
    
    query = db.query(UserMemory).filter(UserMemory.user_id == device_user.id)
    
    if memory_type:
        query = query.filter(UserMemory.memory_type == memory_type)
    
    if category:
        query = query.filter(UserMemory.category == category)
    
    total_count = query.count()
    memories = query.order_by(
        desc(UserMemory.importance_score),
        desc(UserMemory.updated_at)
    ).offset(offset).limit(limit).all()
    
    memory_service = MemoryService(db)
    
    return {
        "memories": [memory_service._memory_to_response(m) for m in memories],
        "total_count": total_count,
        "limit": limit,
        "offset": offset
    }

@router.get("/categories")
async def get_memory_categories(
    device_user: DeviceUser = Depends(get_device_user),
    db: Session = Depends(get_db)
):
    """Get all memory categories for the user"""
    from app.database import UserMemory
    from sqlalchemy import distinct
    
    categories = db.query(distinct(UserMemory.category)).filter(
        UserMemory.user_id == device_user.id,
        UserMemory.category.isnot(None)
    ).all()
    
    return {"categories": [cat[0] for cat in categories if cat[0]]}

@router.post("/bulk-delete")
async def bulk_delete_memories(
    memory_ids: List[str],
    device_user: DeviceUser = Depends(get_device_user),
    db: Session = Depends(get_db)
):
    """Delete multiple memories at once"""
    memory_service = MemoryService(db)
    deleted_count = 0
    
    for memory_id in memory_ids:
        if memory_service.delete_memory(device_user.id, memory_id):
            deleted_count += 1
    
    return {
        "message": f"Deleted {deleted_count} out of {len(memory_ids)} memories",
        "deleted_count": deleted_count,
        "total_requested": len(memory_ids)
    }
