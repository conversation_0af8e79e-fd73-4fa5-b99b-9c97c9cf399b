from fastapi import APIRouter, Depends, HTTPException, status
from typing import List, Optional

from ..models import AIModel, AIModelResponse
from .auth import get_device_user, DeviceUser

router = APIRouter(
    prefix="/ai-model",
    tags=["ai-model"],
)

# Define all AI models with your preferred names (Latest 2025 - Based on Internet Research)
AI_MODELS = [
    # OpenAI Models (Latest 2025)
    AIModel(name="GPT-4.1 nano", is_pro=False, icon_name="chatgpt"),
    AIModel(name="GPT-4.1 mini", is_pro=True, icon_name="chatgpt"),
    AIModel(name="GPT-4o", is_pro=True, icon_name="chatgpt"),
    AIModel(name="GPT-4o mini", is_pro=True, icon_name="chatgpt"),
    AIModel(name="o4-mini", is_pro=True, icon_name="chatgpt"),

    # DeepSeek Model (Latest 2025)
    AIModel(name="DeepSeek", is_pro=True, icon_name="deepseek"),

    # Claude Models (Latest 2025)
    AIModel(name="Claude Sonnet 4", is_pro=True, icon_name="claude"),

    # Gemini Models (Latest 2025)
    AIModel(name="Gemini 2.0 Flash", is_pro=True, icon_name="gemini"),
]

# Map display names to actual model IDs (Latest 2025 - Based on Internet Research)
MODEL_IDS = {
    # OpenAI Models (Latest 2025 - Using confirmed available models)
    "GPT-4.1 nano": "gpt-4o-mini",  # Map to available model (GPT-4.1 may not be available yet)
    "GPT-4.1 mini": "gpt-4o-mini",  # Map to available model
    "GPT-4o": "gpt-4o",  # Latest GPT-4o model
    "GPT-4o mini": "gpt-4o-mini",  # Latest GPT-4o mini model
    "o4-mini": "o1-mini",  # Map to o1-mini until o4-mini is fully available in API


    # DeepSeek Model (Latest 2025)
    "DeepSeek": "deepseek-reasoner",  # Map to DeepSeek R1 model (latest)

    # Claude Models (Latest 2025)
    "Claude Sonnet 4": "claude-sonnet-4-20250514",  # Map to latest Claude Sonnet 4

    # Gemini Models (Latest 2025)
    "Gemini 2.0 Flash": "gemini-2.0-flash",  # Latest Gemini 2.0 Flash
}

@router.get("/models", response_model=AIModelResponse)
async def get_ai_models():
    """Get all available AI models"""
    return AIModelResponse(models=AI_MODELS)

@router.get("/model-id/{model_name}")
async def get_model_id(model_name: str):
    """Get the actual model ID for a display name"""
    if model_name in MODEL_IDS:
        return {"model_id": MODEL_IDS[model_name]}
    else:
        # Default to gpt-4.1-nano-2025-04-14 if not found
        return {"model_id": "gpt-4.1-nano-2025-04-14"}
