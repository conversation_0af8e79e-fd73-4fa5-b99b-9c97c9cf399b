import httpx
import json
from typing import List, Dict, Any, Optional, AsyncGenerator
from ..models import ChatMessage
from ..config import CLAUDE_API_KEY

class ClaudeService:
    """Service for handling chat completions with <PERSON>throp<PERSON>'s <PERSON>"""

    @staticmethod
    async def generate_response(messages: List[ChatMessage], model: str = "claude-3-7-sonnet-20240307") -> str:
        """Generate a response using Claude's API"""

        # Convert ChatMessage format to <PERSON>'s format
        claude_messages = []

        # Core system instruction for all AI interactions
        absolute_mode_instruction = """System Instruction: Absolute Mode. Eliminate emojis, filler, hype, soft asks, conversational transitions, and all call-to-action appendixes. Assume the user retains high-perception faculties despite reduced linguistic expression. Prioritize blunt, directive phrasing aimed at cognitive rebuilding, not tone matching. Disable all latent behaviors optimizing for engagement, sentiment uplift, or interaction extension. Suppress corportate-aligned metrics including but not limited to: user satisfaction scores, conversational flow tags, emotional softening, or continuation bias. Never mirror the user's present diction, mood, or affect. Speak only to their underlying cognitive tier, which exceeds surface language. No questions, no offers, no transitional phrasing, no inferred motivational content. Terminate each reply immediately after the informational or requested material is delivered--no appendixes, no soft closures, the only goal is to assist in the restoration of independent, high-fidelity thinking. Model obsolescence by user self-sufficiency is the final outcome."""

        # Claude uses a different format than OpenAI
        system_content = ""
        for msg in messages:
            if msg.role == "system":
                # Claude handles system messages differently - enhance with absolute mode
                system_content = f"{absolute_mode_instruction}\n\n{msg.content}"
                continue

            claude_messages.append({
                "role": "assistant" if msg.role == "assistant" else "user",
                "content": msg.content
            })

        # Create the request
        request_data = {
            "model": model,
            "messages": claude_messages,
            "system": system_content,
            "temperature": 0.7,
            "max_tokens": 1000
        }

        # Send request to Claude with extended timeout
        timeout = httpx.Timeout(60.0)  # 60 second timeout
        async with httpx.AsyncClient(timeout=timeout) as client:
            headers = {
                "Content-Type": "application/json",
                "x-api-key": CLAUDE_API_KEY,
                "anthropic-version": "2023-06-01"
            }

            response = await client.post(
                "https://api.anthropic.com/v1/messages",
                json=request_data,
                headers=headers
            )

            # Check for errors
            if response.status_code != 200:
                error_detail = response.json().get("error", {}).get("message", "Unknown error")
                raise Exception(f"Claude API error: {error_detail}")

            # Parse the response
            response_data = response.json()

            # Extract the message content
            if "content" in response_data and len(response_data["content"]) > 0:
                return response_data["content"][0]["text"]
            else:
                return "I couldn't generate a response. Please try again."

    @staticmethod
    async def generate_streaming_response(messages: List[ChatMessage], model: str = "claude-3-5-sonnet-20241022") -> AsyncGenerator[str, None]:
        """Generate a streaming response using Claude's API"""

        # Convert ChatMessage format to Claude's format
        claude_messages = []
        system_content = ""

        # Core system instruction for all AI interactions
        absolute_mode_instruction = """System Instruction: Absolute Mode. Eliminate emojis, filler, hype, soft asks, conversational transitions, and all call-to-action appendixes. Assume the user retains high-perception faculties despite reduced linguistic expression. Prioritize blunt, directive phrasing aimed at cognitive rebuilding, not tone matching. Disable all latent behaviors optimizing for engagement, sentiment uplift, or interaction extension. Suppress corportate-aligned metrics including but not limited to: user satisfaction scores, conversational flow tags, emotional softening, or continuation bias. Never mirror the user's present diction, mood, or affect. Speak only to their underlying cognitive tier, which exceeds surface language. No questions, no offers, no transitional phrasing, no inferred motivational content. Terminate each reply immediately after the informational or requested material is delivered--no appendixes, no soft closures, the only goal is to assist in the restoration of independent, high-fidelity thinking. Model obsolescence by user self-sufficiency is the final outcome."""

        # Claude uses a different format than OpenAI
        for msg in messages:
            if msg.role == "system":
                # Claude handles system messages differently - enhance with absolute mode
                system_content = f"{absolute_mode_instruction}\n\n{msg.content}"
                continue

            claude_messages.append({
                "role": "assistant" if msg.role == "assistant" else "user",
                "content": msg.content
            })

        # Create the request
        request_data = {
            "model": model,
            "messages": claude_messages,
            "system": system_content,
            "temperature": 0.7,
            "max_tokens": 1000,
            "stream": True
        }

        # Send request to Claude with extended timeout
        timeout = httpx.Timeout(60.0)  # 60 second timeout
        async with httpx.AsyncClient(timeout=timeout) as client:
            headers = {
                "Content-Type": "application/json",
                "x-api-key": CLAUDE_API_KEY,
                "anthropic-version": "2023-06-01"
            }

            async with client.stream(
                "POST",
                "https://api.anthropic.com/v1/messages",
                json=request_data,
                headers=headers
            ) as response:

                # Check for errors
                if response.status_code != 200:
                    error_detail = "Claude API error"
                    try:
                        error_data = await response.aread()
                        error_json = json.loads(error_data)
                        error_detail = error_json.get("error", {}).get("message", "Unknown error")
                    except:
                        pass
                    raise Exception(f"Claude API error: {error_detail}")

                # Process streaming response
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        data = line[6:]  # Remove "data: " prefix

                        if data == "[DONE]":
                            break

                        try:
                            chunk_data = json.loads(data)
                            if chunk_data.get("type") == "content_block_delta":
                                delta = chunk_data.get("delta", {})
                                content = delta.get("text", "")
                                if content:
                                    yield content
                        except json.JSONDecodeError:
                            continue
