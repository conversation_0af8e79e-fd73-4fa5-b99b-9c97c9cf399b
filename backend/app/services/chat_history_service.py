"""
Chat History Service for managing chat sessions and messages in the database.
"""

from sqlalchemy.orm import Session
from sqlalchemy import desc, and_
from typing import List, Optional, Dict, Any
from datetime import datetime
import uuid

from ..database import Chat, Message
from ..models import ChatCreate, ChatResponse, MessageCreate, MessageResponse


class ChatHistoryService:
    """Service for managing chat history in the database"""
    
    @staticmethod
    def create_chat(db: Session, chat_data: ChatCreate, user_id: str, device_id: str) -> Dict[str, Any]:
        """Create a new chat session"""
        chat = Chat(
            id=str(uuid.uuid4()),
            user_id=user_id,
            device_id=device_id,
            assistant_id=chat_data.assistant_id,
            title=chat_data.title or "New Chat"
        )
        
        db.add(chat)
        db.commit()
        db.refresh(chat)
        
        return {
            "id": chat.id,
            "user_id": chat.user_id,
            "assistant_id": chat.assistant_id,
            "title": chat.title,
            "created_at": chat.created_at,
            "messages": []
        }
    
    @staticmethod
    def get_user_chats(db: Session, user_id: str, device_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Get all chats for a user, ordered by most recent"""
        chats = db.query(Chat).filter(
            and_(Chat.user_id == user_id, Chat.device_id == device_id)
        ).order_by(desc(Chat.updated_at)).limit(limit).all()
        
        result = []
        for chat in chats:
            # Get the last message for preview
            last_message = db.query(Message).filter(
                Message.chat_id == chat.id
            ).order_by(desc(Message.created_at)).first()
            
            result.append({
                "id": chat.id,
                "user_id": chat.user_id,
                "assistant_id": chat.assistant_id,
                "title": chat.title,
                "created_at": chat.created_at,
                "updated_at": chat.updated_at,
                "last_message": {
                    "content": last_message.content[:100] + "..." if last_message and len(last_message.content) > 100 else last_message.content if last_message else None,
                    "is_user": last_message.is_user if last_message else None,
                    "created_at": last_message.created_at if last_message else None
                } if last_message else None,
                "messages": []  # Don't load all messages for list view
            })
        
        return result
    
    @staticmethod
    def get_chat_with_messages(db: Session, chat_id: str, user_id: str, device_id: str) -> Optional[Dict[str, Any]]:
        """Get a specific chat with all its messages"""
        chat = db.query(Chat).filter(
            and_(
                Chat.id == chat_id,
                Chat.user_id == user_id,
                Chat.device_id == device_id
            )
        ).first()
        
        if not chat:
            return None
        
        # Get all messages for this chat
        messages = db.query(Message).filter(
            Message.chat_id == chat_id
        ).order_by(Message.created_at).all()
        
        message_list = []
        for msg in messages:
            message_list.append({
                "id": msg.id,
                "content": msg.content,
                "is_user": msg.is_user,
                "created_at": msg.created_at,
                "model_used": msg.model_used,
                "tokens_used": msg.tokens_used,
                "response_time": msg.response_time
            })
        
        return {
            "id": chat.id,
            "user_id": chat.user_id,
            "assistant_id": chat.assistant_id,
            "title": chat.title,
            "created_at": chat.created_at,
            "updated_at": chat.updated_at,
            "messages": message_list
        }
    
    @staticmethod
    def add_message(
        db: Session, 
        chat_id: str, 
        content: str, 
        is_user: bool, 
        user_id: str, 
        device_id: str,
        model_used: Optional[str] = None,
        tokens_used: Optional[int] = None,
        response_time: Optional[float] = None
    ) -> Optional[Dict[str, Any]]:
        """Add a message to a chat"""
        
        # Verify chat exists and belongs to user
        chat = db.query(Chat).filter(
            and_(
                Chat.id == chat_id,
                Chat.user_id == user_id,
                Chat.device_id == device_id
            )
        ).first()
        
        if not chat:
            return None
        
        # Create the message
        message = Message(
            id=str(uuid.uuid4()),
            chat_id=chat_id,
            content=content,
            is_user=is_user,
            model_used=model_used,
            tokens_used=tokens_used,
            response_time=response_time
        )
        
        db.add(message)
        
        # Update chat's updated_at timestamp
        chat.updated_at = datetime.utcnow()
        
        db.commit()
        db.refresh(message)
        
        return {
            "id": message.id,
            "content": message.content,
            "is_user": message.is_user,
            "created_at": message.created_at,
            "model_used": message.model_used,
            "tokens_used": message.tokens_used,
            "response_time": message.response_time
        }
    
    @staticmethod
    def get_chat_messages(db: Session, chat_id: str, user_id: str, device_id: str) -> List[Dict[str, Any]]:
        """Get all messages for a chat"""
        # Verify chat exists and belongs to user
        chat = db.query(Chat).filter(
            and_(
                Chat.id == chat_id,
                Chat.user_id == user_id,
                Chat.device_id == device_id
            )
        ).first()
        
        if not chat:
            return []
        
        messages = db.query(Message).filter(
            Message.chat_id == chat_id
        ).order_by(Message.created_at).all()
        
        result = []
        for msg in messages:
            result.append({
                "id": msg.id,
                "content": msg.content,
                "is_user": msg.is_user,
                "created_at": msg.created_at,
                "model_used": msg.model_used,
                "tokens_used": msg.tokens_used,
                "response_time": msg.response_time
            })
        
        return result
    
    @staticmethod
    def update_chat_title(db: Session, chat_id: str, user_id: str, device_id: str, title: str) -> bool:
        """Update a chat's title"""
        chat = db.query(Chat).filter(
            and_(
                Chat.id == chat_id,
                Chat.user_id == user_id,
                Chat.device_id == device_id
            )
        ).first()
        
        if not chat:
            return False
        
        chat.title = title
        chat.updated_at = datetime.utcnow()
        db.commit()
        
        return True
    
    @staticmethod
    def delete_chat(db: Session, chat_id: str, user_id: str, device_id: str) -> bool:
        """Delete a chat and all its messages"""
        chat = db.query(Chat).filter(
            and_(
                Chat.id == chat_id,
                Chat.user_id == user_id,
                Chat.device_id == device_id
            )
        ).first()
        
        if not chat:
            return False
        
        # Delete all messages first (cascade should handle this, but being explicit)
        db.query(Message).filter(Message.chat_id == chat_id).delete()
        
        # Delete the chat
        db.delete(chat)
        db.commit()
        
        return True
    
    @staticmethod
    def get_chat_history_for_ai(db: Session, chat_id: str, user_id: str, device_id: str, limit: int = 20) -> List[Dict[str, Any]]:
        """Get recent chat history formatted for AI context"""
        # Verify chat exists and belongs to user
        chat = db.query(Chat).filter(
            and_(
                Chat.id == chat_id,
                Chat.user_id == user_id,
                Chat.device_id == device_id
            )
        ).first()
        
        if not chat:
            return []
        
        # Get recent messages
        messages = db.query(Message).filter(
            Message.chat_id == chat_id
        ).order_by(desc(Message.created_at)).limit(limit).all()
        
        # Reverse to get chronological order
        messages = list(reversed(messages))
        
        result = []
        for msg in messages:
            result.append({
                "content": msg.content,
                "is_user": msg.is_user
            })
        
        return result
