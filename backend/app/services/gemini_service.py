import httpx
import json
from typing import List, Dict, Any, Optional, AsyncGenerator
from ..models import ChatMessage
from ..config import GEMINI_API_KEY

class GeminiService:
    """Service for handling chat completions with Google's Gemini API"""

    @staticmethod
    async def generate_response(messages: List[ChatMessage], model: str = "gemini-2.0-flash") -> str:
        """Generate a response using Gemini's API"""

        # Convert ChatMessage format to Gemini's format
        gemini_messages = []

        # Gemini uses a different format than OpenAI
        for msg in messages:
            if msg.role == "system":
                # Add system message as a user message at the beginning
                gemini_messages.append({
                    "role": "user",
                    "parts": [{"text": f"System instruction: {msg.content}"}]
                })
                # Add a response acknowledging the system instruction
                gemini_messages.append({
                    "role": "model",
                    "parts": [{"text": "I'll follow these instructions."}]
                })
            else:
                gemini_messages.append({
                    "role": "model" if msg.role == "assistant" else "user",
                    "parts": [{"text": msg.content}]
                })

        # Create the request
        request_data = {
            "contents": gemini_messages,
            "generationConfig": {
                "temperature": 0.7,
                "maxOutputTokens": 1000,
                "topP": 0.95,
                "topK": 40
            }
        }

        # Send request to Gemini with extended timeout
        timeout = httpx.Timeout(60.0)  # 60 second timeout
        async with httpx.AsyncClient(timeout=timeout) as client:
            headers = {
                "Content-Type": "application/json"
            }

            # Gemini API uses API key in the URL
            url = f"https://generativelanguage.googleapis.com/v1/models/{model}:generateContent?key={GEMINI_API_KEY}"

            response = await client.post(
                url,
                json=request_data,
                headers=headers
            )

            # Check for errors
            if response.status_code != 200:
                error_detail = response.json().get("error", {}).get("message", "Unknown error")
                raise Exception(f"Gemini API error: {error_detail}")

            # Parse the response
            response_data = response.json()

            # Extract the message content
            if "candidates" in response_data and len(response_data["candidates"]) > 0:
                candidate = response_data["candidates"][0]
                if "content" in candidate and "parts" in candidate["content"]:
                    return candidate["content"]["parts"][0]["text"]

            return "I couldn't generate a response. Please try again."

    @staticmethod
    async def generate_streaming_response(messages: List[ChatMessage], model: str = "gemini-2.0-flash-exp") -> AsyncGenerator[str, None]:
        """Generate a streaming response using Gemini's API"""

        # Convert ChatMessage format to Gemini's format
        gemini_messages = []

        # Gemini uses a different format than OpenAI
        for msg in messages:
            if msg.role == "system":
                # Add system message as a user message at the beginning
                gemini_messages.append({
                    "role": "user",
                    "parts": [{"text": f"System instruction: {msg.content}"}]
                })
                # Add a response acknowledging the system instruction
                gemini_messages.append({
                    "role": "model",
                    "parts": [{"text": "I'll follow these instructions."}]
                })
            else:
                gemini_messages.append({
                    "role": "model" if msg.role == "assistant" else "user",
                    "parts": [{"text": msg.content}]
                })

        # Create the request
        request_data = {
            "contents": gemini_messages,
            "generationConfig": {
                "temperature": 0.7,
                "maxOutputTokens": 1000,
                "topP": 0.95,
                "topK": 40
            }
        }

        # Send request to Gemini with extended timeout
        timeout = httpx.Timeout(60.0)  # 60 second timeout
        async with httpx.AsyncClient(timeout=timeout) as client:
            headers = {
                "Content-Type": "application/json"
            }

            # Gemini API uses API key in the URL
            url = f"https://generativelanguage.googleapis.com/v1/models/{model}:streamGenerateContent?key={GEMINI_API_KEY}"

            async with client.stream(
                "POST",
                url,
                json=request_data,
                headers=headers
            ) as response:

                # Check for errors
                if response.status_code != 200:
                    error_detail = "Gemini API error"
                    try:
                        error_data = await response.aread()
                        error_json = json.loads(error_data)
                        error_detail = error_json.get("error", {}).get("message", "Unknown error")
                    except:
                        pass
                    raise Exception(f"Gemini API error: {error_detail}")

                # Process streaming response
                async for line in response.aiter_lines():
                    if line.strip():
                        try:
                            chunk_data = json.loads(line)
                            if "candidates" in chunk_data and len(chunk_data["candidates"]) > 0:
                                candidate = chunk_data["candidates"][0]
                                if "content" in candidate and "parts" in candidate["content"]:
                                    for part in candidate["content"]["parts"]:
                                        if "text" in part:
                                            yield part["text"]
                        except json.JSONDecodeError:
                            continue
