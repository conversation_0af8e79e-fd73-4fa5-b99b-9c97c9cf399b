import json
import re
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, func
from app.database import UserMemory, MemoryInteraction, ConversationContext, MemoryPreference
from app.models import (
    MemoryCreate, MemoryUpdate, MemoryResponse, MemorySearchRequest, 
    MemoryType, ExtractedMemory, MemoryExtractionResponse
)

class MemoryService:
    """Service for managing user memories and personalization"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_memory(self, user_id: str, device_id: str, memory_data: MemoryCreate) -> MemoryResponse:
        """Create a new memory for a user"""
        # Check if similar memory already exists
        existing = self.find_similar_memory(user_id, memory_data.title, memory_data.content)
        if existing:
            # Update existing memory instead of creating duplicate
            return self.update_memory(user_id, existing.id, MemoryUpdate(
                content=memory_data.content,
                importance_score=max(existing.importance_score, memory_data.importance_score),
                confidence_score=memory_data.confidence_score
            ))
        
        # Create new memory
        memory = UserMemory(
            user_id=user_id,
            device_id=device_id,
            memory_type=memory_data.memory_type.value,
            category=memory_data.category,
            title=memory_data.title,
            content=memory_data.content,
            keywords=memory_data.keywords,
            importance_score=memory_data.importance_score,
            confidence_score=memory_data.confidence_score
        )
        
        self.db.add(memory)
        self.db.commit()
        self.db.refresh(memory)
        
        # Log the creation
        self._log_interaction(memory.id, user_id, "created", f"Memory created: {memory.title}")
        
        return self._memory_to_response(memory)
    
    def get_memory(self, user_id: str, memory_id: str) -> Optional[MemoryResponse]:
        """Get a specific memory by ID"""
        memory = self.db.query(UserMemory).filter(
            and_(UserMemory.id == memory_id, UserMemory.user_id == user_id)
        ).first()
        
        if memory:
            # Update access tracking
            memory.access_count += 1
            memory.last_accessed = datetime.utcnow()
            self.db.commit()
            
            self._log_interaction(memory_id, user_id, "retrieved", "Memory accessed")
            return self._memory_to_response(memory)
        
        return None
    
    def update_memory(self, user_id: str, memory_id: str, update_data: MemoryUpdate) -> Optional[MemoryResponse]:
        """Update an existing memory"""
        memory = self.db.query(UserMemory).filter(
            and_(UserMemory.id == memory_id, UserMemory.user_id == user_id)
        ).first()
        
        if not memory:
            return None
        
        # Update fields
        if update_data.title is not None:
            memory.title = update_data.title
        if update_data.content is not None:
            memory.content = update_data.content
        if update_data.keywords is not None:
            memory.keywords = update_data.keywords
        if update_data.importance_score is not None:
            memory.importance_score = update_data.importance_score
        if update_data.confidence_score is not None:
            memory.confidence_score = update_data.confidence_score
        
        memory.updated_at = datetime.utcnow()
        self.db.commit()
        self.db.refresh(memory)
        
        self._log_interaction(memory_id, user_id, "updated", f"Memory updated: {memory.title}")
        
        return self._memory_to_response(memory)
    
    def delete_memory(self, user_id: str, memory_id: str) -> bool:
        """Delete a memory"""
        memory = self.db.query(UserMemory).filter(
            and_(UserMemory.id == memory_id, UserMemory.user_id == user_id)
        ).first()
        
        if memory:
            self.db.delete(memory)
            self.db.commit()
            return True
        
        return False
    
    def search_memories(self, user_id: str, search_request: MemorySearchRequest) -> Dict[str, Any]:
        """Search memories with various filters"""
        query = self.db.query(UserMemory).filter(UserMemory.user_id == user_id)
        
        # Apply filters
        if search_request.memory_types:
            type_values = [t.value for t in search_request.memory_types]
            query = query.filter(UserMemory.memory_type.in_(type_values))
        
        if search_request.categories:
            query = query.filter(UserMemory.category.in_(search_request.categories))
        
        if search_request.min_importance:
            query = query.filter(UserMemory.importance_score >= search_request.min_importance)
        
        # Text search
        if search_request.query:
            search_terms = search_request.query.lower().split()
            for term in search_terms:
                query = query.filter(
                    or_(
                        UserMemory.title.ilike(f"%{term}%"),
                        UserMemory.content.ilike(f"%{term}%"),
                        UserMemory.keywords.ilike(f"%{term}%")
                    )
                )
        
        # Get total count
        total_count = query.count()
        
        # Apply ordering and limit
        memories = query.order_by(
            desc(UserMemory.importance_score),
            desc(UserMemory.last_accessed),
            desc(UserMemory.updated_at)
        ).limit(search_request.limit).all()
        
        return {
            "memories": [self._memory_to_response(m) for m in memories],
            "total_count": total_count,
            "query_used": search_request.query
        }
    
    def get_relevant_memories(self, user_id: str, context: str, limit: int = 10) -> List[MemoryResponse]:
        """Get memories relevant to a conversation context"""
        # Extract keywords from context
        keywords = self._extract_keywords(context)
        
        if not keywords:
            # If no keywords, return most important recent memories
            memories = self.db.query(UserMemory).filter(
                UserMemory.user_id == user_id
            ).order_by(
                desc(UserMemory.importance_score),
                desc(UserMemory.last_accessed)
            ).limit(limit).all()
        else:
            # Search for memories matching keywords
            query = self.db.query(UserMemory).filter(UserMemory.user_id == user_id)
            
            # Build search conditions
            conditions = []
            for keyword in keywords:
                conditions.append(UserMemory.title.ilike(f"%{keyword}%"))
                conditions.append(UserMemory.content.ilike(f"%{keyword}%"))
                conditions.append(UserMemory.keywords.ilike(f"%{keyword}%"))
            
            if conditions:
                query = query.filter(or_(*conditions))
            
            memories = query.order_by(
                desc(UserMemory.importance_score),
                desc(UserMemory.access_count)
            ).limit(limit).all()
        
        # Update access tracking for retrieved memories
        for memory in memories:
            memory.access_count += 1
            memory.last_accessed = datetime.utcnow()
            self._log_interaction(memory.id, user_id, "retrieved", f"Retrieved for context: {context[:100]}")
        
        self.db.commit()
        
        return [self._memory_to_response(m) for m in memories]
    
    def find_similar_memory(self, user_id: str, title: str, content: str) -> Optional[UserMemory]:
        """Find similar existing memory to avoid duplicates"""
        # Simple similarity check based on title and content overlap
        memories = self.db.query(UserMemory).filter(UserMemory.user_id == user_id).all()
        
        for memory in memories:
            # Check title similarity
            if self._calculate_similarity(title.lower(), memory.title.lower()) > 0.8:
                return memory
            
            # Check content similarity
            if self._calculate_similarity(content.lower(), memory.content.lower()) > 0.7:
                return memory
        
        return None
    
    def _memory_to_response(self, memory: UserMemory) -> MemoryResponse:
        """Convert database memory to response model"""
        return MemoryResponse(
            id=memory.id,
            memory_type=memory.memory_type,
            category=memory.category,
            title=memory.title,
            content=memory.content,
            keywords=memory.keywords,
            importance_score=memory.importance_score,
            confidence_score=memory.confidence_score,
            access_count=memory.access_count,
            last_accessed=memory.last_accessed,
            created_at=memory.created_at,
            updated_at=memory.updated_at
        )
    
    def _log_interaction(self, memory_id: str, user_id: str, interaction_type: str, context: str):
        """Log memory interaction for analytics"""
        interaction = MemoryInteraction(
            memory_id=memory_id,
            user_id=user_id,
            interaction_type=interaction_type,
            context=context
        )
        self.db.add(interaction)
    
    def _extract_keywords(self, text: str) -> List[str]:
        """Extract relevant keywords from text"""
        # Simple keyword extraction - can be enhanced with NLP
        words = re.findall(r'\b\w+\b', text.lower())
        # Filter out common words
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them'}
        keywords = [word for word in words if len(word) > 2 and word not in stop_words]
        return list(set(keywords))[:10]  # Return unique keywords, max 10
    
    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """Calculate simple text similarity"""
        words1 = set(text1.split())
        words2 = set(text2.split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union) if union else 0.0

class MemoryExtractionService:
    """Service for extracting memories from conversations using AI"""

    def __init__(self, db: Session):
        self.db = db
        self.memory_service = MemoryService(db)

    async def extract_memories_from_conversation(
        self,
        user_id: str,
        device_id: str,
        conversation_text: str,
        context: Optional[str] = None,
        assistant_id: Optional[str] = None
    ) -> MemoryExtractionResponse:
        """Extract memories from a conversation using AI"""

        # Create the extraction prompt
        extraction_prompt = self._create_extraction_prompt(conversation_text, context)

        try:
            # Use OpenAI to extract memories (you can switch to other models)
            import openai
            import os

            client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

            response = await client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": extraction_prompt},
                    {"role": "user", "content": conversation_text}
                ],
                temperature=0.3,
                max_tokens=2000
            )

            # Parse the AI response
            ai_response = response.choices[0].message.content
            extracted_data = self._parse_extraction_response(ai_response)

            # Store extracted memories
            stored_memories = []
            for memory_data in extracted_data.get("memories", []):
                try:
                    memory_create = MemoryCreate(
                        memory_type=MemoryType(memory_data["memory_type"]),
                        category=memory_data.get("category"),
                        title=memory_data["title"],
                        content=memory_data["content"],
                        keywords=memory_data.get("keywords"),
                        importance_score=memory_data.get("importance_score", 0.5),
                        confidence_score=memory_data.get("confidence_score", 0.8)
                    )

                    stored_memory = self.memory_service.create_memory(user_id, device_id, memory_create)
                    stored_memories.append(ExtractedMemory(
                        memory_type=memory_create.memory_type,
                        category=memory_create.category,
                        title=memory_create.title,
                        content=memory_create.content,
                        keywords=memory_create.keywords,
                        importance_score=memory_create.importance_score,
                        confidence_score=memory_create.confidence_score,
                        reasoning=memory_data.get("reasoning", "Extracted from conversation")
                    ))
                except Exception as e:
                    print(f"Error storing extracted memory: {e}")
                    continue

            # Store conversation context
            self._store_conversation_context(
                user_id, device_id, conversation_text,
                extracted_data.get("summary"), assistant_id
            )

            return MemoryExtractionResponse(
                extracted_memories=stored_memories,
                conversation_summary=extracted_data.get("summary")
            )

        except Exception as e:
            print(f"Error extracting memories: {e}")
            return MemoryExtractionResponse(extracted_memories=[], conversation_summary=None)

    def _create_extraction_prompt(self, conversation_text: str, context: Optional[str] = None) -> str:
        """Create the prompt for memory extraction"""
        prompt = """You are a memory extraction AI. Your job is to analyze conversations and extract important information that should be remembered about the user for future personalized interactions.

Extract memories in the following categories:
1. PERSONAL_FACT: Basic facts about the user (name, age, location, job, family, etc.)
2. PREFERENCE: User's likes, dislikes, preferences, communication style
3. GOAL: User's goals, aspirations, projects they're working on
4. CONTEXT: Important context about their current situation or recent events
5. BEHAVIOR: Patterns in how they communicate or work
6. TECHNICAL: Technical skills, programming languages, tools they use

For each memory, provide:
- memory_type: One of the categories above
- category: A subcategory (e.g., "work", "personal", "hobby")
- title: A brief, descriptive title
- content: Detailed content of the memory
- keywords: Comma-separated keywords for searching
- importance_score: 0.0 to 1.0 (how important this is to remember)
- confidence_score: 0.0 to 1.0 (how confident you are this is accurate)
- reasoning: Why this should be remembered

Also provide a brief summary of the conversation.

Respond in JSON format:
{
  "memories": [
    {
      "memory_type": "PERSONAL_FACT",
      "category": "work",
      "title": "Software Engineer at Tech Corp",
      "content": "User works as a software engineer at Tech Corp, focusing on backend development",
      "keywords": "software engineer, Tech Corp, backend, development",
      "importance_score": 0.8,
      "confidence_score": 0.9,
      "reasoning": "Job information is important for providing relevant technical advice"
    }
  ],
  "summary": "Brief summary of the conversation"
}

Only extract information that is clearly stated or strongly implied. Don't make assumptions."""

        if context:
            prompt += f"\n\nAdditional context: {context}"

        return prompt

    def _parse_extraction_response(self, ai_response: str) -> Dict[str, Any]:
        """Parse the AI response to extract memories"""
        try:
            # Try to parse as JSON
            return json.loads(ai_response)
        except json.JSONDecodeError:
            # If JSON parsing fails, try to extract JSON from the response
            import re
            json_match = re.search(r'\{.*\}', ai_response, re.DOTALL)
            if json_match:
                try:
                    return json.loads(json_match.group())
                except json.JSONDecodeError:
                    pass

            # If all else fails, return empty structure
            return {"memories": [], "summary": None}

    def _store_conversation_context(
        self,
        user_id: str,
        device_id: str,
        conversation_text: str,
        summary: Optional[str],
        assistant_id: Optional[str]
    ):
        """Store conversation context for future reference"""
        context = ConversationContext(
            user_id=user_id,
            device_id=device_id,
            assistant_id=assistant_id,
            summary=summary,
            message_count=len(conversation_text.split('\n'))
        )

        self.db.add(context)
        self.db.commit()
