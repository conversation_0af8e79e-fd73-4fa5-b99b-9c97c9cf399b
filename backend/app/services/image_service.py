import httpx
from typing import List, Dict, Any
from ..config import OPENAI_API_KEY

class ImageService:
    """Service for handling image generation with OpenAI API"""

    @staticmethod
    async def generate_image(
        prompt: str,
        model: str = "dall-e-3",
        size: str = "1024x1024",
        quality: str = "standard",
        n: int = 1
    ) -> List[Dict[str, str]]:
        """Generate an image using OpenAI's image generation APIs

        Supports both DALL-E models and the new GPT-Image-1 model.
        """

        # Create the base request data
        request_data = {
            "prompt": prompt,
            "size": size,
            "n": n
        }

        # Add model-specific parameters
        if model == "gpt-image-1":
            # GPT-Image-1 doesn't use the quality parameter
            request_data["model"] = "gpt-image-1"
        else:
            # DALL-E models use the quality parameter
            request_data["model"] = model
            request_data["quality"] = quality

        # Send request to OpenAI
        async with httpx.AsyncClient(timeout=60.0) as client:
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {OPENAI_API_KEY}"
            }

            print(f"Sending image generation request: {request_data}")

            try:
                response = await client.post(
                    "https://api.openai.com/v1/images/generations",
                    json=request_data,
                    headers=headers
                )

                print(f"OpenAI response status: {response.status_code}")
                print(f"OpenAI response: {response.text}")

                # Check for errors
                if response.status_code != 200:
                    error_detail = response.json().get("error", {}).get("message", "Unknown error")
                    raise Exception(f"OpenAI API error: {error_detail}")

                # Parse the response
                response_data = response.json()

                # Extract the image URLs
                if "data" in response_data and len(response_data["data"]) > 0:
                    return response_data["data"]
                else:
                    raise Exception("No images were generated. Please try again.")

            except httpx.TimeoutException:
                raise Exception("Image generation request timed out. Please try again.")
            except Exception as e:
                print(f"Image generation error: {str(e)}")
                raise

    @staticmethod
    async def edit_image(
        image_data: bytes,
        prompt: str,
        model: str = "dall-e-2",
        size: str = "1024x1024",
        n: int = 1
    ) -> List[Dict[str, str]]:
        """Edit an image using OpenAI's image editing API

        Note: If gpt-image-1 is not supported for editing, falls back to dall-e-2
        """

        # Create multipart form data
        import tempfile
        import os

        # For image editing, use dall-e-2 if gpt-image-1 is requested (fallback)
        # since gpt-image-1 might not support image editing yet
        edit_model = "dall-e-2" if model == "gpt-image-1" else model

        # Save image data to temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix='.png') as temp_file:
            temp_file.write(image_data)
            temp_file_path = temp_file.name

        try:
            # Send request to OpenAI
            async with httpx.AsyncClient(timeout=60.0) as client:
                headers = {
                    "Authorization": f"Bearer {OPENAI_API_KEY}"
                }

                print(f"Sending image edit request with model: {edit_model}")

                # Prepare files and data for multipart upload
                with open(temp_file_path, 'rb') as image_file:
                    files = {
                        'image': ('image.png', image_file, 'image/png')
                    }
                    data = {
                        'prompt': prompt,
                        'model': edit_model,
                        'size': size,
                        'n': n
                    }

                    try:
                        response = await client.post(
                            "https://api.openai.com/v1/images/edits",
                            headers=headers,
                            files=files,
                            data=data
                        )

                        print(f"OpenAI edit response status: {response.status_code}")
                        print(f"OpenAI edit response: {response.text}")

                        # Check for errors
                        if response.status_code != 200:
                            error_detail = response.json().get("error", {}).get("message", "Unknown error")
                            raise Exception(f"OpenAI API error: {error_detail}")

                        # Parse the response
                        response_data = response.json()

                        # Extract the image URLs
                        if "data" in response_data and len(response_data["data"]) > 0:
                            return response_data["data"]
                        else:
                            raise Exception("No images were generated. Please try again.")

                    except httpx.TimeoutException:
                        raise Exception("Image editing request timed out. Please try again.")
                    except Exception as e:
                        print(f"Image editing error: {str(e)}")
                        raise

        finally:
            # Clean up temporary file
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
