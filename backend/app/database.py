from sqlalchemy import create_engine, Column, String, DateTime, Float, <PERSON><PERSON><PERSON>, Text, Integer, ForeignKey, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.dialects.postgresql import UUID
from datetime import datetime
import uuid
import os

# Database configuration
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./pluto_ai.db")

# Handle PostgreSQL URL format for SQLAlchemy
if DATABASE_URL.startswith("postgres://"):
    DATABASE_URL = DATABASE_URL.replace("postgres://", "postgresql://", 1)

engine = create_engine(
    DATABASE_URL,
    echo=True if os.getenv("DEBUG") == "true" else False
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# Database Models
class Chat(Base):
    """Chat sessions between users and assistants"""
    __tablename__ = "chats"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, nullable=False, index=True)
    device_id = Column(String, nullable=False, index=True)
    assistant_id = Column(String, nullable=False)
    title = Column(String, nullable=False, default="New Chat")

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    messages = relationship("Message", back_populates="chat", cascade="all, delete-orphan")

    # Indexes
    __table_args__ = (
        Index('idx_chat_user_created', 'user_id', 'created_at'),
        Index('idx_chat_device_created', 'device_id', 'created_at'),
    )

class Message(Base):
    """Individual messages within chat sessions"""
    __tablename__ = "messages"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    chat_id = Column(String, ForeignKey("chats.id"), nullable=False)
    content = Column(Text, nullable=False)
    is_user = Column(Boolean, nullable=False, default=True)

    # AI response metadata
    model_used = Column(String, nullable=True)  # which AI model generated this response
    tokens_used = Column(Integer, nullable=True)  # token count for AI responses
    response_time = Column(Float, nullable=True)  # response time in seconds

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    chat = relationship("Chat", back_populates="messages")

    # Indexes
    __table_args__ = (
        Index('idx_message_chat_created', 'chat_id', 'created_at'),
        Index('idx_message_user_flag', 'chat_id', 'is_user'),
    )

class UserMemory(Base):
    """Core memory storage for user information and preferences"""
    __tablename__ = "user_memories"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, nullable=False, index=True)
    device_id = Column(String, nullable=False, index=True)
    
    # Memory content
    memory_type = Column(String, nullable=False, index=True)  # personal_fact, preference, goal, context, behavior, technical
    category = Column(String, nullable=True, index=True)     # subcategory for organization
    title = Column(String, nullable=False)                   # brief title/summary
    content = Column(Text, nullable=False)                   # detailed memory content
    keywords = Column(Text, nullable=True)                   # comma-separated keywords for search
    
    # Memory metadata
    importance_score = Column(Float, default=0.5)            # 0.0 to 1.0, how important this memory is
    confidence_score = Column(Float, default=1.0)            # 0.0 to 1.0, how confident we are in this memory
    access_count = Column(Integer, default=0)                # how often this memory has been accessed
    last_accessed = Column(DateTime, nullable=True)          # when this memory was last used
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    interactions = relationship("MemoryInteraction", back_populates="memory", cascade="all, delete-orphan")
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_user_memory_type', 'user_id', 'memory_type'),
        Index('idx_user_importance', 'user_id', 'importance_score'),
        Index('idx_memory_keywords', 'keywords'),
    )

class MemoryInteraction(Base):
    """Track when and how memories are used in conversations"""
    __tablename__ = "memory_interactions"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    memory_id = Column(String, ForeignKey("user_memories.id"), nullable=False)
    user_id = Column(String, nullable=False, index=True)
    
    # Interaction details
    interaction_type = Column(String, nullable=False)        # retrieved, updated, created, referenced
    context = Column(Text, nullable=True)                    # conversation context when memory was used
    relevance_score = Column(Float, nullable=True)           # how relevant was this memory to the conversation
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    memory = relationship("UserMemory", back_populates="interactions")
    
    # Indexes
    __table_args__ = (
        Index('idx_memory_interaction_user', 'user_id', 'created_at'),
        Index('idx_memory_interaction_type', 'memory_id', 'interaction_type'),
    )

class ConversationContext(Base):
    """Store conversation context for memory extraction"""
    __tablename__ = "conversation_contexts"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, nullable=False, index=True)
    device_id = Column(String, nullable=False)
    
    # Conversation details
    chat_id = Column(String, nullable=True)                  # reference to chat session
    assistant_id = Column(String, nullable=True)             # which assistant was used
    message_count = Column(Integer, default=0)               # number of messages in conversation
    
    # Context summary
    topic = Column(String, nullable=True)                    # main topic of conversation
    summary = Column(Text, nullable=True)                    # AI-generated summary
    extracted_memories = Column(Text, nullable=True)         # JSON of memories extracted from this conversation
    
    # Timestamps
    started_at = Column(DateTime, default=datetime.utcnow)
    last_message_at = Column(DateTime, default=datetime.utcnow)
    
    # Indexes
    __table_args__ = (
        Index('idx_conversation_user_time', 'user_id', 'last_message_at'),
    )

class MemoryPreference(Base):
    """User preferences for memory management"""
    __tablename__ = "memory_preferences"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, nullable=False, unique=True, index=True)
    device_id = Column(String, nullable=False)
    
    # Privacy settings
    memory_enabled = Column(Boolean, default=True)           # whether to store memories at all
    auto_extract = Column(Boolean, default=True)             # automatically extract memories from conversations
    share_across_assistants = Column(Boolean, default=True)  # share memories between different assistants
    
    # Retention settings
    max_memories = Column(Integer, default=1000)             # maximum number of memories to store
    retention_days = Column(Integer, default=365)            # how long to keep memories
    
    # Personalization settings
    personalization_level = Column(String, default="medium") # low, medium, high
    memory_importance_threshold = Column(Float, default=0.3) # minimum importance score to keep memories
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

# Database session dependency
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Create tables
def create_tables():
    Base.metadata.create_all(bind=engine)

if __name__ == "__main__":
    create_tables()
    print("Database tables created successfully!")
