import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# API Configuration
API_V1_PREFIX = "/api/v1"

# OpenAI Configuration
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "********************************************************************************************************************************************************************")

# DeepSeek Configuration
DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY", "***********************************")

# Claude Configuration
CLAUDE_API_KEY = os.getenv("CLAUDE_API_KEY", "************************************************************************************************************")

# Gemini Configuration
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "AIzaSyAhEJAtPNFVcEDdM_GahH_u9MR1Q4FrHLA")

# JWT Configuration
JWT_SECRET = os.getenv("JWT_SECRET", "your-secret-key")
JWT_ALGORITHM = "HS256"
JWT_EXPIRATION_MINUTES = 60 * 24 * 7  # 7 days

# RevenueCat Configuration (for verification)
REVENUECAT_API_KEY = os.getenv("REVENUECAT_API_KEY", "")
