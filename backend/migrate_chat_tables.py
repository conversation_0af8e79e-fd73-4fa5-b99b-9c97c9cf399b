#!/usr/bin/env python3
"""
Migration script to add chat and message tables to the existing database.
This script will create the new tables without affecting existing memory tables.
"""

import os
import sys
from sqlalchemy import create_engine, text
from sqlalchemy.exc import OperationalError

# Add the app directory to the path so we can import our models
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.database import Base, engine, Chat, Message

def create_chat_tables():
    """Create only the chat and message tables"""
    try:
        # Create only the new tables
        Chat.__table__.create(engine, checkfirst=True)
        Message.__table__.create(engine, checkfirst=True)
        
        print("✅ Chat and Message tables created successfully!")
        return True
    except Exception as e:
        print(f"❌ Error creating chat tables: {e}")
        return False

def verify_tables():
    """Verify that the tables were created correctly"""
    try:
        with engine.connect() as conn:
            # Check if tables exist
            result = conn.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name IN ('chats', 'messages')"))
            tables = [row[0] for row in result]
            
            if 'chats' in tables and 'messages' in tables:
                print("✅ Tables verified successfully!")
                
                # Check table structure
                chats_info = conn.execute(text("PRAGMA table_info(chats)"))
                messages_info = conn.execute(text("PRAGMA table_info(messages)"))
                
                print("\n📋 Chats table structure:")
                for row in chats_info:
                    print(f"  - {row[1]} ({row[2]})")
                
                print("\n📋 Messages table structure:")
                for row in messages_info:
                    print(f"  - {row[1]} ({row[2]})")
                
                return True
            else:
                print(f"❌ Missing tables. Found: {tables}")
                return False
    except Exception as e:
        print(f"❌ Error verifying tables: {e}")
        return False

def main():
    print("🚀 Starting chat tables migration...")
    print(f"📍 Database URL: {os.getenv('DATABASE_URL', 'sqlite:///./pluto_ai.db')}")
    
    # Create the tables
    if create_chat_tables():
        # Verify the tables
        if verify_tables():
            print("\n🎉 Migration completed successfully!")
            print("The backend can now save and retrieve chat history.")
        else:
            print("\n⚠️ Migration completed but verification failed.")
            sys.exit(1)
    else:
        print("\n❌ Migration failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
