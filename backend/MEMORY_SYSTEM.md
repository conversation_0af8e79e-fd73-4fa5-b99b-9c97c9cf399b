# 🧠 Pluto AI Memory System

A comprehensive user memory system that enables personalized AI responses similar to ChatGPT's memory feature.

## 🌟 Features

### Core Memory Capabilities
- **Personal Facts**: Store user's name, age, location, job, family details
- **Preferences**: Remember likes, dislikes, communication style, interests
- **Goals & Aspirations**: Track career goals, personal projects, learning objectives
- **Context & History**: Maintain conversation context and important events
- **Behavioral Patterns**: Learn communication style and response preferences
- **Technical Background**: Remember programming languages, tools, frameworks

### Intelligent Memory Management
- **Automatic Extraction**: AI-powered memory extraction from conversations
- **Relevance Scoring**: Smart retrieval of relevant memories for context
- **Duplicate Prevention**: Avoid storing redundant information
- **Importance Weighting**: Prioritize memories based on significance
- **Access Tracking**: Monitor memory usage for optimization

### Privacy & Control
- **User Preferences**: Granular control over memory storage
- **Retention Policies**: Configurable memory retention periods
- **Selective Sharing**: Control memory sharing across assistants
- **Data Analytics**: Insights into memory patterns and usage

## 🏗️ Architecture

```
┌─────────────────────┐
│   Chat Interface    │
├─────────────────────┤
│ Enhanced Chat       │ ← Memory-aware chat processing
│ Service             │
├─────────────────────┤
│ Memory Service      │ ← Core memory operations
├─────────────────────┤
│ Memory Extraction   │ ← AI-powered memory extraction
│ Service             │
├─────────────────────┤
│ Database Layer      │ ← SQLAlchemy models & storage
└─────────────────────┘
```

## 📊 Database Schema

### UserMemory
- **Core Fields**: id, user_id, device_id, memory_type, category
- **Content**: title, content, keywords
- **Metadata**: importance_score, confidence_score, access_count
- **Timestamps**: created_at, updated_at, last_accessed

### MemoryInteraction
- **Tracking**: memory_id, user_id, interaction_type
- **Context**: context, relevance_score
- **Analytics**: created_at

### ConversationContext
- **Session**: user_id, device_id, chat_id, assistant_id
- **Summary**: topic, summary, extracted_memories
- **Metrics**: message_count, started_at, last_message_at

### MemoryPreference
- **Privacy**: memory_enabled, auto_extract, share_across_assistants
- **Retention**: max_memories, retention_days
- **Personalization**: personalization_level, importance_threshold

## 🚀 Quick Start

### 1. Initialize Database
```bash
cd backend
python init_memory_db.py
```

### 2. Start the Server
```bash
uvicorn main:app --reload
```

### 3. Test Memory Endpoints
Visit `http://localhost:8000/docs` to explore the API.

## 📡 API Endpoints

### Memory Management
- `POST /api/v1/memory/create` - Create new memory
- `GET /api/v1/memory/{memory_id}` - Get specific memory
- `PUT /api/v1/memory/{memory_id}` - Update memory
- `DELETE /api/v1/memory/{memory_id}` - Delete memory

### Memory Search & Retrieval
- `POST /api/v1/memory/search` - Search memories with filters
- `GET /api/v1/memory/relevant/context` - Get contextually relevant memories
- `GET /api/v1/memory/list` - List memories with pagination
- `GET /api/v1/memory/categories` - Get memory categories

### Memory Extraction
- `POST /api/v1/memory/extract` - Extract memories from conversation
- `GET /api/v1/memory/stats` - Get memory analytics

### Bulk Operations
- `POST /api/v1/memory/bulk-delete` - Delete multiple memories

## 💡 Usage Examples

### Creating a Memory
```python
memory_data = {
    "memory_type": "personal_fact",
    "category": "work",
    "title": "Software Engineer at Tech Corp",
    "content": "User works as a software engineer at Tech Corp, focusing on backend development",
    "keywords": "software engineer, Tech Corp, backend, development",
    "importance_score": 0.8,
    "confidence_score": 0.9
}
```

### Searching Memories
```python
search_request = {
    "query": "programming",
    "memory_types": ["technical", "preference"],
    "min_importance": 0.5,
    "limit": 10
}
```

### Extracting Memories
```python
extract_request = {
    "conversation_text": "User: I'm a Python developer working on AI projects...",
    "context": "Technical discussion",
    "assistant_id": "coding_assistant"
}
```

## 🔧 Configuration

### Environment Variables
```bash
DATABASE_URL=sqlite:///./pluto_ai.db  # or PostgreSQL URL
OPENAI_API_KEY=your_openai_api_key
DEBUG=true  # for development
```

### Memory Types
- `personal_fact` - Basic personal information
- `preference` - User preferences and likes/dislikes
- `goal` - Goals and aspirations
- `context` - Conversation context and recent events
- `behavior` - Communication patterns
- `technical` - Technical skills and knowledge

## 🎯 Integration with Chat

The memory system automatically integrates with chat responses:

1. **Context Retrieval**: Relevant memories are retrieved based on user input
2. **Response Enhancement**: AI responses include personalized context
3. **Memory Extraction**: New memories are extracted from conversations
4. **Continuous Learning**: System learns and adapts to user patterns

## 📈 Analytics & Insights

### Memory Statistics
- Total memories by type
- Most accessed memories
- Memory growth trends
- User interaction patterns

### Performance Metrics
- Memory retrieval speed
- Relevance accuracy
- Extraction quality
- User satisfaction

## 🔒 Privacy & Security

### Data Protection
- Device-based authentication
- Encrypted memory storage
- Configurable retention policies
- User-controlled data deletion

### Privacy Controls
- Enable/disable memory storage
- Control automatic extraction
- Manage cross-assistant sharing
- Set importance thresholds

## 🛠️ Development

### Adding New Memory Types
1. Update `MemoryType` enum in `models.py`
2. Add extraction logic in `MemoryExtractionService`
3. Update formatting in `EnhancedChatService`

### Customizing Extraction
Modify the extraction prompt in `MemoryExtractionService._create_extraction_prompt()` to:
- Add new memory categories
- Adjust extraction criteria
- Improve accuracy

### Enhancing Retrieval
Update `MemoryService.get_relevant_memories()` to:
- Implement semantic search
- Add machine learning ranking
- Improve context matching

## 🚀 Future Enhancements

- **Semantic Search**: Vector-based memory retrieval
- **Memory Clustering**: Group related memories
- **Smart Summarization**: Automatic memory consolidation
- **Cross-Device Sync**: Sync memories across devices
- **Memory Visualization**: Visual memory maps
- **Export/Import**: Memory backup and migration

## 📞 Support

For questions or issues with the memory system:
1. Check the API documentation at `/docs`
2. Review the database logs for errors
3. Test individual endpoints with sample data
4. Verify database connectivity and permissions

---

**🎉 Your Pluto AI chatbot now has a memory! Enjoy personalized conversations that get better over time.**
