#!/bin/bash

# Test the complete chat flow
DEVICE_ID="ios-test-$(date +%s)"
USER_ID="user_$DEVICE_ID"
BASE_URL="https://pluto-ai-chatbot.fly.dev/api/v1"

echo "Testing chat flow with Device ID: $DEVICE_ID"
echo "User ID: $USER_ID"
echo ""

# Step 1: Create a new chat
echo "1. Creating new chat..."
CHAT_RESPONSE=$(curl -s -X POST "$BASE_URL/chat" \
  -H "Content-Type: application/json" \
  -H "X-Device-ID: $DEVICE_ID" \
  -H "X-User-ID: $USER_ID" \
  -d '{"assistant_id": "gpt-4.1-nano", "title": "Test Chat"}')

echo "Chat creation response: $CHAT_RESPONSE"

# Extract chat ID
CHAT_ID=$(echo $CHAT_RESPONSE | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
echo "Chat ID: $CHAT_ID"
echo ""

if [ -z "$CHAT_ID" ]; then
    echo "ERROR: Failed to create chat"
    exit 1
fi

# Step 2: Send a message
echo "2. Sending message to chat..."
MESSAGE_RESPONSE=$(curl -s -X POST "$BASE_URL/chat/$CHAT_ID/messages" \
  -H "Content-Type: application/json" \
  -H "X-Device-ID: $DEVICE_ID" \
  -H "X-User-ID: $USER_ID" \
  -d '{"content": "Hello, this is a test message from the script"}')

echo "Message response: $MESSAGE_RESPONSE"
echo ""

# Step 3: Get chat history
echo "3. Getting chat history..."
HISTORY_RESPONSE=$(curl -s -X GET "$BASE_URL/chat" \
  -H "X-Device-ID: $DEVICE_ID" \
  -H "X-User-ID: $USER_ID")

echo "Chat history: $HISTORY_RESPONSE"
echo ""

# Step 4: Get specific chat with messages
echo "4. Getting specific chat with messages..."
CHAT_DETAIL=$(curl -s -X GET "$BASE_URL/chat/$CHAT_ID" \
  -H "X-Device-ID: $DEVICE_ID" \
  -H "X-User-ID: $USER_ID")

echo "Chat detail: $CHAT_DETAIL"
echo ""

echo "Test completed!"
