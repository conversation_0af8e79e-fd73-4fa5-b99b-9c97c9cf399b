# Testing Pluto AI Chatbot with TestFlight

This guide explains how to run the backend locally and test the app from TestFlight.

## 1. Set Up Supabase

1. Go to your Supabase project: https://bhxniozyeopiqnmwzhge.supabase.co
2. Navigate to the SQL Editor
3. Copy and paste the contents of `backend/setup_supabase.sql`
4. Run the SQL to create the necessary tables and policies

## 2. Run the Backend Locally

### Option A: Run with the provided script

```bash
# Navigate to the backend directory
cd backend

# Run the script (it will create a virtual environment, install dependencies, and start the server)
./run_backend.sh
```

### Option B: Run with <PERSON> directly

```bash
# Navigate to the backend directory
cd backend

# Create and activate a virtual environment (optional but recommended)
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Run the server
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

### Option B: Run with <PERSON><PERSON>

```bash
# Navigate to the project root
cd /path/to/Pluto-AI---<PERSON><PERSON>-<PERSON><PERSON>-Assistant

# Build and run with <PERSON><PERSON> Compose
docker-compose up
```

## 3. Make the Backend Accessible from the Internet

To test with TestFlight, you need to make your local backend accessible from the internet. You can use ngrok for this:

1. Install ngrok: https://ngrok.com/download

2. Run ngrok to create a tunnel to your local server:
```bash
ngrok http 8000
```

3. Ngrok will provide a public URL (like `https://abc123.ngrok.io`) that forwards to your local server.

## 4. Update the iOS App

Before building for TestFlight, update the backend URL in your app:

1. Open `frontend/Pluto AI - Chat Bot Assistant/Services/APIConfig.swift`
2. Update the `backendBaseURL` to your ngrok URL:
```swift
static let backendBaseURL = "https://your-ngrok-url.io"
```

3. Build your app for TestFlight:
   - In Xcode, select Product > Archive
   - Upload the archive to App Store Connect
   - Add your app to TestFlight

## 5. Testing the AI Functionality

Once your app is on TestFlight and your backend is running, you can test the AI functionality:

1. **Authentication**: Sign up and log in using Supabase
2. **Chat**: Test sending messages and receiving AI responses
3. **AI Models**: Test both OpenAI and DeepSeek models
4. **Image Generation**: Test generating images with DALL-E
5. **Assistants**: Test different assistants and verify they provide specialized responses
6. **Suggestions**: Test that suggestions work and provide relevant responses
7. **Subscriptions**: Test both weekly and lifetime subscription options with RevenueCat
   - The app is configured with your RevenueCat API key: `appl_nXzSOQAelhQlIhOmVLyiUmQguwS`
   - Make sure you've set up the subscription products in RevenueCat dashboard
   - Test the free trial for weekly subscription
   - Test one-time payment for lifetime subscription

## 6. Verifying Backend Functionality

You can directly test your backend API endpoints to verify they're working:

```bash
# Get health status
curl http://localhost:8000/health

# Get AI models (requires auth token)
curl -X GET http://localhost:8000/api/v1/ai-model/models \
  -H "Authorization: Bearer YOUR_AUTH_TOKEN"

# Generate an image (requires auth token)
curl -X POST http://localhost:8000/api/v1/image/generate \
  -H "Authorization: Bearer YOUR_AUTH_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"prompt": "A beautiful sunset over mountains"}'
```

## 7. Troubleshooting

If you encounter issues:

1. **Backend Connection Issues**:
   - Check that your ngrok URL is correct in `APIConfig.swift`
   - Verify your backend is running and accessible
   - Check for CORS issues

2. **Authentication Issues**:
   - Ensure Supabase is properly configured
   - Check that auth tokens are being stored and sent correctly

3. **AI Response Issues**:
   - Verify your OpenAI and DeepSeek API keys are valid
   - Check backend logs for any API errors

4. **Database Issues**:
   - Check that the Supabase tables were created correctly
   - Verify that the RLS policies are working as expected

## 8. Deploying to Production

When you're ready to deploy to production:

1. Deploy the backend to Fly.io:
```bash
cd backend
fly auth login
fly launch
# Set environment variables
fly secrets set OPENAI_API_KEY=your_openai_api_key
fly secrets set DEEPSEEK_API_KEY=your_deepseek_api_key
fly secrets set SUPABASE_URL=your_supabase_url
fly secrets set SUPABASE_KEY=your_supabase_key
fly secrets set JWT_SECRET=your_jwt_secret
fly deploy
```

2. Update the backend URL in `APIConfig.swift` to your Fly.io URL:
```swift
static let backendBaseURL = "https://your-app.fly.dev"
```

3. Build and submit your app to the App Store.
