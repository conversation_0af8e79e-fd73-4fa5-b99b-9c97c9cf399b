// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		89DE12AE2DC23B1A0023C99C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 89DE128F2DC23B190023C99C /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 89DE12962DC23B190023C99C;
			remoteInfo = "Pluto AI - Chat Bot Assistant";
		};
		89DE12B82DC23B1A0023C99C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 89DE128F2DC23B190023C99C /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 89DE12962DC23B190023C99C;
			remoteInfo = "Pluto AI - Chat Bot Assistant";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		89DE12972DC23B190023C99C /* Pluto AI - Chat Bot Assistant.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "Pluto AI - Chat Bot Assistant.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		89DE12AD2DC23B1A0023C99C /* Pluto AI - Chat Bot AssistantTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "Pluto AI - Chat Bot AssistantTests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		89DE12B72DC23B1A0023C99C /* Pluto AI - Chat Bot AssistantUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "Pluto AI - Chat Bot AssistantUITests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		89DE12992DC23B190023C99C /* Pluto AI - Chat Bot Assistant */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "Pluto AI - Chat Bot Assistant";
			sourceTree = "<group>";
		};
		89DE12B02DC23B1A0023C99C /* Pluto AI - Chat Bot AssistantTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "Pluto AI - Chat Bot AssistantTests";
			sourceTree = "<group>";
		};
		89DE12BA2DC23B1A0023C99C /* Pluto AI - Chat Bot AssistantUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "Pluto AI - Chat Bot AssistantUITests";
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		89DE12942DC23B190023C99C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		89DE12AA2DC23B1A0023C99C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		89DE12B42DC23B1A0023C99C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		894498442DD9EE7B0013B050 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		89DE128E2DC23B190023C99C = {
			isa = PBXGroup;
			children = (
				89DE12992DC23B190023C99C /* Pluto AI - Chat Bot Assistant */,
				89DE12B02DC23B1A0023C99C /* Pluto AI - Chat Bot AssistantTests */,
				89DE12BA2DC23B1A0023C99C /* Pluto AI - Chat Bot AssistantUITests */,
				894498442DD9EE7B0013B050 /* Frameworks */,
				89DE12982DC23B190023C99C /* Products */,
			);
			sourceTree = "<group>";
		};
		89DE12982DC23B190023C99C /* Products */ = {
			isa = PBXGroup;
			children = (
				89DE12972DC23B190023C99C /* Pluto AI - Chat Bot Assistant.app */,
				89DE12AD2DC23B1A0023C99C /* Pluto AI - Chat Bot AssistantTests.xctest */,
				89DE12B72DC23B1A0023C99C /* Pluto AI - Chat Bot AssistantUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		89DE12962DC23B190023C99C /* Pluto AI - Chat Bot Assistant */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 89DE12C12DC23B1A0023C99C /* Build configuration list for PBXNativeTarget "Pluto AI - Chat Bot Assistant" */;
			buildPhases = (
				89DE12932DC23B190023C99C /* Sources */,
				89DE12942DC23B190023C99C /* Frameworks */,
				89DE12952DC23B190023C99C /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				89DE12992DC23B190023C99C /* Pluto AI - Chat Bot Assistant */,
			);
			name = "Pluto AI - Chat Bot Assistant";
			packageProductDependencies = (
			);
			productName = "Pluto AI - Chat Bot Assistant";
			productReference = 89DE12972DC23B190023C99C /* Pluto AI - Chat Bot Assistant.app */;
			productType = "com.apple.product-type.application";
		};
		89DE12AC2DC23B1A0023C99C /* Pluto AI - Chat Bot AssistantTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 89DE12C42DC23B1A0023C99C /* Build configuration list for PBXNativeTarget "Pluto AI - Chat Bot AssistantTests" */;
			buildPhases = (
				89DE12A92DC23B1A0023C99C /* Sources */,
				89DE12AA2DC23B1A0023C99C /* Frameworks */,
				89DE12AB2DC23B1A0023C99C /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				89DE12AF2DC23B1A0023C99C /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				89DE12B02DC23B1A0023C99C /* Pluto AI - Chat Bot AssistantTests */,
			);
			name = "Pluto AI - Chat Bot AssistantTests";
			packageProductDependencies = (
			);
			productName = "Pluto AI - Chat Bot AssistantTests";
			productReference = 89DE12AD2DC23B1A0023C99C /* Pluto AI - Chat Bot AssistantTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		89DE12B62DC23B1A0023C99C /* Pluto AI - Chat Bot AssistantUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 89DE12C72DC23B1A0023C99C /* Build configuration list for PBXNativeTarget "Pluto AI - Chat Bot AssistantUITests" */;
			buildPhases = (
				89DE12B32DC23B1A0023C99C /* Sources */,
				89DE12B42DC23B1A0023C99C /* Frameworks */,
				89DE12B52DC23B1A0023C99C /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				89DE12B92DC23B1A0023C99C /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				89DE12BA2DC23B1A0023C99C /* Pluto AI - Chat Bot AssistantUITests */,
			);
			name = "Pluto AI - Chat Bot AssistantUITests";
			packageProductDependencies = (
			);
			productName = "Pluto AI - Chat Bot AssistantUITests";
			productReference = 89DE12B72DC23B1A0023C99C /* Pluto AI - Chat Bot AssistantUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		89DE128F2DC23B190023C99C /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					89DE12962DC23B190023C99C = {
						CreatedOnToolsVersion = 16.2;
					};
					89DE12AC2DC23B1A0023C99C = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 89DE12962DC23B190023C99C;
					};
					89DE12B62DC23B1A0023C99C = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 89DE12962DC23B190023C99C;
					};
				};
			};
			buildConfigurationList = 89DE12922DC23B190023C99C /* Build configuration list for PBXProject "Pluto AI - Chat Bot Assistant" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 89DE128E2DC23B190023C99C;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 89DE12982DC23B190023C99C /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				89DE12962DC23B190023C99C /* Pluto AI - Chat Bot Assistant */,
				89DE12AC2DC23B1A0023C99C /* Pluto AI - Chat Bot AssistantTests */,
				89DE12B62DC23B1A0023C99C /* Pluto AI - Chat Bot AssistantUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		89DE12952DC23B190023C99C /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		89DE12AB2DC23B1A0023C99C /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		89DE12B52DC23B1A0023C99C /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		89DE12932DC23B190023C99C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		89DE12A92DC23B1A0023C99C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		89DE12B32DC23B1A0023C99C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		89DE12AF2DC23B1A0023C99C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 89DE12962DC23B190023C99C /* Pluto AI - Chat Bot Assistant */;
			targetProxy = 89DE12AE2DC23B1A0023C99C /* PBXContainerItemProxy */;
		};
		89DE12B92DC23B1A0023C99C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 89DE12962DC23B190023C99C /* Pluto AI - Chat Bot Assistant */;
			targetProxy = 89DE12B82DC23B1A0023C99C /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		89DE12BF2DC23B1A0023C99C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		89DE12C02DC23B1A0023C99C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		89DE12C22DC23B1A0023C99C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = "Pluto AI - Chat Bot Assistant/Pluto_AI___Chat_Bot_Assistant.entitlements";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"Pluto AI - Chat Bot Assistant/Preview Content\"";
				DEVELOPMENT_TEAM = RAUVJFWBMG;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_CFBundleDisplayName = "Pluto AI";
				INFOPLIST_KEY_CFBundleIconName = AppIcon;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.productivity";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "Pluto AI needs access to your microphone to enable voice input for chat messages.";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "Pluto AI needs access to your photo library to save generated images.";
				INFOPLIST_KEY_NSSpeechRecognitionUsageDescription = "Pluto AI needs access to speech recognition to enable voice input for chat messages.";
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 10.15;
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"-framework",
					Speech,
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.www.oyu-intelligence.com.pluto.Pluto-AI---Chat-Bot-Assistant";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Debug;
		};
		89DE12C32DC23B1A0023C99C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = "Pluto AI - Chat Bot Assistant/Pluto_AI___Chat_Bot_Assistant.entitlements";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"Pluto AI - Chat Bot Assistant/Preview Content\"";
				DEVELOPMENT_TEAM = RAUVJFWBMG;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_CFBundleDisplayName = "Pluto AI";
				INFOPLIST_KEY_CFBundleIconName = AppIcon;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.productivity";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "Pluto AI needs access to your microphone to enable voice input for chat messages.";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "Pluto AI needs access to your photo library to save generated images.";
				INFOPLIST_KEY_NSSpeechRecognitionUsageDescription = "Pluto AI needs access to speech recognition to enable voice input for chat messages.";
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 10.15;
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"-framework",
					Speech,
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.www.oyu-intelligence.com.pluto.Pluto-AI---Chat-Bot-Assistant";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Release;
		};
		89DE12C52DC23B1A0023C99C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = RAUVJFWBMG;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.www.oyu-intelligence.com.pluto.Pluto-AI---Chat-Bot-AssistantTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Pluto AI - Chat Bot Assistant.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Pluto AI - Chat Bot Assistant";
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Debug;
		};
		89DE12C62DC23B1A0023C99C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = RAUVJFWBMG;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.www.oyu-intelligence.com.pluto.Pluto-AI---Chat-Bot-AssistantTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Pluto AI - Chat Bot Assistant.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Pluto AI - Chat Bot Assistant";
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Release;
		};
		89DE12C82DC23B1A0023C99C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = RAUVJFWBMG;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.www.oyu-intelligence.com.pluto.Pluto-AI---Chat-Bot-AssistantUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = "Pluto AI - Chat Bot Assistant";
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Debug;
		};
		89DE12C92DC23B1A0023C99C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = RAUVJFWBMG;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.www.oyu-intelligence.com.pluto.Pluto-AI---Chat-Bot-AssistantUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = "Pluto AI - Chat Bot Assistant";
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		89DE12922DC23B190023C99C /* Build configuration list for PBXProject "Pluto AI - Chat Bot Assistant" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				89DE12BF2DC23B1A0023C99C /* Debug */,
				89DE12C02DC23B1A0023C99C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		89DE12C12DC23B1A0023C99C /* Build configuration list for PBXNativeTarget "Pluto AI - Chat Bot Assistant" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				89DE12C22DC23B1A0023C99C /* Debug */,
				89DE12C32DC23B1A0023C99C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		89DE12C42DC23B1A0023C99C /* Build configuration list for PBXNativeTarget "Pluto AI - Chat Bot AssistantTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				89DE12C52DC23B1A0023C99C /* Debug */,
				89DE12C62DC23B1A0023C99C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		89DE12C72DC23B1A0023C99C /* Build configuration list for PBXNativeTarget "Pluto AI - Chat Bot AssistantUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				89DE12C82DC23B1A0023C99C /* Debug */,
				89DE12C92DC23B1A0023C99C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 89DE128F2DC23B190023C99C /* Project object */;
}
