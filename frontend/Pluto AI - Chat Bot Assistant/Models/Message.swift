//
//  Message.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.04.30.
//

import Foundation
import CoreData

struct Message: Identifiable, Equatable {
    let id: UUID
    var content: String  // Made mutable for streaming
    let isUser: Bool
    let timestamp: Date
    var imageURL: URL?  // For image messages

    // Initialize from a CoreData entity
    init(entity: MessageEntity) {
        self.id = entity.id ?? UUID()
        self.content = entity.content ?? ""
        self.isUser = entity.isUser
        self.timestamp = entity.timestamp ?? Date()
        self.imageURL = nil  // CoreData doesn't store imageURL yet
    }

    // Initialize manually
    init(id: UUID = UUID(), content: String, isUser: Bool, timestamp: Date, imageURL: URL? = nil) {
        self.id = id
        self.content = content
        self.isUser = isUser
        self.timestamp = timestamp
        self.imageURL = imageURL
    }

    static func == (lhs: Message, rhs: Message) -> <PERSON>ol {
        return lhs.id == rhs.id
    }
}
