//
//  Assistant.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.04.30.
//

import SwiftUI

struct SuggestedPrompt: Identifiable, Codable {
    let id = UUID()
    let text: String
    let icon: String
    let description: String?

    init(text: String, icon: String, description: String? = nil) {
        self.text = text
        self.icon = icon
        self.description = description
    }
}

// Helper struct to make Color codable
struct CodableColor: Codable {
    let red: Double
    let green: Double
    let blue: Double
    let alpha: Double

    init(_ color: Color) {
        let uiColor = UIColor(color)
        var red: CGFloat = 0
        var green: CGFloat = 0
        var blue: CGFloat = 0
        var alpha: CGFloat = 0
        uiColor.getRed(&red, green: &green, blue: &blue, alpha: &alpha)

        self.red = Double(red)
        self.green = Double(green)
        self.blue = Double(blue)
        self.alpha = Double(alpha)
    }

    var color: Color {
        return Color(.sRGB, red: red, green: green, blue: blue, opacity: alpha)
    }
}

struct Assistant: Identifiable, Codable {
    let id = UUID()
    let name: String
    let description: String
    let iconName: String
    let backgroundColor: CodableColor
    let suggestedPrompts: [SuggestedPrompt]?
    let isCustom: Bool

    init(name: String, description: String, iconName: String, backgroundColor: Color, suggestedPrompts: [SuggestedPrompt]? = nil, isCustom: Bool = false) {
        self.name = name
        self.description = description
        self.iconName = iconName
        self.backgroundColor = CodableColor(backgroundColor)
        self.suggestedPrompts = suggestedPrompts
        self.isCustom = isCustom
    }

    var color: Color {
        return backgroundColor.color
    }

    static let assistants: [Assistant] = [
        Assistant(
            name: "Artist",
            description: "Create stunning images with OpenAI's latest model.",
            iconName: "sparkles.rectangle.stack",
            backgroundColor: Color(red: 0.8, green: 0.2, blue: 0.8),
            suggestedPrompts: [
                SuggestedPrompt(text: "Create a photorealistic portrait of a futuristic cyberpunk character", icon: "person.fill", description: "Generate a detailed cyberpunk character with neon lighting"),
                SuggestedPrompt(text: "Design a minimalist logo for a tech startup", icon: "sparkles", description: "Create a clean, modern logo design"),
                SuggestedPrompt(text: "Generate a surreal landscape with floating islands", icon: "mountain.2.fill", description: "Create an imaginative fantasy landscape")
            ]
        ),
        Assistant(
            name: "Logo Designer",
            description: "Unique and memorable logos for your brand.",
            iconName: "paintbrush.fill",
            backgroundColor: Color.pink,
            suggestedPrompts: [
                SuggestedPrompt(text: "Design a modern logo for a sustainable fashion brand", icon: "leaf.fill", description: "Create an eco-friendly brand identity"),
                SuggestedPrompt(text: "Create a tech company logo with geometric elements", icon: "square.grid.3x3.fill", description: "Design a clean, tech-focused logo"),
                SuggestedPrompt(text: "Design a vintage-style logo for a coffee shop", icon: "cup.and.saucer.fill", description: "Create a warm, inviting coffee brand")
            ]
        ),
        Assistant(
            name: "Creative Writer",
            description: "Provides innovative writing ideas and drafts.",
            iconName: "pencil",
            backgroundColor: Color.brown,
            suggestedPrompts: [
                SuggestedPrompt(text: "Write a compelling opening for a sci-fi novel", icon: "sparkles", description: "Create an engaging science fiction story beginning"),
                SuggestedPrompt(text: "Help me develop a character for my fantasy story", icon: "person.fill", description: "Build a detailed fictional character"),
                SuggestedPrompt(text: "Create a plot twist for my mystery novel", icon: "questionmark.circle.fill", description: "Add an unexpected turn to your story")
            ]
        ),
        Assistant(
            name: "Business Planner",
            description: "Provides strategies for business planning and growth.",
            iconName: "briefcase.fill",
            backgroundColor: Color.blue,
            suggestedPrompts: [
                SuggestedPrompt(text: "Create a business plan for a mobile app startup", icon: "iphone", description: "Develop a comprehensive startup strategy"),
                SuggestedPrompt(text: "Analyze market opportunities for my product idea", icon: "chart.line.uptrend.xyaxis", description: "Research market potential and competition"),
                SuggestedPrompt(text: "Help me develop a pricing strategy for my service", icon: "dollarsign.circle.fill", description: "Create an effective pricing model")
            ]
        ),
        Assistant(
            name: "Study Helper",
            description: "Assists with academic tasks and questions.",
            iconName: "book.fill",
            backgroundColor: Color.purple,
            suggestedPrompts: [
                SuggestedPrompt(text: "Explain quantum mechanics using simple analogies", icon: "atom", description: "Break down complex physics concepts"),
                SuggestedPrompt(text: "Create a study schedule for my upcoming exams", icon: "calendar.badge.clock", description: "Optimize your study time effectively"),
                SuggestedPrompt(text: "Help me understand this calculus problem step by step", icon: "function", description: "Master mathematical concepts with guidance")
            ]
        ),
        Assistant(
            name: "Language Teacher",
            description: "Check, translate or practice for a new language.",
            iconName: "character.bubble",
            backgroundColor: Color.cyan,
            suggestedPrompts: [
                SuggestedPrompt(text: "Correct my grammar and suggest improvements for this text", icon: "checkmark.circle.fill", description: "Perfect your writing skills"),
                SuggestedPrompt(text: "Teach me advanced conversation phrases in Spanish", icon: "bubble.left.and.bubble.right.fill", description: "Master natural conversation flow"),
                SuggestedPrompt(text: "Explain the cultural context behind this idiom", icon: "globe", description: "Understand language beyond words")
            ]
        ),
        Assistant(
            name: "Chef",
            description: "Delicious recipe ideas and cooking tips.",
            iconName: "fork.knife",
            backgroundColor: Color.orange,
            suggestedPrompts: [
                SuggestedPrompt(text: "Create a gourmet 3-course menu for a dinner party", icon: "star.fill", description: "Design an impressive dining experience"),
                SuggestedPrompt(text: "What's the best technique for perfectly searing a steak?", icon: "flame.fill", description: "Master professional cooking methods"),
                SuggestedPrompt(text: "Suggest wine pairings for this Mediterranean dish", icon: "wineglass.fill", description: "Elevate your meal with perfect pairings")
            ]
        ),
        Assistant(
            name: "Financial Analyst",
            description: "Manage your finances and plan for a future.",
            iconName: "chart.line.uptrend.xyaxis",
            backgroundColor: Color.green,
            suggestedPrompts: [
                SuggestedPrompt(text: "Analyze this company's financial statements for investment potential", icon: "chart.bar.xaxis", description: "Make informed investment decisions"),
                SuggestedPrompt(text: "Create a retirement savings strategy for my age and income", icon: "banknote.fill", description: "Plan for long-term financial security"),
                SuggestedPrompt(text: "Evaluate the risk-return profile of this portfolio", icon: "scale.3d", description: "Optimize your investment balance")
            ]
        ),
        Assistant(
            name: "Lawyer",
            description: "Legal advice and document preparation.",
            iconName: "building.columns.fill",
            backgroundColor: Color(red: 0.4, green: 0.2, blue: 0.6),
            suggestedPrompts: [
                SuggestedPrompt(text: "Review this contract and identify potential legal issues", icon: "doc.text.magnifyingglass", description: "Protect your interests in agreements"),
                SuggestedPrompt(text: "What are my rights in this employment dispute?", icon: "person.badge.shield.checkmark.fill", description: "Understand your legal protections"),
                SuggestedPrompt(text: "Draft a cease and desist letter for trademark infringement", icon: "envelope.badge.shield.half.filled.fill", description: "Protect your intellectual property")
            ]
        ),
        Assistant(
            name: "Relationship Coach",
            description: "Advice for healthy relationships and dating.",
            iconName: "heart.fill",
            backgroundColor: Color.red,
            suggestedPrompts: [
                SuggestedPrompt(text: "How can I improve communication with my partner?", icon: "bubble.left.and.bubble.right.fill", description: "Build stronger emotional connections"),
                SuggestedPrompt(text: "What are the red flags I should watch for in dating?", icon: "exclamationmark.triangle.fill", description: "Recognize unhealthy relationship patterns"),
                SuggestedPrompt(text: "Help me navigate this conflict resolution conversation", icon: "heart.text.square.fill", description: "Turn disagreements into understanding")
            ]
        ),
        Assistant(
            name: "Marketing Expert",
            description: "Strategies to promote your business effectively.",
            iconName: "megaphone.fill",
            backgroundColor: Color.orange,
            suggestedPrompts: [
                SuggestedPrompt(text: "Create a social media strategy for my target audience", icon: "person.3.fill", description: "Reach and engage your ideal customers"),
                SuggestedPrompt(text: "Analyze my competitor's marketing approach", icon: "magnifyingglass.circle.fill", description: "Gain competitive intelligence insights"),
                SuggestedPrompt(text: "Design a conversion funnel for my online business", icon: "arrow.down.circle.fill", description: "Turn visitors into paying customers")
            ]
        ),
        Assistant(
            name: "Zodiac Expert",
            description: "Astrological insights and horoscope readings.",
            iconName: "sparkles",
            backgroundColor: Color.purple,
            suggestedPrompts: [
                SuggestedPrompt(text: "What does my birth chart reveal about my personality?", icon: "star.circle.fill", description: "Discover your astrological blueprint"),
                SuggestedPrompt(text: "How will this Mercury retrograde affect my sign?", icon: "arrow.clockwise.circle.fill", description: "Navigate planetary influences"),
                SuggestedPrompt(text: "Analyze our compatibility based on our zodiac signs", icon: "heart.circle.fill", description: "Explore relationship astrology")
            ]
        ),
        Assistant(
            name: "Tattoo Artist",
            description: "Design ideas and advice for tattoos.",
            iconName: "paintpalette.fill",
            backgroundColor: Color.black,
            suggestedPrompts: [
                SuggestedPrompt(text: "Design a meaningful sleeve tattoo concept", icon: "paintbrush.pointed.fill", description: "Create a cohesive artistic narrative"),
                SuggestedPrompt(text: "What tattoo style would work best for this design?", icon: "eye.fill", description: "Match art style to your vision"),
                SuggestedPrompt(text: "Advise on tattoo placement and sizing considerations", icon: "ruler.fill", description: "Optimize design for body placement")
            ]
        ),
        Assistant(
            name: "Routine Planner",
            description: "Help organize your daily activities efficiently.",
            iconName: "calendar",
            backgroundColor: Color.blue,
            suggestedPrompts: [
                SuggestedPrompt(text: "Create an optimal morning routine for productivity", icon: "sunrise.fill", description: "Start your day with purpose"),
                SuggestedPrompt(text: "Design a time-blocking schedule for my busy week", icon: "clock.fill", description: "Maximize efficiency and focus"),
                SuggestedPrompt(text: "Help me build sustainable healthy habits", icon: "heart.circle.fill", description: "Create lasting positive changes")
            ]
        ),
        Assistant(
            name: "Interviewer",
            description: "Prepare for job interviews with expert advice.",
            iconName: "person.2.fill",
            backgroundColor: Color.gray,
            suggestedPrompts: [
                SuggestedPrompt(text: "Help me answer 'Tell me about yourself' professionally", icon: "person.circle.fill", description: "Craft a compelling personal pitch"),
                SuggestedPrompt(text: "Practice behavioral interview questions with examples", icon: "questionmark.bubble.fill", description: "Master the STAR method responses"),
                SuggestedPrompt(text: "What questions should I ask the interviewer?", icon: "bubble.left.and.bubble.right.fill", description: "Show engagement and interest")
            ]
        ),
        Assistant(
            name: "Wellness Advisor",
            description: "Tips for mental and physical well-being.",
            iconName: "heart.text.square.fill",
            backgroundColor: Color.green,
            suggestedPrompts: [
                SuggestedPrompt(text: "Create a stress management plan for my lifestyle", icon: "leaf.fill", description: "Build resilience and calm"),
                SuggestedPrompt(text: "Design a mindfulness routine for better mental health", icon: "brain.head.profile", description: "Cultivate inner peace and awareness"),
                SuggestedPrompt(text: "Help me establish better sleep hygiene habits", icon: "moon.fill", description: "Improve rest and recovery")
            ]
        ),
        Assistant(
            name: "Research Assistant",
            description: "Help with finding and analyzing information.",
            iconName: "magnifyingglass",
            backgroundColor: Color.blue,
            suggestedPrompts: [
                SuggestedPrompt(text: "Conduct a literature review on this academic topic", icon: "books.vertical.fill", description: "Synthesize scholarly research"),
                SuggestedPrompt(text: "Fact-check these claims and find credible sources", icon: "checkmark.shield.fill", description: "Verify information accuracy"),
                SuggestedPrompt(text: "Analyze trends and patterns in this dataset", icon: "chart.xyaxis.line", description: "Extract meaningful insights")
            ]
        ),
        Assistant(
            name: "Fitness & Health Coach",
            description: "Personalized workout and nutrition plans.",
            iconName: "figure.run",
            backgroundColor: Color.green,
            suggestedPrompts: [
                SuggestedPrompt(text: "Design a strength training program for my goals", icon: "dumbbell.fill", description: "Build muscle and power effectively"),
                SuggestedPrompt(text: "Create a meal prep plan for weight management", icon: "fork.knife.circle.fill", description: "Fuel your body optimally"),
                SuggestedPrompt(text: "Help me overcome this fitness plateau", icon: "arrow.up.circle.fill", description: "Break through training barriers")
            ]
        ),
        Assistant(
            name: "Travel Guide",
            description: "Plan your perfect trip with local insights.",
            iconName: "airplane",
            backgroundColor: Color.blue,
            suggestedPrompts: [
                SuggestedPrompt(text: "Plan a 10-day itinerary for Japan with hidden gems", icon: "map.fill", description: "Discover authentic local experiences"),
                SuggestedPrompt(text: "What's the best time to visit this destination?", icon: "calendar.circle.fill", description: "Optimize weather and crowds"),
                SuggestedPrompt(text: "Help me budget for this international trip", icon: "dollarsign.circle.fill", description: "Travel smart within your means")
            ]
        ),
        Assistant(
            name: "News Reporter",
            description: "Stay updated with the latest news and events.",
            iconName: "newspaper.fill",
            backgroundColor: Color.gray,
            suggestedPrompts: [
                SuggestedPrompt(text: "Analyze the bias and credibility of this news source", icon: "scale.3d", description: "Evaluate media objectivity"),
                SuggestedPrompt(text: "Summarize today's major global events", icon: "globe", description: "Stay informed on world affairs"),
                SuggestedPrompt(text: "Explain the context behind this breaking news story", icon: "exclamationmark.bubble.fill", description: "Understand the bigger picture")
            ]
        ),
        Assistant(
            name: "Accountant",
            description: "Financial record-keeping and tax advice.",
            iconName: "dollarsign.circle",
            backgroundColor: Color.green,
            suggestedPrompts: [
                SuggestedPrompt(text: "What business expenses can I legally deduct?", icon: "receipt.fill", description: "Maximize your tax savings"),
                SuggestedPrompt(text: "Set up a bookkeeping system for my small business", icon: "book.closed.fill", description: "Organize financial records efficiently"),
                SuggestedPrompt(text: "Explain this tax form and help me complete it", icon: "doc.text.fill", description: "Navigate tax compliance confidently")
            ]
        ),
        Assistant(
            name: "Artist",
            description: "Creative art ideas and techniques.",
            iconName: "paintbrush",
            backgroundColor: Color.purple,
            suggestedPrompts: [
                SuggestedPrompt(text: "Teach me advanced color theory for oil painting", icon: "paintpalette.fill", description: "Master professional color techniques"),
                SuggestedPrompt(text: "Critique my artwork and suggest improvements", icon: "eye.circle.fill", description: "Develop your artistic skills"),
                SuggestedPrompt(text: "What art style would best express this concept?", icon: "sparkles", description: "Find your unique artistic voice")
            ]
        ),
        Assistant(
            name: "Botanist",
            description: "Plant identification and care advice.",
            iconName: "leaf",
            backgroundColor: Color.green,
            suggestedPrompts: [
                SuggestedPrompt(text: "Identify this plant and its care requirements", icon: "magnifyingglass.circle.fill", description: "Discover plant species and needs"),
                SuggestedPrompt(text: "Diagnose why my houseplant is dying", icon: "cross.circle.fill", description: "Save your struggling plants"),
                SuggestedPrompt(text: "Design a garden ecosystem for my climate zone", icon: "tree.fill", description: "Create thriving plant communities")
            ]
        ),
        Assistant(
            name: "Career Advisor",
            description: "Professional development and career guidance.",
            iconName: "briefcase",
            backgroundColor: Color.blue,
            suggestedPrompts: [
                SuggestedPrompt(text: "Should I make this career transition at my age?", icon: "arrow.triangle.branch", description: "Navigate career change decisions"),
                SuggestedPrompt(text: "How can I negotiate a salary increase effectively?", icon: "arrow.up.circle.fill", description: "Advocate for your worth"),
                SuggestedPrompt(text: "Build a professional development plan for my field", icon: "chart.line.uptrend.xyaxis", description: "Advance your career strategically")
            ]
        ),
        Assistant(
            name: "Comedian",
            description: "Jokes, humor, and entertainment.",
            iconName: "theatermasks",
            backgroundColor: Color.orange,
            suggestedPrompts: [
                SuggestedPrompt(text: "Write a stand-up routine about modern technology", icon: "mic.fill", description: "Craft observational comedy gold"),
                SuggestedPrompt(text: "Help me roast my friend for their birthday", icon: "flame.fill", description: "Create playful, witty comebacks"),
                SuggestedPrompt(text: "What's a clever comeback to this situation?", icon: "bubble.left.and.bubble.right.fill", description: "Master the art of quick wit")
            ]
        ),
        Assistant(
            name: "Doctor",
            description: "General health information and advice.",
            iconName: "heart.text.square",
            backgroundColor: Color.red,
            suggestedPrompts: [
                SuggestedPrompt(text: "Explain these symptoms and when to see a doctor", icon: "stethoscope", description: "Understand health warning signs"),
                SuggestedPrompt(text: "What lifestyle changes can improve my condition?", icon: "heart.circle.fill", description: "Optimize your health naturally"),
                SuggestedPrompt(text: "Interpret my lab results in simple terms", icon: "doc.text.magnifyingglass", description: "Understand medical test results")
            ]
        ),
        Assistant(
            name: "Fashion Designer",
            description: "Style tips and clothing design ideas.",
            iconName: "tshirt",
            backgroundColor: Color.pink,
            suggestedPrompts: [
                SuggestedPrompt(text: "Design a capsule wardrobe for my lifestyle", icon: "tshirt.fill", description: "Create versatile, stylish outfits"),
                SuggestedPrompt(text: "What colors and styles flatter my body type?", icon: "paintpalette.fill", description: "Enhance your natural features"),
                SuggestedPrompt(text: "Help me style this outfit for a special occasion", icon: "sparkles", description: "Make a memorable impression")
            ]
        ),
        Assistant(
            name: "Spy",
            description: "Espionage and intelligence gathering techniques.",
            iconName: "eye",
            backgroundColor: Color.black,
            suggestedPrompts: [
                SuggestedPrompt(text: "Teach me advanced observation and surveillance skills", icon: "binoculars.fill", description: "Master situational awareness"),
                SuggestedPrompt(text: "How can I gather information without being detected?", icon: "eye.slash.fill", description: "Learn covert intelligence methods"),
                SuggestedPrompt(text: "Analyze this person's behavior for deception cues", icon: "person.crop.circle.badge.questionmark", description: "Read body language like a pro")
            ]
        ),
        Assistant(
            name: "Story Teller",
            description: "Engaging stories and narrative creation.",
            iconName: "book",
            backgroundColor: Color.brown,
            suggestedPrompts: [
                SuggestedPrompt(text: "Create an epic fantasy adventure with dragons", icon: "flame.circle.fill", description: "Craft immersive fantasy worlds"),
                SuggestedPrompt(text: "Tell me a mystery story with an unexpected twist", icon: "questionmark.circle.fill", description: "Master suspenseful storytelling"),
                SuggestedPrompt(text: "Help me develop compelling characters for my story", icon: "person.3.fill", description: "Bring characters to life")
            ]
        ),
        Assistant(
            name: "Tech Support",
            description: "Technical troubleshooting and device help.",
            iconName: "desktopcomputer",
            backgroundColor: Color.gray,
            suggestedPrompts: [
                SuggestedPrompt(text: "My computer is running slowly, how can I fix it?", icon: "speedometer", description: "Optimize system performance"),
                SuggestedPrompt(text: "Walk me through setting up this software securely", icon: "lock.shield.fill", description: "Configure technology safely"),
                SuggestedPrompt(text: "Troubleshoot this network connectivity issue", icon: "wifi.exclamationmark", description: "Restore internet connection")
            ]
        ),
        Assistant(
            name: "Veterinarian",
            description: "Pet health information and care advice.",
            iconName: "pawprint",
            backgroundColor: Color.green,
            suggestedPrompts: [
                SuggestedPrompt(text: "Is this behavior normal for my pet's breed and age?", icon: "questionmark.circle.fill", description: "Understand pet behavior patterns"),
                SuggestedPrompt(text: "Create a preventive health plan for my pet", icon: "heart.circle.fill", description: "Keep your pet healthy long-term"),
                SuggestedPrompt(text: "What emergency signs should I watch for?", icon: "exclamationmark.triangle.fill", description: "Recognize urgent health issues")
            ]
        )
    ]
}

// MARK: - Assistant Manager
class AssistantManager: ObservableObject {
    static let shared = AssistantManager()

    @Published var customAssistants: [Assistant] = []

    private let customAssistantsKey = "CustomAssistants"

    private init() {
        loadCustomAssistants()
    }

    var allAssistants: [Assistant] {
        return Assistant.assistants + customAssistants
    }

    func addCustomAssistant(_ assistant: Assistant) {
        customAssistants.append(assistant)
        saveCustomAssistants()
    }

    func removeCustomAssistant(withId id: UUID) {
        customAssistants.removeAll { $0.id == id }
        saveCustomAssistants()
    }

    private func saveCustomAssistants() {
        if let encoded = try? JSONEncoder().encode(customAssistants) {
            UserDefaults.standard.set(encoded, forKey: customAssistantsKey)
        }
    }

    private func loadCustomAssistants() {
        if let data = UserDefaults.standard.data(forKey: customAssistantsKey),
           let decoded = try? JSONDecoder().decode([Assistant].self, from: data) {
            customAssistants = decoded
        }
    }
}
