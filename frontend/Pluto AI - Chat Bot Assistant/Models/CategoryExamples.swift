//
//  CategoryExamples.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.05.01.
//

import Foundation
import SwiftUI

struct CategoryExamples {
    static let examples: [String: [String]] = [
        "E-Mail": [
            "Write a professional email to follow up on a job application",
            "Create a newsletter template for a monthly company update",
            "Draft a polite response to a customer complaint"
        ],
        "Business & Marketing": [
            "Create a marketing plan for a new mobile app",
            "Generate 5 catchy taglines for a fitness brand",
            "Write a compelling product description for an eco-friendly water bottle"
        ],
        "Astrology": [
            "What does it mean if I have multiple planets in Scorpio?",
            "How will the upcoming Mercury retrograde affect my Taurus sign?",
            "Compare compatibility between Libra and Aquarius"
        ],
        "Education": [
            "Explain quantum physics in simple terms",
            "Create a study plan for the SAT exam",
            "Help me understand the causes of World War I"
        ],
        "Art": [
            "Write a short story in the style of Edgar <PERSON>",
            "Create a poem about autumn using vivid imagery",
            "Suggest creative ideas for a mixed media art project"
        ],
        "Travel": [
            "Plan a 7-day itinerary for Tokyo, Japan",
            "What are the must-visit attractions in Barcelona?",
            "Create a packing list for a winter trip to Norway"
        ],
        "Daily Lifestyle": [
            "Suggest a weekly meal plan for a vegetarian diet",
            "How can I create a productive morning routine?",
            "Recommend exercises for improving posture while working from home"
        ],
        "Relationship": [
            "How to effectively communicate needs in a relationship",
            "Suggest fun date night ideas for a long-term couple",
            "Tips for resolving conflicts with a roommate"
        ],
        "Fun": [
            "Create a challenging riddle about time",
            "Tell me a funny joke about technology",
            "Suggest an interesting party game for 10 people"
        ],
        "Social": [
            "How to make a good impression at a networking event",
            "Write a thoughtful birthday message for a close friend",
            "Tips for improving public speaking skills"
        ],
        "Career": [
            "Help me update my resume for a marketing position",
            "Prepare answers for common job interview questions",
            "Suggest ways to ask for a promotion at work"
        ],
        "Health & Nutrition": [
            "Create a balanced meal plan for weight management",
            "Suggest exercises for strengthening core muscles",
            "What are the best foods for boosting immune system?"
        ],
        "Greetings": [
            "Write a heartfelt wedding congratulations message",
            "Create a thoughtful thank you note for a mentor",
            "Draft a sincere condolence message for a colleague"
        ]
    ]

    static func getExamples(for category: String) -> [String] {
        // Check if this is a tool category
        if Tool.eliteTools.contains(where: { $0.name == category }) {
            return [
                "How do I use the \(category) feature?",
                "What can I do with \(category)?",
                "Show me examples of \(category) in action"
            ]
        }

        // Check if this is an assistant category
        if category == "Create Assistant" {
            return [
                "How do I create a custom assistant?",
                "What types of assistants can I create?",
                "What are the steps to set up a new assistant?"
            ]
        } else if Assistant.assistants.contains(where: { $0.name == category }) {
            return [
                "What can the \(category) assistant help me with?",
                "Give me examples of tasks for the \(category) assistant",
                "What are the capabilities of the \(category) assistant?"
            ]
        }

        // Return examples from the predefined categories or default examples
        return examples[category] ?? [
            "Ask me anything about \(category)",
            "How can I learn more about \(category)?",
            "What are the best resources for \(category)?"
        ]
    }

    static func getIcon(for category: String) -> String {
        if let categoryObj = SuggestionCategory.categories.first(where: { $0.name == category }) {
            return categoryObj.icon
        }
        return "❓" // Use emoji question mark as fallback
    }

    static func getColor(for category: String) -> Color {
        if let categoryObj = SuggestionCategory.categories.first(where: { $0.name == category }) {
            return categoryObj.color
        }
        return .gray
    }
}
