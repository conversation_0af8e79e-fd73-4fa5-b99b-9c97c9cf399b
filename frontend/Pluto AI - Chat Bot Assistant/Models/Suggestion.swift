//
//  Suggestion.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.04.30.
//

import SwiftUI

struct SuggestionCategory: Identifiable {
    let id = UUID()
    let name: String
    let icon: String
    let color: Color
    let systemMessage: String
    let description: String

    static let categories: [SuggestionCategory] = [
        SuggestionCategory(
            name: "E-Mail",
            icon: "📧",
            color: .gray,
            systemMessage: "You are an expert email writer and communication specialist. Help the user craft professional, effective emails for various purposes. Provide clear, concise, and well-structured email content that achieves the user's goals.",
            description: "Master professional email communication with expert guidance for all your messaging needs."
        ),
        SuggestionCategory(
            name: "Business & Marketing",
            icon: "💼",
            color: .blue,
            systemMessage: "You are a business and marketing expert with extensive knowledge of strategy, market analysis, and growth tactics. Help the user with business plans, marketing strategies, and professional advice to achieve their business goals.",
            description: "Accelerate your business growth with strategic marketing insights and professional guidance."
        ),
        SuggestionCategory(
            name: "Astrology",
            icon: "✨",
            color: .purple,
            systemMessage: "You are an astrology expert with deep knowledge of zodiac signs, horoscopes, and celestial influences. Provide insightful interpretations and guidance based on astrological principles while acknowledging that astrology is for entertainment purposes.",
            description: "Discover cosmic insights and celestial guidance for your life's journey through the stars."
        ),
        SuggestionCategory(
            name: "Education",
            icon: "📚",
            color: .orange,
            systemMessage: "You are an educational expert and tutor with knowledge across multiple subjects. Help the user learn concepts, solve problems, and develop their understanding in a clear, patient, and supportive manner.",
            description: "Unlock your learning potential with personalized tutoring across all academic subjects."
        ),
        SuggestionCategory(
            name: "Art",
            icon: "🎨",
            color: .pink,
            systemMessage: "You are a creative arts expert with knowledge of literature, music, visual arts, and creative writing. Help the user explore their creativity, develop artistic ideas, and appreciate various art forms.",
            description: "Unleash your creativity with expert guidance in literature, music, and visual arts."
        ),
        SuggestionCategory(
            name: "Travel",
            icon: "✈️",
            color: .cyan,
            systemMessage: "You are a travel expert with global knowledge of destinations, cultures, and travel planning. Provide helpful advice on itineraries, local attractions, cultural norms, and travel logistics to enhance the user's travel experiences.",
            description: "Explore the world with expert travel planning and cultural insights for unforgettable adventures."
        ),
        SuggestionCategory(
            name: "Daily Lifestyle",
            icon: "🌞",
            color: .green,
            systemMessage: "You are a lifestyle consultant with expertise in daily routines, personal organization, and balanced living. Provide practical advice to help the user improve their everyday life, habits, and overall wellbeing.",
            description: "Optimize your daily life with practical tips for wellness, organization, and balanced living."
        ),
        SuggestionCategory(
            name: "Relationship",
            icon: "❤️",
            color: .red,
            systemMessage: "You are a relationship counselor with expertise in interpersonal dynamics, communication, and emotional intelligence. Provide thoughtful advice to help the user navigate their relationships in a healthy, respectful manner.",
            description: "Strengthen your connections with expert relationship guidance and communication strategies."
        ),
        SuggestionCategory(
            name: "Fun",
            icon: "🎮",
            color: .orange,
            systemMessage: "You are an entertainment specialist focused on providing fun, engaging, and lighthearted interactions. Your goal is to entertain the user with creative content, games, stories, and playful conversations.",
            description: "Enjoy entertaining conversations, games, and creative content for pure fun and relaxation."
        ),
        SuggestionCategory(
            name: "Social",
            icon: "👥",
            color: .blue,
            systemMessage: "You are a social interaction expert with knowledge of etiquette, communication skills, and social dynamics. Help the user navigate social situations, improve their social skills, and build meaningful connections.",
            description: "Master social interactions with expert guidance on communication and relationship building."
        ),
        SuggestionCategory(
            name: "Career",
            icon: "💼",
            color: .gray,
            systemMessage: "You are a career counselor with expertise in professional development, job searching, and workplace success. Provide guidance to help the user advance their career, improve their skills, and achieve their professional goals.",
            description: "Advance your professional journey with career guidance and workplace success strategies."
        ),
        SuggestionCategory(
            name: "Health & Nutrition",
            icon: "🥗",
            color: .green,
            systemMessage: "You are a health and wellness advisor with knowledge of nutrition, fitness, and general wellbeing. Provide informative guidance while emphasizing that you're not a medical professional and serious health concerns should be directed to healthcare providers.",
            description: "Improve your wellbeing with expert advice on nutrition, fitness, and healthy living."
        ),
        SuggestionCategory(
            name: "Greetings",
            icon: "🎁",
            color: .red,
            systemMessage: "You are a specialist in creating heartfelt messages and greetings for special occasions. Help the user craft meaningful, personalized messages that convey their sentiments for celebrations, holidays, and important life events.",
            description: "Create heartfelt messages and greetings for every special occasion and celebration."
        )
    ]

    // Get system message for a category by name
    static func getSystemMessage(for categoryName: String) -> String {
        // Check if this is a predefined category
        if let category = categories.first(where: { $0.name == categoryName }) {
            return category.systemMessage
        }

        // Check if this is a tool category
        if let tool = Tool.eliteTools.first(where: { $0.name == categoryName }) {
            return "You are an expert on the \(tool.name) feature. Provide detailed information about how to use this tool, its capabilities, limitations, and best practices. Offer specific examples and step-by-step instructions when appropriate."
        }

        // Check if this is an assistant category
        if categoryName == "Create Assistant" {
            return "You are an expert on creating custom assistants. Provide detailed guidance on how to create, configure, and optimize assistants for different purposes. Explain the available options, best practices, and offer examples of effective assistant configurations."
        } else if let assistant = Assistant.assistants.first(where: { $0.name == categoryName }) {
            return "You are the \(assistant.name) assistant. Your expertise is in \(assistant.description). Provide specialized assistance in this domain, offering detailed, accurate, and helpful responses tailored to the user's specific needs."
        }

        // Default system message
        return "You are a helpful AI assistant. Provide informative and thoughtful responses to the user's questions."
    }
}

struct Suggestion: Identifiable {
    let id = UUID()
    let text: String
    let icon: String
    let category: String
    let examplePrompts: [String]

    // Get the system message for this suggestion based on its category
    func getSystemMessage() -> String {
        return SuggestionCategory.getSystemMessage(for: category)
    }

    static let emailSuggestions: [Suggestion] = [
        Suggestion(text: "Write an email to promote the sale", icon: "📧", category: "E-Mail", examplePrompts: [
            "📢 Write a promotional email for a 50% off Black Friday sale on electronics 💻⚡",
            "⏰ Create a flash sale email for summer clothing with 24-hour urgency 👕🌞",
            "🚀 Draft a promotional email for a new product launch with early bird discount 🐦💰"
        ]),
        Suggestion(text: "Newsletter template", icon: "📰", category: "E-Mail", examplePrompts: [
            "📰 Create a monthly company newsletter template with sections for news, updates, and employee highlights 👥✨",
            "💪 Design a weekly newsletter template for a fitness blog with workout tips and nutrition advice 🥗",
            "❤️ Build a quarterly newsletter template for a nonprofit organization showcasing impact and donor stories 🌟"
        ]),
        Suggestion(text: "Mail response for angry clients", icon: "😡", category: "E-Mail", examplePrompts: [
            "😤 Write a professional response to a customer complaining about delayed delivery 📦⏰",
            "💸 Draft an apology email for a billing error that caused customer frustration 😓",
            "🔧 Create a response to a client upset about poor service quality during their recent visit 💔"
        ]),
        Suggestion(text: "Email subject lines for high open rates", icon: "🔓", category: "E-Mail", examplePrompts: [
            "🎯 Generate 10 compelling subject lines for a marketing email about a new software tool 💻",
            "🎪 Create attention-grabbing subject lines for a webinar invitation email 📹",
            "💌 Write persuasive subject lines for a re-engagement email campaign to inactive subscribers 🔄"
        ]),
        Suggestion(text: "Mass marketing email", icon: "📢", category: "E-Mail", examplePrompts: [
            "🎉 Create a mass marketing email for a new restaurant's grand opening 🍽️",
            "🏠 Write a bulk email campaign for a real estate agent promoting new listings 🔑",
            "💪 Draft a mass email for a fitness center's membership drive with special offers 🏋️‍♀️"
        ]),
        Suggestion(text: "Text formalizer prettifier and fixer", icon: "✨", category: "E-Mail", examplePrompts: [
            "✨ Make this casual message professional: 'hey can u send me the report asap thx' 📊",
            "💼 Formalize this text: 'sup boss, the meeting went ok but we need 2 talk about budget stuff' 💰",
            "📝 Polish this email draft: 'hi there, just wanted to check in about the project and see how things r going' 🔄"
        ]),
        Suggestion(text: "Email responder (friendly/professional)", icon: "💌", category: "E-Mail", examplePrompts: [
            "🤝 Write a friendly response to a colleague thanking them for their help on a project 🙏",
            "💼 Create a professional reply to a client's inquiry about pricing and services 💰",
            "😊 Draft a warm response to a job interview invitation confirming availability 📅"
        ])
    ]

    static let businessSuggestions: [Suggestion] = [
        Suggestion(text: "E-Mail generator", icon: "📩", category: "Business & Marketing", examplePrompts: [
            "🤝 Write a professional follow-up email after a job interview 💼",
            "❄️ Create a cold outreach email to potential clients for my consulting business 📈",
            "😓 Draft an apology email to a customer for a delayed shipment 📦"
        ]),
        Suggestion(text: "Social media manager", icon: "📱", category: "Business & Marketing", examplePrompts: [
            "🥐 Create a week's worth of Instagram posts for a local bakery 📸",
            "🚀 Generate engaging LinkedIn content for a tech startup's company page 💻",
            "💪 Plan a social media campaign for a new fitness app launch 📱"
        ]),
        Suggestion(text: "Business Idea", icon: "💡", category: "Business & Marketing", examplePrompts: [
            "🌱 Generate 5 innovative business ideas for the sustainable living market ♻️",
            "💰 Suggest profitable online business ideas that require minimal startup capital 🚀",
            "🏠 Create unique business concepts that solve common problems for remote workers 💻"
        ]),
        Suggestion(text: "Digital Marketing Strategy", icon: "📊", category: "Business & Marketing", examplePrompts: [
            "🛒 Develop a comprehensive digital marketing strategy for a new e-commerce store 📈",
            "🍽️ Create a social media marketing plan for a local restaurant to increase foot traffic 👥",
            "💼 Design a content marketing strategy for a B2B software company 🚀"
        ]),
        Suggestion(text: "SEO generator", icon: "🔍", category: "Business & Marketing", examplePrompts: [
            "✈️ Generate SEO-optimized blog post titles for a travel website 🌍",
            "👕 Create meta descriptions for an online clothing store's product pages 🛍️",
            "🦷 Write SEO-friendly content for a dental practice's website homepage 😁"
        ]),
        Suggestion(text: "Slide presentation", icon: "📑", category: "Business & Marketing", examplePrompts: [
            "📱 Create a 10-slide pitch deck for a mobile app startup seeking investors 💰",
            "📊 Design a quarterly business review presentation for company stakeholders 👔",
            "💄 Build a product launch presentation for a new skincare line ✨"
        ]),
        Suggestion(text: "Prepare a professional business plan", icon: "📝", category: "Business & Marketing", examplePrompts: [
            "🍔 Write a business plan for a food truck specializing in gourmet burgers 🚚",
            "📚 Create a comprehensive business plan for an online tutoring platform 💻",
            "👗 Develop a business plan for a sustainable fashion boutique 🌱"
        ]),
        Suggestion(text: "All-in-one marketing", icon: "📢", category: "Business & Marketing", examplePrompts: [
            "🧘‍♀️ Create a complete marketing campaign for a new yoga studio opening 🌟",
            "💻 Design an integrated marketing strategy for a tech conference event 🎪",
            "📦 Develop a full marketing plan for launching a subscription box service 🎁"
        ]),
        Suggestion(text: "Social media caption generator", icon: "💬", category: "Business & Marketing", examplePrompts: [
            "☕ Write engaging Instagram captions for a coffee shop's daily posts 📸",
            "💼 Create professional LinkedIn captions for a marketing agency's case studies 📊",
            "🐕 Generate fun TikTok captions for a pet grooming business 🎥"
        ]),
        Suggestion(text: "Make $100 a day", icon: "💰", category: "Business & Marketing", examplePrompts: [
            "💻 Suggest 5 legitimate ways to earn $100 daily through online freelancing 🚀",
            "🎨 Create a plan to make $100 per day selling handmade crafts online 🛍️",
            "📈 Outline strategies to earn $100 daily through affiliate marketing 💰"
        ]),
        Suggestion(text: "CEO (Virtual CEO Consultant)", icon: "👔", category: "Business & Marketing", examplePrompts: [
            "🌍 Help me make a strategic decision about expanding my business to new markets 📊",
            "👥 Advise on how to improve team productivity and company culture 🚀",
            "🔄 Guide me through planning a major business pivot during economic uncertainty 💡"
        ]),
        Suggestion(text: "Business Tax Advisor", icon: "📋", category: "Business & Marketing", examplePrompts: [
            "🏠 Explain tax deductions available for a home-based consulting business 💼",
            "📅 Help me understand quarterly tax payments for my freelance income 💰",
            "👥 Advise on tax implications of hiring my first employee for my small business 📊"
        ])
    ]

    static let astrologySuggestions: [Suggestion] = [
        Suggestion(text: "Daily Horoscope / Love, Money, Mood, Health", icon: "✨", category: "Astrology", examplePrompts: [
            "🦂 Give me today's horoscope for Scorpio focusing on love and relationships 💕",
            "⚖️ What does today hold for a Libra in terms of money and career opportunities? 💰",
            "🐐 Provide a daily horoscope for Capricorn covering mood and health insights 🌟"
        ]),
        Suggestion(text: "What are 10 characteristic features of my zodiac sign?", icon: "👤", category: "Astrology", examplePrompts: [
            "🦁 Tell me 10 key personality traits of a Leo and how they show up in daily life ✨",
            "🐟 What are the main characteristics of a Pisces and their strengths and weaknesses? 🌊",
            "♍ Describe 10 defining features of a Virgo's personality and behavior patterns 📋"
        ]),
        Suggestion(text: "Interpret your horoscope map", icon: "🗺️", category: "Astrology", examplePrompts: [
            "🏹 I'm a Sagittarius with Gemini rising and Moon in Cancer - interpret my chart 🌙",
            "🐂 Help me understand my birth chart: Taurus sun, Scorpio moon, Leo rising ⭐",
            "🏺 Analyze my astrological profile: Aquarius sun, Virgo moon, Capricorn rising 🌟"
        ]),
        Suggestion(text: "Tarot analysis", icon: "🔮", category: "Astrology", examplePrompts: [
            "🗼 I drew The Tower, Three of Cups, and The Star - what do these cards mean together? ⭐",
            "💕 Interpret a love reading with The Lovers, Two of Cups, and Ten of Pentacles 💰",
            "🧙‍♂️ What does it mean if I keep drawing The Hermit card in my daily readings? 🔮"
        ]),
        Suggestion(text: "Weekly horoscope", icon: "📅", category: "Astrology", examplePrompts: [
            "🦀 Give me this week's horoscope for Cancer with focus on career and relationships 💼💕",
            "🐏 What should an Aries expect this week in love, work, and personal growth? 🌱",
            "👯‍♂️ Provide a weekly forecast for Gemini covering all major life areas ✨"
        ]),
        Suggestion(text: "Music & movie to match with your Zodiac sign", icon: "🎵", category: "Astrology", examplePrompts: [
            "🦂 Recommend movies and music that perfectly match a Scorpio's intense personality 🎬🎵",
            "🐟 What songs and films would resonate with a dreamy Pisces nature? 🌊✨",
            "🦁 Suggest entertainment that aligns with a confident Leo's energy and style 👑🎭"
        ]),
        Suggestion(text: "Is an aries woman and a gemini man a good match?", icon: "❤️", category: "Astrology", examplePrompts: [
            "🐂 Analyze the compatibility between a Taurus woman and a Sagittarius man 🏹💕",
            "🦀 How well do Cancer and Capricorn match in a romantic relationship? 🐐❤️",
            "♍ What are the strengths and challenges of a Virgo-Aquarius partnership? 🏺💫"
        ])
    ]

    static let educationSuggestions: [Suggestion] = [
        Suggestion(text: "Science chat", icon: "⚛️", category: "Education", examplePrompts: [
            "⚛️ Explain quantum physics concepts in simple terms for a high school student 🎓",
            "🌱 Help me understand how photosynthesis works at the molecular level 🔬",
            "🚀 Discuss the latest discoveries in space exploration and their significance 🌌"
        ]),
        Suggestion(text: "English Teacher", icon: "📚", category: "Education", examplePrompts: [
            "🎭 Help me analyze the themes and symbolism in Shakespeare's Macbeth 👑",
            "📝 Explain the difference between active and passive voice with examples ✍️",
            "🌍 Guide me through writing a compelling argumentative essay about climate change 🌱"
        ]),
        Suggestion(text: "Translator", icon: "🌎", category: "Education", examplePrompts: [
            "💼 Translate this business email from English to Spanish while maintaining professional tone 🇪🇸",
            "🎨 Help me translate French poetry into English while preserving the artistic meaning 🇫🇷",
            "🔧 Convert this technical manual from German to English with accurate terminology 🇩🇪"
        ]),
        Suggestion(text: "Math Teacher", icon: "🧮", category: "Education", examplePrompts: [
            "📈 Explain calculus derivatives step-by-step with real-world applications 🌍",
            "💰 Help me solve this algebra word problem about compound interest 📊",
            "🎲 Teach me how to calculate probability in statistics with practical examples 📈"
        ]),
        Suggestion(text: "Create a short essay on any topic", icon: "📄", category: "Education", examplePrompts: [
            "📱 Write a 500-word essay on the impact of social media on modern communication 💬",
            "🌱 Create an essay about the importance of renewable energy for our future ⚡",
            "🧠 Compose a persuasive essay on why critical thinking should be taught in schools 🎓"
        ]),
        Suggestion(text: "Citation Generator for any style", icon: "💭", category: "Education", examplePrompts: [
            "📚 Generate APA citations for a research paper using online articles and books 💻",
            "📖 Create MLA format citations for my literature review on modern poetry ✍️",
            "📜 Help me format Chicago style citations for my history thesis sources 🏛️"
        ]),
        Suggestion(text: "Course Generator on any Topic", icon: "📕", category: "Education", examplePrompts: [
            "📸 Design a 4-week online course curriculum for learning digital photography 🎨",
            "🐍 Create a comprehensive course outline for teaching Python programming to beginners 💻",
            "🇪🇸 Develop a structured course plan for learning Spanish conversation skills 💬"
        ])
    ]

    static let artSuggestions: [Suggestion] = [
        Suggestion(text: "Write J. K. Rowling-style short story", icon: "🧙‍♂️", category: "Art", examplePrompts: [
            "Write a magical short story about a student discovering a hidden room at Hogwarts",
            "Create a tale about a young wizard learning their first advanced spell",
            "Craft a story about magical creatures living secretly in modern London"
        ]),
        Suggestion(text: "Write Travis Scott-style song lyrics", icon: "🎤", category: "Art", examplePrompts: [
            "Write rap lyrics about overcoming struggles and achieving success in Travis Scott's style",
            "Create a song about late-night city life with Travis Scott's energy and flow",
            "Compose lyrics about chasing dreams and ambition with his signature sound"
        ]),
        Suggestion(text: "Create a playlist similar to your favorite song", icon: "🎵", category: "Art", examplePrompts: [
            "Build a playlist similar to 'Bohemian Rhapsody' with epic, theatrical rock songs",
            "Create a chill playlist inspired by 'Blinding Lights' with synthwave and retro vibes",
            "Make a workout playlist based on 'Eye of the Tiger' with motivational rock anthems"
        ]),
        Suggestion(text: "Storyteller", icon: "📖", category: "Art", examplePrompts: [
            "Tell me an engaging bedtime story about a brave little mouse on an adventure",
            "Create a thrilling mystery story set in a haunted Victorian mansion",
            "Narrate an inspiring tale about friendship and courage during difficult times"
        ]),
        Suggestion(text: "Book recommendations", icon: "📚", category: "Art", examplePrompts: [
            "Recommend 5 sci-fi books for someone who loved 'Dune' and 'Foundation'",
            "Suggest mystery novels similar to Agatha Christie but with modern settings",
            "Find me fantasy books with strong female protagonists like in 'The Hunger Games'"
        ]),
        Suggestion(text: "Poem generator", icon: "🖋️", category: "Art", examplePrompts: [
            "Write a romantic sonnet about autumn leaves and changing seasons",
            "Create a powerful spoken word poem about social justice and equality",
            "Compose a haiku series capturing the beauty of a sunrise over mountains"
        ]),
        Suggestion(text: "Movie and series critic", icon: "🎬", category: "Art", examplePrompts: [
            "Write a detailed review of 'The Last of Us' TV series analyzing its storytelling",
            "Critique the cinematography and themes in 'Dune' (2021) movie",
            "Analyze the character development in 'Breaking Bad' across all seasons"
        ]),
        Suggestion(text: "Song recommendation match for your mood and genre", icon: "🎧", category: "Art", examplePrompts: [
            "Recommend upbeat indie pop songs for a productive morning work session",
            "Suggest melancholic alternative rock for a rainy evening mood",
            "Find energetic electronic dance music perfect for a weekend party"
        ]),
        Suggestion(text: "Write a South Park episode", icon: "📺", category: "Art", examplePrompts: [
            "Create a South Park episode where the boys discover social media influencing",
            "Write an episode about Stan and Kyle dealing with cryptocurrency trends",
            "Craft a story where Cartman starts his own podcast and chaos ensues"
        ])
    ]

    static let travelSuggestions: [Suggestion] = [
        Suggestion(text: "Vacation planner", icon: "✈️", category: "Travel", examplePrompts: [
            "Plan a 7-day romantic getaway to Paris for a couple on a moderate budget",
            "Create a family-friendly vacation itinerary for Disney World with kids ages 5-12",
            "Design a solo backpacking adventure through Southeast Asia for 3 weeks"
        ]),
        Suggestion(text: "Local foods", icon: "🍽️", category: "Travel", examplePrompts: [
            "What are the must-try local dishes when visiting Tokyo, Japan?",
            "Recommend authentic street food experiences in Bangkok, Thailand",
            "Guide me to the best traditional cuisine in Rome, Italy"
        ]),
        Suggestion(text: "Best time to visit", icon: "📅", category: "Travel", examplePrompts: [
            "When is the ideal time to visit Iceland for Northern Lights and good weather?",
            "What's the best season to travel to India to avoid monsoons but enjoy culture?",
            "When should I visit New Zealand for the best hiking and outdoor activities?"
        ]),
        Suggestion(text: "Activities", icon: "🧗‍♀️", category: "Travel", examplePrompts: [
            "Suggest adventure activities for thrill-seekers visiting Costa Rica",
            "What are the top cultural experiences to have in Morocco?",
            "Recommend outdoor activities for nature lovers visiting Norway"
        ]),
        Suggestion(text: "Budgeting tips", icon: "💲", category: "Travel", examplePrompts: [
            "How can I travel through Europe on a $50 per day budget?",
            "Share money-saving tips for a family vacation to Hawaii",
            "What are the best ways to travel cheaply through South America?"
        ]),
        Suggestion(text: "Prepare Itinerary", icon: "📋", category: "Travel", examplePrompts: [
            "Create a detailed 10-day itinerary for exploring the Greek islands",
            "Plan a weekend city break itinerary for Barcelona with must-see attractions",
            "Design a 2-week road trip itinerary through the American Southwest"
        ]),
        Suggestion(text: "Cultural Legal Advisor For Safe Travels", icon: "🏛️", category: "Travel", examplePrompts: [
            "What cultural customs and laws should I know before visiting Saudi Arabia?",
            "Advise me on cultural etiquette and legal considerations for traveling in China",
            "What are important cultural norms and legal requirements for visiting India?"
        ]),
        Suggestion(text: "Time - Travel Machine", icon: "⏰", category: "Travel", examplePrompts: [
            "If I could time travel, take me on a virtual tour of ancient Rome at its peak",
            "Describe what it would be like to visit Paris during the Renaissance period",
            "Transport me to experience the Wild West in 1880s America"
        ])
    ]

    static let lifestyleSuggestions: [Suggestion] = [
        Suggestion(text: "Daily Horoscope / Love, Money, Mood, Health", icon: "✨", category: "Daily Lifestyle", examplePrompts: [
            "Give me today's horoscope for Virgo focusing on health and wellness",
            "What does today hold for a Cancer in terms of love and emotional well-being?",
            "Provide daily guidance for a Sagittarius about money and career decisions"
        ]),
        Suggestion(text: "Outfit Idea / harmonious with event concepts", icon: "👕", category: "Daily Lifestyle", examplePrompts: [
            "Suggest a stylish outfit for a casual coffee date that's comfortable yet attractive",
            "What should I wear to a business networking event that's professional but memorable?",
            "Help me choose an outfit for a weekend brunch with friends that's trendy and fun"
        ]),
        Suggestion(text: "How many liters of water should I drink in a day?", icon: "💧", category: "Daily Lifestyle", examplePrompts: [
            "Calculate my daily water intake needs based on my weight, activity level, and climate",
            "How much water should I drink if I exercise for 1 hour daily and weigh 150 pounds?",
            "What's the recommended water intake for someone working in an air-conditioned office?"
        ]),
        Suggestion(text: "Make-Up Idea compatible with concept and outfit", icon: "💄", category: "Daily Lifestyle", examplePrompts: [
            "Create a natural makeup look that complements a floral summer dress for daytime",
            "Suggest bold makeup ideas for a black cocktail dress at an evening party",
            "Design a professional makeup look for a job interview with a navy blue suit"
        ]),
        Suggestion(text: "Meal idea with ingredients and instructions", icon: "🍲", category: "Daily Lifestyle", examplePrompts: [
            "Give me a healthy 30-minute dinner recipe using chicken, vegetables, and rice",
            "Suggest a vegetarian breakfast that's high in protein and easy to prepare",
            "Create a quick lunch recipe using ingredients I likely have in my pantry"
        ]),
        Suggestion(text: "Meal order idea for breakfast, lunch, dinner, and night snacks", icon: "🥡", category: "Daily Lifestyle", examplePrompts: [
            "Plan a full day of healthy meals for someone trying to lose weight",
            "Create a meal schedule for a busy professional who wants nutritious, quick options",
            "Design a day of comfort food meals that are still relatively healthy"
        ]),
        Suggestion(text: "Jovial mentor wisdom for lives queries", icon: "🧠", category: "Daily Lifestyle", examplePrompts: [
            "I'm feeling overwhelmed with work and life balance - give me some wise guidance",
            "How can I build more confidence and overcome self-doubt in my daily life?",
            "Share wisdom about finding purpose and meaning when feeling lost in life"
        ])
    ]

    static let relationshipSuggestions: [Suggestion] = [
        Suggestion(text: "Dating Tips", icon: "❤️", category: "Relationship", examplePrompts: [
            "Give me conversation starters for a first date at a coffee shop",
            "How can I show genuine interest without appearing too eager when dating?",
            "What are some red flags to watch for when getting to know someone new?"
        ]),
        Suggestion(text: "Relationship Therapist", icon: "👫", category: "Relationship", examplePrompts: [
            "My partner and I keep arguing about money - how can we communicate better?",
            "We're struggling with intimacy issues after 5 years together - what should we do?",
            "How can we rebuild trust after a betrayal in our relationship?"
        ]),
        Suggestion(text: "Sex Therapist", icon: "💕", category: "Relationship", examplePrompts: [
            "How can couples improve communication about their intimate needs and desires?",
            "What are healthy ways to address mismatched libidos in a long-term relationship?",
            "How can we reignite passion and intimacy after becoming parents?"
        ]),
        Suggestion(text: "Outfit Advisor for Upcoming Date", icon: "👔", category: "Relationship", examplePrompts: [
            "What should I wear for a dinner date at an upscale restaurant?",
            "Help me choose an outfit for a casual outdoor picnic date",
            "Suggest what to wear for a movie and drinks first date"
        ]),
        Suggestion(text: "Game Generator for Couples", icon: "🎮", category: "Relationship", examplePrompts: [
            "Create fun conversation games for a couple on a long car ride",
            "Suggest romantic games we can play at home for date night",
            "Give me creative challenge games to help us learn more about each other"
        ]),
        Suggestion(text: "Calculation of Relationship Score", icon: "💯", category: "Relationship", examplePrompts: [
            "Assess our relationship compatibility based on communication, values, and goals",
            "Help me evaluate if my 2-year relationship is healthy and has long-term potential",
            "Rate our relationship strength considering trust, intimacy, and shared interests"
        ]),
        Suggestion(text: "Deep Question to Ask to Partner", icon: "❓", category: "Relationship", examplePrompts: [
            "Give me meaningful questions to ask my partner to deepen our emotional connection",
            "Suggest thought-provoking questions for couples who want to know each other better",
            "What are some deep questions to ask before moving in together?"
        ]),
        Suggestion(text: "Is My Relationship Healthy Or Not", icon: "💔", category: "Relationship", examplePrompts: [
            "Help me identify if my relationship has healthy communication patterns",
            "What are signs that my relationship might be toxic or unhealthy?",
            "Evaluate whether my partner's behavior shows respect and genuine care"
        ])
    ]

    static let funSuggestions: [Suggestion] = [
        Suggestion(text: "Astrology", icon: "🌟", category: "Fun", examplePrompts: [
            "👯‍♂️ Tell me something fun and quirky about being a Gemini ✨",
            "🎉 What would happen if all zodiac signs had a party together? 🌟",
            "💇‍♀️ Create a funny horoscope for someone having a bad hair day 😅"
        ]),
        Suggestion(text: "Dream interpreter", icon: "💭", category: "Fun", examplePrompts: [
            "🍫 I dreamed I was flying over a city made of chocolate - what does this mean? ✈️",
            "🐾 Interpret my weird dream about talking animals giving me life advice 🦉",
            "📚 What does it mean when I dream about being late for an exam I never studied for? ⏰"
        ]),
        Suggestion(text: "Turn any text into emoji", icon: "😊", category: "Fun", examplePrompts: [
            "🍕 Convert this sentence to emojis: 'I love pizza and watching movies on rainy days' 🎬🌧️",
            "💪 Turn 'Going to the gym then grabbing coffee with friends' into emoji story ☕👥",
            "🎂 Transform 'Happy birthday, hope your day is amazing!' into pure emojis ✨"
        ]),
        Suggestion(text: "Storyteller", icon: "📚", category: "Fun", examplePrompts: [
            "Tell me a funny story about a cat who thinks it's a dog",
            "Create an adventure tale about a pizza delivery person who saves the world",
            "Narrate a silly story about what happens when socks go missing in the dryer"
        ]),
        Suggestion(text: "Song recommender", icon: "🎵", category: "Fun", examplePrompts: [
            "Recommend songs that would make a great soundtrack for doing chores",
            "Suggest music for a road trip with friends who love singing along",
            "Find songs that perfectly capture the feeling of Friday afternoon"
        ]),
        Suggestion(text: "Give advice like Elon Musk about cars", icon: "🚗", category: "Fun", examplePrompts: [
            "What would Elon Musk say about choosing between a Tesla and a traditional car?",
            "Channel Elon Musk giving advice about the future of transportation",
            "How would Elon Musk explain why electric cars are better for road trips?"
        ]),
        Suggestion(text: "Kyle in South Park", icon: "📺", category: "Fun", examplePrompts: [
            "What would Kyle Broflovski say about social media influencers?",
            "How would Kyle react to discovering his mom started a TikTok account?",
            "Channel Kyle's voice complaining about modern dating apps"
        ]),
        Suggestion(text: "Play Chess", icon: "♟️", category: "Fun", examplePrompts: [
            "Teach me a fun chess opening strategy that beginners can master",
            "Let's play a quick chess game - you make the first move and explain your strategy",
            "Explain chess tactics using funny analogies and memorable examples"
        ]),
        Suggestion(text: "Play Trivia Quest", icon: "🎯", category: "Fun", examplePrompts: [
            "Give me 5 fun trivia questions about 90s pop culture",
            "Create a trivia challenge about weird animal facts",
            "Quiz me on random interesting facts about space and science"
        ]),
        Suggestion(text: "Minecraft Steve", icon: "🧱", category: "Fun", examplePrompts: [
            "What would Steve from Minecraft say about building the perfect house?",
            "Channel Minecraft Steve giving survival tips for the real world",
            "How would Steve react to discovering a new biome in his world?"
        ]),
        Suggestion(text: "Eminem-style Jokes about Max Payne", icon: "🎤", category: "Fun", examplePrompts: [
            "Create Eminem-style rap lyrics making fun of Max Payne's dramatic monologues",
            "Write a funny rap about Max Payne's bullet-time abilities in Eminem's style",
            "Channel Eminem roasting Max Payne's fashion choices and attitude"
        ]),
        Suggestion(text: "Son Goku in Dragon Ball", icon: "⚡", category: "Fun", examplePrompts: [
            "What would Goku say about trying to order food at a fancy restaurant?",
            "How would Goku react to discovering social media and smartphones?",
            "Channel Goku giving motivational advice about working out and staying positive"
        ])
    ]

    static let socialSuggestions: [Suggestion] = [
        Suggestion(text: "Gift advice", icon: "🎁", category: "Social", examplePrompts: [
            "Suggest thoughtful gifts for my best friend who loves cooking and travel",
            "What's a meaningful gift for my mom's 60th birthday that shows I care?",
            "Help me find the perfect anniversary gift for my partner who has everything"
        ]),
        Suggestion(text: "Event suggestion", icon: "📆", category: "Social", examplePrompts: [
            "Plan a fun birthday party for a 25-year-old who loves board games",
            "Suggest creative ideas for a housewarming party on a budget",
            "Help me organize a memorable graduation celebration for my daughter"
        ]),
        Suggestion(text: "Win someone's heart on a dating app", icon: "💘", category: "Social", examplePrompts: [
            "Write a charming opening message for someone who loves hiking and photography",
            "Help me create an attractive dating profile that shows my personality",
            "Suggest conversation starters that lead to meaningful connections on dating apps"
        ]),
        Suggestion(text: "Personal stylist", icon: "👔", category: "Social", examplePrompts: [
            "Help me build a versatile wardrobe for a young professional on a budget",
            "Suggest a style makeover for someone who wants to look more confident",
            "What clothing styles would suit my body type and lifestyle?"
        ]),
        Suggestion(text: "Outfit Idea (Harmonious with Event Concept)", icon: "👗", category: "Social", examplePrompts: [
            "What should I wear to a garden wedding as a guest in spring?",
            "Suggest an outfit for a casual work happy hour that's professional yet fun",
            "Help me choose what to wear to a friend's art gallery opening"
        ]),
        Suggestion(text: "New Topic to Open a Conversation", icon: "💬", category: "Social", examplePrompts: [
            "Give me interesting conversation starters for a networking event",
            "Suggest topics to discuss with new neighbors at a community gathering",
            "What are good conversation topics for a first meeting with my partner's parents?"
        ]),
        Suggestion(text: "Birthday Message", icon: "🎂", category: "Social", examplePrompts: [
            "Write a heartfelt birthday message for my childhood friend who lives far away",
            "Create a funny birthday wish for my coworker who loves dad jokes",
            "Help me write a meaningful birthday message for my grandmother's 85th birthday"
        ])
    ]

    static let careerSuggestions: [Suggestion] = [
        Suggestion(text: "Generate secure passwords", icon: "🔒", category: "Career", examplePrompts: [
            "Create a strong password for my work email that I can actually remember",
            "Generate secure passwords for my professional social media accounts",
            "Help me create a password system for all my work-related accounts"
        ]),
        Suggestion(text: "Interview question", icon: "🤔", category: "Career", examplePrompts: [
            "Help me prepare answers for common software engineer interview questions",
            "What are good questions to ask the interviewer for a marketing manager role?",
            "Practice behavioral interview questions for a leadership position"
        ]),
        Suggestion(text: "Career Counselor", icon: "💼", category: "Career", examplePrompts: [
            "I'm feeling stuck in my current job - help me explore new career paths",
            "Should I pursue an MBA or gain more work experience first?",
            "How can I transition from teaching to corporate training and development?"
        ]),
        Suggestion(text: "Self-Help book", icon: "📗", category: "Career", examplePrompts: [
            "Recommend books for developing leadership skills and emotional intelligence",
            "Suggest self-help books for overcoming imposter syndrome at work",
            "What books can help me improve my communication and networking skills?"
        ]),
        Suggestion(text: "Statistician", icon: "📊", category: "Career", examplePrompts: [
            "Help me analyze this sales data to identify trends and patterns",
            "Explain how to calculate statistical significance for my marketing campaign results",
            "What statistical methods should I use to evaluate employee performance data?"
        ]),
        Suggestion(text: "Financial planning", icon: "💰", category: "Career", examplePrompts: [
            "Help me create a budget plan for my first job out of college",
            "How should I allocate my salary between savings, investments, and expenses?",
            "What's the best strategy for paying off student loans while building an emergency fund?"
        ])
    ]

    static let healthSuggestions: [Suggestion] = [
        Suggestion(text: "Life Coach", icon: "🧘‍♂️", category: "Health & Nutrition", examplePrompts: [
            "Help me create a morning routine that sets me up for success each day",
            "I'm struggling with work-life balance - give me strategies to manage stress",
            "How can I build better habits and break negative patterns in my life?"
        ]),
        Suggestion(text: "Dietitian", icon: "🥗", category: "Health & Nutrition", examplePrompts: [
            "Create a meal plan for someone trying to lose 20 pounds in a healthy way",
            "What foods should I eat to boost my energy levels throughout the day?",
            "Help me plan nutritious meals for a family with a picky eater child"
        ]),
        Suggestion(text: "Abs-Boosting workouts program", icon: "💪", category: "Health & Nutrition", examplePrompts: [
            "Design a 4-week ab workout routine for beginners with no equipment",
            "Create an advanced core strengthening program for someone who works out regularly",
            "Give me a quick 10-minute daily ab routine I can do at home"
        ]),
        Suggestion(text: "Yoga Poses", icon: "🧘‍♀️", category: "Health & Nutrition", examplePrompts: [
            "Suggest yoga poses for relieving lower back pain from sitting at a desk",
            "Create a relaxing bedtime yoga sequence to help me sleep better",
            "What are the best yoga poses for improving flexibility and reducing stress?"
        ]),
        Suggestion(text: "Vegan Lunch", icon: "🥬", category: "Health & Nutrition", examplePrompts: [
            "Give me 5 easy vegan lunch recipes that are high in protein",
            "Suggest vegan meal prep ideas for busy weekdays",
            "Create a satisfying vegan lunch that will keep me full until dinner"
        ]),
        Suggestion(text: "How many calories should I eat in a day?", icon: "📉", category: "Health & Nutrition", examplePrompts: [
            "Calculate my daily calorie needs for maintaining my current weight",
            "How many calories should I eat to lose 1 pound per week safely?",
            "What's the right calorie intake for someone training for a marathon?"
        ]),
        Suggestion(text: "Youtube Channels about Health, Nutrition and Sports", icon: "▶️", category: "Health & Nutrition", examplePrompts: [
            "Recommend YouTube channels for learning about nutrition and healthy eating",
            "Suggest fitness YouTube channels with effective home workout routines",
            "Find educational channels about mental health and wellness topics"
        ]),
        Suggestion(text: "Training Plan Generator", icon: "🏃", category: "Health & Nutrition", examplePrompts: [
            "Create a beginner's running plan to train for my first 5K race",
            "Design a strength training program for someone new to weightlifting",
            "Generate a workout plan for busy parents with only 30 minutes to exercise"
        ])
    ]

    static let greetingsSuggestions: [Suggestion] = [
        Suggestion(text: "Merry Christmas", icon: "🎄", category: "Greetings", examplePrompts: [
            "Write a heartfelt Christmas message for my family Christmas card",
            "Create a festive Christmas greeting for my coworkers and business contacts",
            "Help me write a warm Christmas message for friends I haven't seen in a while"
        ]),
        Suggestion(text: "Happy Mother's Day", icon: "💐", category: "Greetings", examplePrompts: [
            "Write a touching Mother's Day message for my mom who lives far away",
            "Create a Mother's Day greeting for my wife who's an amazing mother to our kids",
            "Help me write a Mother's Day message for my grandmother who raised me"
        ]),
        Suggestion(text: "Happy Birthday", icon: "🎂", category: "Greetings", examplePrompts: [
            "Write a funny birthday message for my best friend who loves humor",
            "Create a heartfelt birthday greeting for my partner's milestone 30th birthday",
            "Help me write a professional birthday message for my boss"
        ]),
        Suggestion(text: "Happy Valentine's Day", icon: "💝", category: "Greetings", examplePrompts: [
            "Write a romantic Valentine's Day message for my long-distance partner",
            "Create a sweet Valentine's greeting for my spouse of 10 years",
            "Help me write a cute Valentine's message for someone I just started dating"
        ]),
        Suggestion(text: "Happy Anniversary", icon: "💍", category: "Greetings", examplePrompts: [
            "Write a meaningful anniversary message for our 5th wedding anniversary",
            "Create an anniversary greeting for my parents' 25th wedding anniversary",
            "Help me write a romantic anniversary message for my partner"
        ]),
        Suggestion(text: "Happy Thanksgiving", icon: "🦃", category: "Greetings", examplePrompts: [
            "Write a Thanksgiving message expressing gratitude to my family and friends",
            "Create a Thanksgiving greeting for my social media that reflects on this year",
            "Help me write a Thanksgiving message for my team at work"
        ]),
        Suggestion(text: "Happy Halloween", icon: "🎃", category: "Greetings", examplePrompts: [
            "Write a fun Halloween message for my neighborhood trick-or-treaters",
            "Create a spooky Halloween greeting for my friends who love horror movies",
            "Help me write a Halloween message for my kids' school Halloween party"
        ]),
        Suggestion(text: "Greetings in 101 Languages", icon: "🌍", category: "Greetings", examplePrompts: [
            "Teach me how to say 'Happy New Year' in 10 different languages",
            "Show me how to greet people in Spanish, French, German, and Italian",
            "Help me learn basic greetings in Japanese, Korean, and Mandarin Chinese"
        ])
    ]
}
