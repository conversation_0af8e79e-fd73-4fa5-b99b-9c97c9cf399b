//
//  AIModel.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.04.30.
//

import SwiftUI

struct AIModel: Identifiable {
    let id = UUID()
    let name: String
    let isPro: Bool
    let iconName: String

    static let models: [AIModel] = [
        // OpenAI Models (Latest 2025 - Based on Internet Research)
        AIModel(name: "GPT-4.1 nano", isPro: false, iconName: "chatgpt"),
        AIModel(name: "GPT-4.1 mini", isPro: true, iconName: "chatgpt"),
        AIModel(name: "GPT-4o", isPro: true, iconName: "chatgpt"),
        AIModel(name: "GPT-4o mini", isPro: true, iconName: "chatgpt"),
        AIModel(name: "o4-mini", isPro: true, iconName: "chatgpt"),

        // DeepSeek Model (Latest 2025)
        AIModel(name: "DeepSeek", isPro: true, iconName: "deepseek"),

        // <PERSON> (Latest 2025)
        AIModel(name: "Claude Sonnet 4", isPro: true, iconName: "claude"),

        // Gemini Models (Latest 2025)
        AIModel(name: "Gemini 2.0 Flash", isPro: true, iconName: "gemini"),
    ]
}
