//
//  Tool.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.04.30.
//

import SwiftUI

struct Tool: Identifiable, Hashable {
    let id = UUID()
    let name: String
    let description: String
    let icon: String
    let assetImageName: String? // New property for asset image names
    let isNew: Bool
    let iconBackgroundColor: Color

    // Implement Hashable
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }

    static func == (lhs: Tool, rhs: Tool) -> Bool {
        return lhs.id == rhs.id
    }

    static let eliteTools: [Tool] = [
        Tool(name: "AI Image Generator", description: "Create & transform images with AI", icon: "photo.artframe", assetImageName: "aiimagegenerator", isNew: true, iconBackgroundColor: Color(red: 0.2, green: 0.6, blue: 0.8)),
        Tool(name: "Voice Chat", description: "Talk with Pluto AI about anything", icon: "waveform.circle", assetImageName: "browsingchat", isNew: true, iconBackgroundColor: Color.black),
        <PERSON><PERSON>(name: "<PERSON>rows<PERSON> <PERSON><PERSON>", description: "Get most recent answers with web search", icon: "globe", assetImageName: "browsingchat", isNew: false, iconBackgroundColor: Color.blue),
        Tool(name: "YouTube Summary", description: "Get detailed YouTube video summarization", icon: "play.rectangle.fill", assetImageName: "youtubesummary", isNew: false, iconBackgroundColor: Color.red),
        Tool(name: "Upload & Ask", description: "Search or ask about anything in a document", icon: "doc.text", assetImageName: "uploadandask", isNew: false, iconBackgroundColor: Color.gray),
        Tool(name: "Link & Ask", description: "Search or ask about anything in a webpage", icon: "link", assetImageName: "linkandask", isNew: false, iconBackgroundColor: Color.purple)
    ]

    // Since we no longer have Santa AI, displayedEliteTools is the same as eliteTools
    static let displayedEliteTools: [Tool] = eliteTools
}
