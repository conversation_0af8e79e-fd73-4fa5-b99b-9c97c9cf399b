//
//  AssistantGreetingView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.05.01.
//

import SwiftUI

struct AssistantGreetingView: View {
    let assistant: Assistant
    let onActionSelected: (String) -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Assistant avatar and greeting message
            HStack(alignment: .top, spacing: 12) {
                // Assistant avatar
                Group {
                    if let uiImage = UIImage(named: assistant.name) {
                        // Show assistant image
                        Image(uiImage: uiImage)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 40, height: 40)
                            .clipShape(Circle())
                            .shadow(color: .black.opacity(0.2), radius: 4, x: 0, y: 2)
                    } else {
                        // Default assistant avatar with gradient
                        Circle()
                            .fill(LinearGradient(
                                gradient: Gradient(colors: [
                                    assistant.color,
                                    assistant.color.opacity(0.7)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ))
                            .frame(width: 40, height: 40)
                            .overlay(
                                Image(systemName: assistant.iconName)
                                    .font(.system(size: 20, weight: .medium))
                                    .foregroundColor(.white)
                            )
                            .shadow(color: assistant.color.opacity(0.3), radius: 4, x: 0, y: 2)
                    }
                }
                
                // Greeting message
                VStack(alignment: .leading, spacing: 12) {
                    Text(getGreetingMessage())
                        .font(.body)
                        .foregroundColor(.white)
                        .multilineTextAlignment(.leading)
                    
                    // Quick action buttons
                    VStack(spacing: 8) {
                        ForEach(getQuickActions(), id: \.text) { action in
                            AssistantActionButton(
                                text: action.text,
                                icon: action.icon,
                                onTap: {
                                    onActionSelected(action.text)
                                }
                            )
                        }
                    }
                }
            }
        }
        .padding()
    }
    
    private func getGreetingMessage() -> String {
        switch assistant.name {
        case "Logo Designer":
            return "Greetings! Excited to start designing a unique logo for your brand. What specific elements and styles are you looking for?"
        case "Creative Writer":
            return "Hello! I'm here to help you craft compelling stories, articles, and creative content. What writing project can I assist you with today?"
        case "Business Planner":
            return "Welcome! Ready to help you develop strategic business plans and growth strategies. What business challenge shall we tackle?"
        case "Study Helper":
            return "Hi there! I'm your study companion, ready to help you learn, understand concepts, and prepare for exams. What subject are you working on?"
        case "Financial Analyst":
            return "Greetings! I'm here to help you analyze financial data, create reports, and make informed investment decisions. What financial topic interests you?"
        case "Lawyer":
            return "Hello! I can assist you with legal research, document drafting, and understanding legal concepts. What legal matter can I help with?"
        case "Tattoo Artist":
            return "Hey! Ready to design some amazing tattoo concepts and help you visualize your next ink. What style or theme are you considering?"
        case "Routine Planner":
            return "Hi! I'm here to help you create productive daily routines and optimize your time management. What area of your routine needs improvement?"
        case "Travel Guide":
            return "Welcome, traveler! I'm excited to help you plan amazing trips and discover new destinations. Where would you like to explore?"
        case "News Reporter":
            return "Hello! I'm here to help you stay informed with the latest news, analyze current events, and understand global developments. What's happening in the world today?"
        default:
            return "Hello! I'm \(assistant.name), and I'm here to help you with specialized assistance. What can I do for you today?"
        }
    }
    
    private func getQuickActions() -> [AssistantAction] {
        // Use the assistant's suggested prompts if available
        if let suggestedPrompts = assistant.suggestedPrompts, !suggestedPrompts.isEmpty {
            return suggestedPrompts.map { prompt in
                AssistantAction(text: prompt.text, icon: prompt.icon)
            }
        }

        // Fallback to default actions if no suggested prompts
        return [
            AssistantAction(text: "Get started", icon: "play"),
            AssistantAction(text: "Learn more", icon: "info.circle"),
            AssistantAction(text: "Ask a question", icon: "questionmark.bubble")
        ]
    }
}

struct AssistantAction {
    let text: String
    let icon: String
}

struct AssistantActionButton: View {
    let text: String
    let icon: String
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white)
                    .frame(width: 20)
                
                Text(text)
                    .font(.subheadline)
                    .foregroundColor(.white)
                    .multilineTextAlignment(.leading)
                
                Spacer()
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color.gray.opacity(0.2))
            .cornerRadius(20)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    AssistantGreetingView(
        assistant: Assistant.assistants[0],
        onActionSelected: { action in
            print("Selected action: \(action)")
        }
    )
    .padding()
    .background(Color.black)
    .preferredColorScheme(.dark)
}
