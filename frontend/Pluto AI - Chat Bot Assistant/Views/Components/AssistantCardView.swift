//
//  AssistantCardView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.04.30.
//

import SwiftUI

struct AssistantCardView: View {
    let assistant: Assistant
    @State private var showChatScreen = false

    var body: some View {
        Button(action: {
            showChatScreen = true
        }) {
            ZStack {
                // Background image that fills the entire card
                Group {
                    if let uiImage = UIImage(named: assistant.name) {
                        Image(uiImage: uiImage)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 180, height: 180)
                            .clipped()
                            .onAppear {
                                print("Successfully loaded image for assistant: \(assistant.name)")
                            }
                    } else {
                        // Fallback gradient background with system icon
                        LinearGradient(
                            gradient: Gradient(colors: [
                                assistant.color.opacity(0.8),
                                assistant.color.opacity(0.4)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                        .overlay(
                            Image(systemName: assistant.iconName)
                                .font(.system(size: 60, weight: .medium))
                                .foregroundColor(.white.opacity(0.3))
                        )
                        .onAppear {
                            print("Using gradient background for assistant: \(assistant.name)")
                        }
                    }
                }

                // Dark gradient overlay for better text readability
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.clear,
                        Color.black.opacity(0.7)
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                )

                // Text overlay at the bottom
                VStack {
                    Spacer()

                    VStack(alignment: .leading, spacing: 4) {
                        Text(assistant.name)
                            .font(.headline)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .multilineTextAlignment(.leading)
                            .shadow(color: .black.opacity(0.5), radius: 2, x: 0, y: 1)

                        Text(assistant.description)
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.9))
                            .lineLimit(2)
                            .multilineTextAlignment(.leading)
                            .shadow(color: .black.opacity(0.5), radius: 1, x: 0, y: 1)
                    }
                    .padding(.horizontal, 12)
                    .padding(.bottom, 12)
                    .frame(maxWidth: .infinity, alignment: .leading)
                }
            }
            .frame(width: 180, height: 180)
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color.white.opacity(0.1), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .fullScreenCover(isPresented: $showChatScreen) {
            NavigationView {
                // Create a specialized chat view for this assistant without auto-sending
                // Create a suggestion object for this assistant
                let assistantSuggestion = Suggestion(
                    text: "I'd like to work with the \(assistant.name) assistant",
                    icon: assistant.iconName,
                    category: assistant.name,
                    examplePrompts: [
                        "What can you help me with?",
                        "Show me what you're capable of",
                        "Let's get started with a conversation"
                    ]
                )

                // Pass the suggestion and assistant to ChatScreenView without auto-sending
                ChatScreenView(suggestion: assistantSuggestion, autoSend: false, showExamplesOnAppear: false, assistant: assistant)
            }
        }
    }
}

struct CreateAssistantCardView: View {
    @State private var showCreateAssistant = false

    var body: some View {
        Button(action: {
            showCreateAssistant = true
        }) {
            ZStack {
                // Dark background to match reference design
                Color.gray.opacity(0.2)

                // Content
                VStack(spacing: 12) {
                    // Plus icon
                    Image(systemName: "plus")
                        .font(.system(size: 40, weight: .medium))
                        .foregroundColor(.white)

                    // Title
                    Text("Create Assistant")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                        .multilineTextAlignment(.center)
                }
            }
            .frame(width: 180, height: 180)
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color.white.opacity(0.1), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .fullScreenCover(isPresented: $showCreateAssistant) {
            CreateAssistantView()
        }
    }
}

#Preview {
    HStack {
        CreateAssistantCardView()
        AssistantCardView(assistant: Assistant.assistants[0])
    }
    .preferredColorScheme(.dark)
}
