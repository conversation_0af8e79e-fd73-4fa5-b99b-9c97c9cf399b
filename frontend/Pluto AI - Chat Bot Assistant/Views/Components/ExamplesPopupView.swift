//
//  ExamplesPopupView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.05.01.
//

import SwiftUI

struct ExamplesPopupView: View {
    let categoryName: String
    let examples: [String]
    var onExampleSelected: (String) -> Void
    var onCustomPrompt: (() -> Void)? = nil
    var onDismiss: () -> Void

    var body: some View {
        ZStack {
            // Background overlay
            Color.black.opacity(0.7)
                .edgesIgnoringSafeArea(.all)
                .onTapGesture {
                    onDismiss()
                }

            // Popup content
            VStack(spacing: 20) {
                // Header
                Text("Examples")
                    .font(.headline)
                    .foregroundColor(.white)
                    .padding(.top)

                // Category badge
                Text(categoryName)
                    .font(.caption)
                    .foregroundColor(.white)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 4)
                    .background(Color.gray.opacity(0.6))
                    .cornerRadius(12)

                // Subtitle
                Text("Choose an example to get started:")
                    .font(.subheadline)
                    .foregroundColor(.gray)

                // Examples list
                VStack(spacing: 12) {
                    ForEach(Array(examples.enumerated()), id: \.offset) { index, example in
                        Button(action: {
                            onExampleSelected(example)
                        }) {
                            HStack {
                                // Number indicator
                                Text("\(index + 1)")
                                    .font(.caption)
                                    .fontWeight(.bold)
                                    .foregroundColor(.white)
                                    .frame(width: 24, height: 24)
                                    .background(Color.blue)
                                    .clipShape(Circle())

                                Text(example)
                                    .font(.subheadline)
                                    .foregroundColor(.white)
                                    .multilineTextAlignment(.leading)
                                    .lineLimit(nil)

                                Spacer()

                                Image(systemName: "arrow.right")
                                    .foregroundColor(.gray)
                                    .font(.caption)
                            }
                            .padding()
                            .background(Color.systemGray6)
                            .cornerRadius(12)
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
                .padding(.horizontal)

                // Action buttons
                HStack(spacing: 16) {
                    // Custom prompt button
                    if let onCustomPrompt = onCustomPrompt {
                        Button(action: onCustomPrompt) {
                            Text("Custom Prompt")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(.white)
                                .padding(.vertical, 10)
                                .padding(.horizontal, 20)
                                .background(Color.blue)
                                .cornerRadius(20)
                        }
                    }

                    // Close button
                    Button(action: onDismiss) {
                        Text("Close")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.white)
                            .padding(.vertical, 10)
                            .padding(.horizontal, 20)
                            .background(Color.systemGray5)
                            .cornerRadius(20)
                    }
                }
                .padding(.bottom)
            }
            .padding()
            .background(Color.black)
            .cornerRadius(16)
            .frame(width: UIScreen.main.bounds.width * 0.85)
            .shadow(radius: 10)
        }
    }
}

#Preview {
    ExamplesPopupView(
        categoryName: "Business & Marketing",
        examples: [
            "Create a marketing plan for a new mobile app",
            "Write a professional email to follow up with a client",
            "Generate 5 catchy taglines for a fitness brand"
        ],
        onExampleSelected: { _ in },
        onCustomPrompt: {},
        onDismiss: {}
    )
    .preferredColorScheme(.dark)
}
