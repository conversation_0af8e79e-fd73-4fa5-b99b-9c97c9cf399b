//
//  SuggestionGreetingView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.05.01.
//

import SwiftUI

struct SuggestionGreetingView: View {
    let category: SuggestionCategory
    let onActionSelected: (String) -> Void
    
    var body: some View {
        VStack(spacing: 20) {
            // Category icon and info
            VStack(spacing: 16) {
                // Category icon
                Text(category.icon)
                    .font(.system(size: 60))
                    .padding()
                    .background(category.color.opacity(0.2))
                    .clipShape(Circle())
                
                // Category name
                Text(category.name)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)
                
                // Category description
                Text(category.description)
                    .font(.subheadline)
                    .foregroundColor(.gray)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
            }
            
            // Example prompts section
            VStack(alignment: .leading, spacing: 12) {
                Text("Try asking:")
                    .font(.headline)
                    .foregroundColor(.white)
                    .padding(.horizontal)
                
                // Get example prompts from the category's suggestions
                let examplePrompts = getExamplePrompts(for: category.name)
                
                VStack(spacing: 8) {
                    ForEach(Array(examplePrompts.prefix(3).enumerated()), id: \.offset) { index, prompt in
                        Button(action: {
                            onActionSelected(prompt)
                        }) {
                            HStack {
                                Text(prompt)
                                    .font(.subheadline)
                                    .foregroundColor(.white)
                                    .multilineTextAlignment(.leading)
                                    .lineLimit(2)
                                Spacer()
                                Image(systemName: "arrow.right.circle.fill")
                                    .foregroundColor(category.color)
                                    .font(.system(size: 20))
                            }
                            .padding()
                            .background(Color.gray.opacity(0.2))
                            .cornerRadius(12)
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
                .padding(.horizontal)
            }
        }
        .padding()
        .frame(maxWidth: .infinity)
        .background(Color.black)
    }
    
    // Helper function to get example prompts for a category
    private func getExamplePrompts(for categoryName: String) -> [String] {
        switch categoryName {
        case "E-Mail":
            return [
                "Write a professional follow-up email after a job interview",
                "Create a newsletter template for my monthly company update",
                "Draft a polite response to a customer complaint"
            ]
        case "Business & Marketing":
            return [
                "Create a marketing plan for a new mobile app",
                "Generate 5 catchy taglines for a fitness brand",
                "Write a compelling product description for an eco-friendly water bottle"
            ]
        case "Astrology":
            return [
                "What does it mean if I have multiple planets in Scorpio?",
                "How will the upcoming Mercury retrograde affect my Taurus sign?",
                "Compare compatibility between Libra and Aquarius"
            ]
        case "Education":
            return [
                "Explain quantum physics in simple terms",
                "Create a study plan for the SAT exam",
                "Help me understand the causes of World War I"
            ]
        case "Art":
            return [
                "Write a short story in the style of Edgar Allan Poe",
                "Create a poem about autumn using vivid imagery",
                "Suggest creative ideas for a mixed media art project"
            ]
        case "Travel":
            return [
                "Plan a 7-day itinerary for Tokyo, Japan",
                "What are the must-visit attractions in Barcelona?",
                "Create a packing list for a winter trip to Norway"
            ]
        case "Daily Lifestyle":
            return [
                "Suggest a weekly meal plan for a vegetarian diet",
                "How can I create a productive morning routine?",
                "Recommend exercises for improving posture while working from home"
            ]
        case "Relationship":
            return [
                "Give me conversation starters for a first date",
                "How can we communicate better about money issues?",
                "What are some red flags to watch for when dating?"
            ]
        case "Fun":
            return [
                "Tell me something fun and quirky about being a Gemini",
                "Create a funny story about a cat who thinks it's a dog",
                "Turn this text into emojis: 'I love pizza and movies'"
            ]
        case "Social":
            return [
                "Suggest thoughtful gifts for my best friend who loves cooking",
                "Help me create an attractive dating profile",
                "Give me interesting conversation starters for networking"
            ]
        case "Career":
            return [
                "Help me prepare for common software engineer interview questions",
                "I'm feeling stuck in my job - help me explore new career paths",
                "Create a budget plan for my first job out of college"
            ]
        case "Health & Nutrition":
            return [
                "Help me create a morning routine that sets me up for success",
                "Suggest a 30-minute workout routine for beginners",
                "What are some healthy meal prep ideas for busy weekdays?"
            ]
        case "Greetings":
            return [
                "Write a heartfelt birthday message for my childhood friend",
                "Create a funny birthday wish for my coworker",
                "Help me write a meaningful anniversary message"
            ]
        default:
            return [
                "Ask me anything about \(categoryName)",
                "How can I learn more about \(categoryName)?",
                "What are the best resources for \(categoryName)?"
            ]
        }
    }
}

#Preview {
    SuggestionGreetingView(
        category: SuggestionCategory.categories.first(where: { $0.name == "Business & Marketing" })!,
        onActionSelected: { _ in }
    )
    .preferredColorScheme(.dark)
}
