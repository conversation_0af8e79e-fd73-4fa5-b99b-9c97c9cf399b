//
//  ChatToolbarContent.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.04.30.
//

import SwiftUI

struct ChatToolbarContent: ToolbarContent {
    @Binding var showModelSelection: Bool
    var selectedModel: AIModel
    var dismissAction: () -> Void
    var categoryName: String? = nil
    var categoryIcon: String? = nil
    var assistant: Assistant? = nil
    var onInfoButtonTapped: (() -> Void)? = nil
    var onNewChatTapped: (() -> Void)? = nil
    var onMoreOptionsTapped: (() -> Void)? = nil

    var body: some ToolbarContent {
        ToolbarItem(placement: .navigationBarLeading) {
            Button(action: {
                dismissAction()
            }) {
                Image(systemName: "xmark")
                    .foregroundColor(.white)
            }
        }

        ToolbarItem(placement: .principal) {
            if let assistant = assistant {
                // Show assistant name and info button
                HStack(spacing: 8) {
                    // Assistant avatar
                    Group {
                        if let uiImage = UIImage(named: assistant.name) {
                            Image(uiImage: uiImage)
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                                .frame(width: 32, height: 32)
                                .clipShape(Circle())
                        } else {
                            Circle()
                                .fill(LinearGradient(
                                    gradient: Gradient(colors: [
                                        assistant.color,
                                        assistant.color.opacity(0.7)
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ))
                                .frame(width: 32, height: 32)
                                .overlay(
                                    Image(systemName: assistant.iconName)
                                        .font(.system(size: 16, weight: .medium))
                                        .foregroundColor(.white)
                                )
                        }
                    }

                    // Assistant name
                    Text(assistant.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)

                    // Info button
                    Button(action: {
                        onInfoButtonTapped?()
                    }) {
                        Image(systemName: "info.circle")
                            .font(.system(size: 16))
                            .foregroundColor(.gray)
                    }
                }
            } else if let categoryName = categoryName {
                // Show category name with icon for suggestion-based chats
                HStack(spacing: 8) {
                    // Category icon
                    if let icon = categoryIcon {
                        // Handle both emojis and SF Symbols
                        if icon.count == 1 || icon.first?.isEmoji == true {
                            // Display emoji directly
                            Text(icon)
                                .font(.title3)
                        } else {
                            // Display SF Symbol
                            Image(systemName: icon)
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(.white)
                        }
                    }

                    // Category name
                    Text(categoryName)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                        .lineLimit(1)
                }
            } else {
                // Show model selection for general chats
                Button(action: {
                    showModelSelection.toggle()
                }) {
                    HStack(spacing: 8) {
                        // Model logo
                        Image(selectedModel.iconName)
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 20, height: 20)

                        Text(selectedModel.name)
                            .foregroundColor(.white)
                        Image(systemName: "chevron.down")
                            .foregroundColor(.white)
                            .font(.caption)
                    }
                    .padding(.vertical, 6)
                    .padding(.horizontal, 12)
                    .background(Color.systemGray6)
                    .cornerRadius(16)
                }
            }
        }

        ToolbarItem(placement: .navigationBarTrailing) {
            HStack {
                Button(action: {
                    onNewChatTapped?()
                }) {
                    Image(systemName: "square.and.pencil")
                        .foregroundColor(.white)
                }

                Button(action: {
                    onMoreOptionsTapped?()
                }) {
                    Image(systemName: "ellipsis")
                        .foregroundColor(.white)
                }
            }
        }
    }
}
