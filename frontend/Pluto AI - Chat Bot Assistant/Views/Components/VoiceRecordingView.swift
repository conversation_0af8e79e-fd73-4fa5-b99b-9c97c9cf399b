//
//  VoiceRecordingView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.04.30.
//

import SwiftUI

struct VoiceRecordingView: View {
    @Binding var isPresented: Bool
    @StateObject private var speechService = SpeechRecognitionService.shared
    @State private var recordingTime: TimeInterval = 0
    @State private var timer: Timer?
    var onRecordingComplete: (String) -> Void
    
    var body: some View {
        ZStack {
            // Background
            Color(red: 76/255, green: 175/255, blue: 130/255)
                .edgesIgnoringSafeArea(.all)
            
            VStack {
                // Timer display
                Text(timeString(from: recordingTime))
                    .font(.system(size: 36, weight: .bold))
                    .foregroundColor(.white)
                    .padding(.top, 40)
                
                Spacer()
                
                // Recording button
                ZStack {
                    Circle()
                        .fill(Color.white.opacity(0.3))
                        .frame(width: 200, height: 200)
                    
                    Button(action: {
                        stopRecording()
                    }) {
                        ZStack {
                            Circle()
                                .fill(Color.white)
                                .frame(width: 80, height: 80)
                            
                            Image(systemName: "stop.fill")
                                .font(.system(size: 30))
                                .foregroundColor(Color(red: 76/255, green: 175/255, blue: 130/255))
                        }
                    }
                }
                .padding(.bottom, 40)
                
                // Stop recording text
                Text("Tap to stop recording")
                    .foregroundColor(.white)
                    .font(.headline)
                    .padding(.bottom, 40)
                
                // Progress bar
                Rectangle()
                    .fill(Color.white)
                    .frame(width: 100, height: 4)
                    .cornerRadius(2)
                    .padding(.bottom, 40)
            }
        }
        .onAppear {
            startRecording()
        }
        .onDisappear {
            if speechService.isRecording {
                speechService.stopRecording()
            }
            timer?.invalidate()
            timer = nil
        }
    }
    
    private func startRecording() {
        // Reset timer
        recordingTime = 0
        
        // Start timer
        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { _ in
            recordingTime += 1
        }
        
        // Start speech recognition
        speechService.startRecording { recognizedText in
            stopRecording()
            if !recognizedText.isEmpty {
                onRecordingComplete(recognizedText)
            }
        }
    }
    
    private func stopRecording() {
        // Stop speech recognition
        speechService.stopRecording()
        
        // Stop timer
        timer?.invalidate()
        timer = nil
        
        // Dismiss the view
        isPresented = false
        
        // Pass the recognized text back
        if !speechService.recognizedText.isEmpty {
            onRecordingComplete(speechService.recognizedText)
        }
    }
    
    private func timeString(from timeInterval: TimeInterval) -> String {
        let minutes = Int(timeInterval) / 60
        let seconds = Int(timeInterval) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
}

#Preview {
    VoiceRecordingView(isPresented: .constant(true), onRecordingComplete: { _ in })
}
