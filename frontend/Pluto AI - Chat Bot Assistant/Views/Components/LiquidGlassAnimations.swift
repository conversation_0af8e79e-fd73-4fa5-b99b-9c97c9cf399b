//
//  LiquidGlassAnimations.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.01.18.
//  Liquid Glass Animation System
//

import SwiftUI

// MARK: - Liquid Glass Animation Presets

struct LiquidGlassAnimations {
    static let spring = Animation.spring(response: 0.6, dampingFraction: 0.8, blendDuration: 0)
    static let easeInOut = Animation.easeInOut(duration: 0.3)
    static let smooth = Animation.interpolatingSpring(stiffness: 300, damping: 30)
    static let bounce = Animation.spring(response: 0.5, dampingFraction: 0.6, blendDuration: 0)
    static let gentle = Animation.easeInOut(duration: 0.4)
}

// MARK: - Floating Animation

struct FloatingEffect: ViewModifier {
    @State private var isFloating = false
    let amplitude: CGFloat
    let duration: Double
    
    init(amplitude: CGFloat = 10, duration: Double = 3) {
        self.amplitude = amplitude
        self.duration = duration
    }
    
    func body(content: Content) -> some View {
        content
            .offset(y: isFloating ? -amplitude : amplitude)
            .animation(
                Animation.easeInOut(duration: duration)
                    .repeatFore<PERSON>(autoreverses: true),
                value: isFloating
            )
            .onAppear {
                isFloating = true
            }
    }
}

// MARK: - Shimmer Effect

struct ShimmerEffect: ViewModifier {
    @State private var phase: CGFloat = 0
    let duration: Double
    let angle: Double
    
    init(duration: Double = 2, angle: Double = 70) {
        self.duration = duration
        self.angle = angle
    }
    
    func body(content: Content) -> some View {
        content
            .overlay(
                Rectangle()
                    .fill(
                        LinearGradient(
                            colors: [
                                .clear,
                                .white.opacity(0.3),
                                .clear
                            ],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .rotationEffect(.degrees(angle))
                    .offset(x: phase)
                    .animation(
                        Animation.linear(duration: duration)
                            .repeatForever(autoreverses: false),
                        value: phase
                    )
                    .onAppear {
                        phase = 300
                    }
            )
            .clipped()
    }
}

// MARK: - Pulse Effect

struct PulseEffect: ViewModifier {
    @State private var isPulsing = false
    let minScale: CGFloat
    let maxScale: CGFloat
    let duration: Double
    
    init(minScale: CGFloat = 0.95, maxScale: CGFloat = 1.05, duration: Double = 1.5) {
        self.minScale = minScale
        self.maxScale = maxScale
        self.duration = duration
    }
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(isPulsing ? maxScale : minScale)
            .animation(
                Animation.easeInOut(duration: duration)
                    .repeatForever(autoreverses: true),
                value: isPulsing
            )
            .onAppear {
                isPulsing = true
            }
    }
}

// MARK: - Glow Animation

struct GlowEffect: ViewModifier {
    @State private var glowIntensity: Double = 0
    let color: Color
    let radius: CGFloat
    let duration: Double
    
    init(color: Color = .blue, radius: CGFloat = 10, duration: Double = 2) {
        self.color = color
        self.radius = radius
        self.duration = duration
    }
    
    func body(content: Content) -> some View {
        content
            .shadow(color: color.opacity(glowIntensity), radius: radius)
            .animation(
                Animation.easeInOut(duration: duration)
                    .repeatForever(autoreverses: true),
                value: glowIntensity
            )
            .onAppear {
                glowIntensity = 0.8
            }
    }
}

// MARK: - Morphing Background

struct MorphingBackground: View {
    @State private var phase: CGFloat = 0
    let colors: [Color]
    let duration: Double
    
    init(colors: [Color] = [.blue, .purple, .pink, .orange], duration: Double = 8) {
        self.colors = colors
        self.duration = duration
    }
    
    var body: some View {
        ZStack {
            ForEach(0..<colors.count, id: \.self) { index in
                Circle()
                    .fill(
                        RadialGradient(
                            colors: [colors[index].opacity(0.6), .clear],
                            center: .center,
                            startRadius: 0,
                            endRadius: 200
                        )
                    )
                    .frame(width: 400, height: 400)
                    .offset(
                        x: cos(phase + Double(index) * .pi / 2) * 100,
                        y: sin(phase + Double(index) * .pi / 2) * 100
                    )
                    .blur(radius: 20)
            }
        }
        .animation(
            Animation.linear(duration: duration)
                .repeatForever(autoreverses: false),
            value: phase
        )
        .onAppear {
            phase = .pi * 2
        }
    }
}

// MARK: - Liquid Glass Transition

struct LiquidTransition: ViewModifier {
    let isVisible: Bool
    let direction: Direction
    
    enum Direction {
        case up, down, left, right, scale, fade
    }
    
    func body(content: Content) -> some View {
        content
            .opacity(isVisible ? 1 : 0)
            .offset(
                x: offsetX,
                y: offsetY
            )
            .scaleEffect(scaleEffect)
            .animation(LiquidGlassAnimations.spring, value: isVisible)
    }
    
    private var offsetX: CGFloat {
        guard !isVisible else { return 0 }
        switch direction {
        case .left: return -50
        case .right: return 50
        default: return 0
        }
    }
    
    private var offsetY: CGFloat {
        guard !isVisible else { return 0 }
        switch direction {
        case .up: return -50
        case .down: return 50
        default: return 0
        }
    }
    
    private var scaleEffect: CGFloat {
        guard !isVisible else { return 1 }
        switch direction {
        case .scale: return 0.8
        default: return 1
        }
    }
}

// MARK: - Interactive Hover Effect

struct InteractiveHover: ViewModifier {
    @State private var isHovered = false
    let scale: CGFloat
    let shadowRadius: CGFloat
    let glowColor: Color
    
    init(scale: CGFloat = 1.05, shadowRadius: CGFloat = 20, glowColor: Color = .blue) {
        self.scale = scale
        self.shadowRadius = shadowRadius
        self.glowColor = glowColor
    }
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(isHovered ? scale : 1.0)
            .shadow(
                color: glowColor.opacity(isHovered ? 0.3 : 0),
                radius: isHovered ? shadowRadius : 0
            )
            .animation(LiquidGlassAnimations.gentle, value: isHovered)
            .onTapGesture {
                // Trigger hover effect on tap for touch devices
                withAnimation(LiquidGlassAnimations.bounce) {
                    isHovered = true
                }
                
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    withAnimation(LiquidGlassAnimations.gentle) {
                        isHovered = false
                    }
                }
            }
    }
}

// MARK: - Liquid Loading Animation

struct LiquidLoadingView: View {
    @State private var animationPhase: CGFloat = 0
    let size: CGFloat
    let color: Color
    
    init(size: CGFloat = 40, color: Color = .blue) {
        self.size = size
        self.color = color
    }
    
    var body: some View {
        ZStack {
            ForEach(0..<3) { index in
                Circle()
                    .fill(color.opacity(0.6))
                    .frame(width: size / 3, height: size / 3)
                    .scaleEffect(
                        1 + sin(animationPhase + Double(index) * .pi / 3) * 0.5
                    )
                    .offset(
                        x: cos(animationPhase + Double(index) * 2 * .pi / 3) * size / 4,
                        y: sin(animationPhase + Double(index) * 2 * .pi / 3) * size / 4
                    )
            }
        }
        .frame(width: size, height: size)
        .animation(
            Animation.linear(duration: 2)
                .repeatForever(autoreverses: false),
            value: animationPhase
        )
        .onAppear {
            animationPhase = .pi * 2
        }
    }
}

// MARK: - Particle System

struct ParticleSystem: View {
    @State private var particles: [Particle] = []
    let particleCount: Int
    let colors: [Color]
    
    init(particleCount: Int = 20, colors: [Color] = [.blue, .purple, .pink]) {
        self.particleCount = particleCount
        self.colors = colors
    }
    
    var body: some View {
        ZStack {
            ForEach(particles.indices, id: \.self) { index in
                Circle()
                    .fill(particles[index].color.opacity(0.6))
                    .frame(width: particles[index].size, height: particles[index].size)
                    .position(particles[index].position)
                    .blur(radius: 2)
            }
        }
        .onAppear {
            generateParticles()
            startAnimation()
        }
    }
    
    private func generateParticles() {
        particles = (0..<particleCount).map { _ in
            Particle(
                position: CGPoint(
                    x: CGFloat.random(in: 0...UIScreen.main.bounds.width),
                    y: CGFloat.random(in: 0...UIScreen.main.bounds.height)
                ),
                velocity: CGPoint(
                    x: CGFloat.random(in: -1...1),
                    y: CGFloat.random(in: -1...1)
                ),
                size: CGFloat.random(in: 2...8),
                color: colors.randomElement() ?? .blue
            )
        }
    }
    
    private func startAnimation() {
        Timer.scheduledTimer(withTimeInterval: 0.016, repeats: true) { _ in
            updateParticles()
        }
    }
    
    private func updateParticles() {
        for index in particles.indices {
            particles[index].position.x += particles[index].velocity.x
            particles[index].position.y += particles[index].velocity.y
            
            // Wrap around screen edges
            if particles[index].position.x < 0 {
                particles[index].position.x = UIScreen.main.bounds.width
            } else if particles[index].position.x > UIScreen.main.bounds.width {
                particles[index].position.x = 0
            }
            
            if particles[index].position.y < 0 {
                particles[index].position.y = UIScreen.main.bounds.height
            } else if particles[index].position.y > UIScreen.main.bounds.height {
                particles[index].position.y = 0
            }
        }
    }
}

struct Particle {
    var position: CGPoint
    var velocity: CGPoint
    var size: CGFloat
    var color: Color
}

// MARK: - View Extensions

extension View {
    func floating(amplitude: CGFloat = 10, duration: Double = 3) -> some View {
        modifier(FloatingEffect(amplitude: amplitude, duration: duration))
    }
    
    func shimmer(duration: Double = 2, angle: Double = 70) -> some View {
        modifier(ShimmerEffect(duration: duration, angle: angle))
    }
    
    func pulse(minScale: CGFloat = 0.95, maxScale: CGFloat = 1.05, duration: Double = 1.5) -> some View {
        modifier(PulseEffect(minScale: minScale, maxScale: maxScale, duration: duration))
    }
    
    func glow(color: Color = .blue, radius: CGFloat = 10, duration: Double = 2) -> some View {
        modifier(GlowEffect(color: color, radius: radius, duration: duration))
    }
    
    func liquidTransition(isVisible: Bool, direction: LiquidTransition.Direction = .scale) -> some View {
        modifier(LiquidTransition(isVisible: isVisible, direction: direction))
    }
    
    func interactiveHover(scale: CGFloat = 1.05, shadowRadius: CGFloat = 20, glowColor: Color = .blue) -> some View {
        modifier(InteractiveHover(scale: scale, shadowRadius: shadowRadius, glowColor: glowColor))
    }
}
