//
//  SuggestionView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.04.30.
//

import SwiftUI

struct CategoryPillView: View {
    let category: SuggestionCategory
    @Binding var selectedCategory: String

    var body: some View {
        Button(action: {
            selectedCategory = category.name
        }) {
            HStack(spacing: 6) {
                // Display emoji directly
                Text(category.icon)
                    .font(.system(size: 16))

                Text(category.name)
                    .foregroundColor(.white)
                    .font(.system(size: 14, weight: .medium))
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(selectedCategory == category.name ? Color.gray : Color.systemGray6)
            .cornerRadius(20)
        }
    }
}

struct SuggestionRowView: View {
    let suggestion: Suggestion
    var action: (Suggestion) -> Void
    @State private var showSpecializedChat = false

    var body: some View {
        Button(action: {
            // Go directly to chat screen which will show examples
            showSpecializedChat = true
        }) {
            HStack {
                // Display emoji directly instead of SF Symbol
                Text(suggestion.icon)
                    .font(.title2)
                    .frame(width: 30, height: 30)

                Text(suggestion.text)
                    .foregroundColor(.white)
                    .multilineTextAlignment(.leading)

                Spacer()

                Image(systemName: "arrow.right")
                    .foregroundColor(.gray)
            }
            .padding()
            .background(Color.systemGray6)
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
        .fullScreenCover(isPresented: $showSpecializedChat) {
            NavigationView {
                // Create a specialized chat view without auto-showing examples
                ChatScreenView(
                    suggestion: suggestion,
                    autoSend: false,
                    showExamplesOnAppear: false,
                    assistant: nil
                )
            }
        }
    }
}

struct SuggestionsListView: View {
    let suggestions: [Suggestion]
    var onSuggestionTapped: (Suggestion) -> Void

    var body: some View {
        VStack(spacing: 10) {
            ForEach(suggestions) { suggestion in
                // We still pass the action for compatibility, but the SuggestionRowView
                // now handles navigation directly with fullScreenCover
                SuggestionRowView(suggestion: suggestion, action: onSuggestionTapped)
            }
        }
    }
}

#Preview {
    VStack {
        HStack {
            CategoryPillView(category: SuggestionCategory.categories[0], selectedCategory: .constant("E-Mail"))
            CategoryPillView(category: SuggestionCategory.categories[1], selectedCategory: .constant("E-Mail"))
        }

        SuggestionsListView(
            suggestions: Suggestion.emailSuggestions,
            onSuggestionTapped: { suggestion in
                print("Tapped suggestion: \(suggestion.text) from category \(suggestion.category)")
            }
        )
    }
    .padding()
    .background(Color.black)
    .preferredColorScheme(.dark)
}
