//
//  HomeToolbarContent.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.04.30.
//

import SwiftUI

// Regular view instead of ToolbarContent
struct HomeToolbarView: View {
    @Binding var showSettings: Bool
    @State private var showProSubscription = false

    var body: some View {
        // Full header with safe area extension
        VStack(spacing: 0) {
            // Actual header content
            HStack {
                // Left spacer (smaller to balance the right side)
                Spacer()
                    .frame(width: 10)

                Text("Pluto AI")
                    .font(.title3)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .lineLimit(1)
                    .fixedSize(horizontal: false, vertical: true)

                Spacer()

                HStack(spacing: 12) {
                    But<PERSON>(action: {
                        showProSubscription = true
                    }) {
                        Text("PRO")
                            .font(.caption)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color(red: 0, green: 0.8, blue: 0.6)) // Teal color
                            .cornerRadius(8)
                    }

                    Button(action: {
                        // Open settings
                        showSettings = true
                    }) {
                        Image(systemName: "gearshape.fill")
                            .font(.system(size: 20))
                            .foregroundColor(.white)
                    }
                    .padding(.trailing, 5)
                }
            }
            .padding(.horizontal)
            .padding(.top, 50) // Add padding for status bar
            .padding(.bottom, 5)
        }
        .background(Color.black)
        .edgesIgnoringSafeArea(.top) // Extend to top edge
        .sheet(isPresented: $showProSubscription) {
            ProSubscriptionView()
        }
    }
}
