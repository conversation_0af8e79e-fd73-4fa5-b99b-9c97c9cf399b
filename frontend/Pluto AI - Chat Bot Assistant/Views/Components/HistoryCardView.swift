//
//  HistoryCardView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.04.30.
//

import SwiftUI

struct HistoryCardView: View {
    let history: ChatHistory
    @State private var navigateToChat = false
    @Environment(\.managedObjectContext) private var viewContext

    var body: some View {
        Button(action: {
            navigateToChat = true
        }) {
            VStack(alignment: .leading) {
                Text(history.title)
                    .font(.headline)
                    .foregroundColor(.white)
                    .lineLimit(1)

                Text(history.description)
                    .font(.caption)
                    .foregroundColor(.gray)

                Text(history.date, style: .date)
                    .font(.caption2)
                    .foregroundColor(.gray)
            }
            .padding()
            .frame(maxWidth: .infinity, alignment: .leading)
            .background(Color.systemGray6)
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
        .sheet(isPresented: $navigateToChat) {
            NavigationView {
                // Create a new ChatScreenView with the messages from history
                ChatScreenView(withMessages: history.messages)
            }
        }
    }
}

#Preview {
    HistoryCardView(history: ChatHist<PERSON>(title: "Sample Chat", date: Date()))
        .preferredColorScheme(.dark)
        .padding()
}
