//
//  ToolCardView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.04.30.
//

import SwiftUI

struct ToolCardView: View {
    let tool: Tool
    @State private var showChatScreen = false

    var body: some View {
        Button(action: {
            showChatScreen = true
        }) {
            VStack(spacing: 2) {
                // Tool icon - fixed position at top
                VStack {
                    if let assetImageName = tool.assetImageName {
                        Image(assetImageName)
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 95, height: 95)
                            .background(Color.black) // Consistent black background
                            .clipShape(RoundedRectangle(cornerRadius: 12))
                    } else {
                        // Fallback to SF Symbol if no asset image
                        Image(systemName: tool.icon)
                            .font(.system(size: 48))
                            .foregroundColor(.white)
                            .frame(width: 95, height: 95)
                            .background(Color.black) // Consistent black background
                            .clipShape(RoundedRectangle(cornerRadius: 12))
                    }
                }
                .frame(height: 120) // Fixed height for icon area

                // Tool name - fixed position at bottom
                VStack {
                    Text(tool.name)
                        .font(.system(size: 18, weight: .bold))
                        .foregroundColor(.white)
                        .multilineTextAlignment(.center)
                        .lineLimit(2)
                        .fixedSize(horizontal: false, vertical: true)
                }
                .frame(height: 50) // Fixed height for text area
            }
            .frame(width: 130, height: 170) // Fixed total frame
            .background(Color.clear) // No background for tighter spacing
            .clipShape(RoundedRectangle(cornerRadius: 16))
        }
        .buttonStyle(PlainButtonStyle())
        // Use sheet for AI Image Generator and Voice Chat
        .sheet(isPresented: $showChatScreen, content: {
            if tool.name == "AI Image Generator" {
                // Open the specialized AI Image Generator view as a popup
                AIImageGeneratorView()
            } else if tool.name == "Voice Chat" {
                // Open the specialized Voice Chat view as a popup
                VoiceChatView()
            } else if tool.name == "Browsing Chat" {
                // Open the specialized Browsing Chat view as a popup
                BrowsingChatPopupView()
            } else if tool.name == "YouTube Summary" {
                // Open the specialized YouTube Summary view as a popup
                YouTubeSummaryPopupView()
            } else if tool.name == "Upload & Ask" {
                // Open the specialized Upload & Ask view as a popup
                UploadAndAskPopupView()
            } else if tool.name == "Link & Ask" {
                // Open the specialized Link & Ask view as a popup
                LinkAndAskPopupView()
            }
        })
        .fullScreenCover(item: Binding<Tool?>(
            get: { tool.name != "AI Image Generator" && tool.name != "Voice Chat" && tool.name != "Browsing Chat" && tool.name != "YouTube Summary" && tool.name != "Upload & Ask" && tool.name != "Link & Ask" && showChatScreen ? tool : nil },
            set: { newValue in showChatScreen = newValue != nil }
        )) { _ in
            NavigationView {
                // For other tools, create a specialized chat view without auto-sending
                // Create a suggestion object for this tool
                let toolSuggestion = Suggestion(
                    text: "How can I use the \(tool.name)?",
                    icon: tool.icon,
                    category: tool.name,
                    examplePrompts: [
                        "What are the main features of \(tool.name)?",
                        "How can \(tool.name) help me with my tasks?",
                        "Show me examples of what \(tool.name) can do"
                    ]
                )

                // Pass the suggestion to ChatScreenView without auto-sending
                ChatScreenView(suggestion: toolSuggestion, autoSend: false, showExamplesOnAppear: false, assistant: nil)
            }
        }
    }
}

#Preview {
    ToolCardView(tool: Tool.eliteTools[0])
        .preferredColorScheme(.dark)
}
