//
//  VoiceSelectionView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.05.01.
//

import SwiftUI
import AVFoundation

struct VoiceSelectionView: View {
    @Environment(\.dismiss) private var dismiss
    @Binding var selectedVoice: String?
    @State private var selectedVoiceOption: String = "Luna"
    @StateObject private var ttsService = TextToSpeechService.shared

    // Voice options from the TTS service
    let voiceOptions = VoiceModel.availableVoices

    var body: some View {
        ZStack {
            // Background color
            Color.black.edgesIgnoringSafeArea(.all)

            // Content
            VStack(spacing: 20) {
                // Header with close and volume buttons
                HStack {
                    Button(action: {
                        // Toggle volume/mute
                    }) {
                        Image(systemName: "speaker.wave.2.fill")
                            .font(.system(size: 20))
                            .foregroundColor(.white)
                    }

                    Spacer()

                    But<PERSON>(action: {
                        dismiss()
                    }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 20))
                            .foregroundColor(.white)
                    }
                }
                .padding(.horizontal)
                .padding(.top, 10)

                // Title
                Text("Choose a voice")
                    .font(.system(size: 34, weight: .bold))
                    .foregroundColor(.white)
                    .padding(.top, 10)

                // Subtitle
                Text("You can change this selection later.")
                    .font(.subheadline)
                    .foregroundColor(.gray)
                    .padding(.bottom, 20)

                // Audio waveform visualization
                HStack(spacing: 10) {
                    Rectangle()
                        .fill(Color.white)
                        .frame(width: 8, height: 80)
                        .cornerRadius(4)

                    Rectangle()
                        .fill(Color.white)
                        .frame(width: 8, height: 40)
                        .cornerRadius(4)

                    Rectangle()
                        .fill(Color.white)
                        .frame(width: 8, height: 80)
                        .cornerRadius(4)

                    Rectangle()
                        .fill(Color.white)
                        .frame(width: 8, height: 40)
                        .cornerRadius(4)

                    Rectangle()
                        .fill(Color.white)
                        .frame(width: 8, height: 80)
                        .cornerRadius(4)
                }
                .padding(.bottom, 30)

                // Voice options
                VStack(spacing: 12) {
                    ForEach(voiceOptions) { voice in
                        VoiceOptionCard(
                            voiceModel: voice,
                            isSelected: selectedVoiceOption == voice.name,
                            onSelect: {
                                selectedVoiceOption = voice.name
                                ttsService.setVoice(voice)
                            },
                            onPreview: {
                                previewVoice(voice)
                            }
                        )
                    }
                }
                .padding(.horizontal)

                Spacer()

                // Continue button
                Button(action: {
                    // Set the selected voice and dismiss
                    selectedVoice = selectedVoiceOption
                    dismiss()
                }) {
                    Text("Continue")
                        .font(.headline)
                        .foregroundColor(.black)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.white)
                        .cornerRadius(12)
                        .padding(.horizontal)
                }
                .padding(.bottom, 30)
            }
        }
    }

    private func previewVoice(_ voice: VoiceModel) {
        let previewText = "Hello, I'm \(voice.name). This is how I sound when speaking."
        ttsService.setVoice(voice)
        ttsService.speak(text: previewText)
    }
}

struct VoiceOptionCard: View {
    let voiceModel: VoiceModel
    let isSelected: Bool
    let onSelect: () -> Void
    let onPreview: () -> Void

    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(voiceModel.name)
                    .font(.headline)
                    .foregroundColor(.white)

                Text(voiceModel.description)
                    .font(.caption)
                    .foregroundColor(.gray)

                HStack {
                    Text(voiceModel.gender)
                        .font(.caption2)
                        .foregroundColor(.white.opacity(0.7))
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(Color.white.opacity(0.2))
                        .cornerRadius(4)

                    Text(voiceModel.language)
                        .font(.caption2)
                        .foregroundColor(.white.opacity(0.7))
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(Color.white.opacity(0.2))
                        .cornerRadius(4)
                }
            }

            Spacer()

            // Preview button
            Button(action: onPreview) {
                Image(systemName: "play.circle.fill")
                    .font(.title2)
                    .foregroundColor(.white)
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding()
        .background(
            isSelected ?
            Color(red: 0, green: 0.4, blue: 0.3) : // Dark teal for selected
            Color(UIColor.systemGray6).opacity(0.3) // Dark gray for unselected
        )
        .cornerRadius(12)
        .onTapGesture {
            onSelect()
        }
    }
}

#Preview {
    VoiceSelectionView(selectedVoice: .constant(nil))
}
