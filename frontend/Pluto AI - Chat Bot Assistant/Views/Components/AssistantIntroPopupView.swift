//
//  AssistantIntroPopupView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.05.01.
//

import SwiftUI

struct AssistantIntroPopupView: View {
    let assistant: Assistant
    let onContinue: () -> Void
    let onDismiss: () -> Void
    let onPromptSelected: ((String) -> Void)?

    @State private var isVisible = false
    @State private var dragOffset = CGSize.zero

    init(assistant: Assistant, onContinue: @escaping () -> Void, onDismiss: @escaping () -> Void, onPromptSelected: ((String) -> Void)? = nil) {
        self.assistant = assistant
        self.onContinue = onContinue
        self.onDismiss = onDismiss
        self.onPromptSelected = onPromptSelected
    }

    var body: some View {
        ZStack {
            // Background overlay
            Color.black.opacity(0.6)
                .edgesIgnoringSafeArea(.all)
                .onTapGesture {
                    dismissPopup()
                }

            VStack {
                Spacer()

                // Main popup content
                VStack(spacing: 0) {
                    // Drag indicator
                    RoundedRectangle(cornerRadius: 2.5)
                        .fill(Color.gray.opacity(0.4))
                        .frame(width: 40, height: 5)
                        .padding(.top, 12)
                        .padding(.bottom, 8)

                    // Close button
                    HStack {
                        Spacer()
                        Button(action: dismissPopup) {
                            Image(systemName: "xmark")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(.gray)
                                .padding(8)
                                .background(Color.gray.opacity(0.2))
                                .clipShape(Circle())
                        }
                        .padding(.trailing, 20)
                        .padding(.top, 8)
                    }

                    // Assistant image
                    ZStack {
                        // Background image that fills the area
                        Group {
                            if let uiImage = UIImage(named: assistant.name) {
                                Image(uiImage: uiImage)
                                    .resizable()
                                    .aspectRatio(contentMode: .fill)
                                    .frame(width: 300, height: 200)
                                    .clipped()
                                    .cornerRadius(16)
                            } else {
                                // Fallback gradient background
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        assistant.color.opacity(0.8),
                                        assistant.color.opacity(0.4)
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                                .frame(width: 300, height: 200)
                                .cornerRadius(16)
                                .overlay(
                                    Image(systemName: assistant.iconName)
                                        .font(.system(size: 60, weight: .medium))
                                        .foregroundColor(.white.opacity(0.3))
                                )
                            }
                        }

                        // Dark gradient overlay for text readability
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.clear,
                                Color.black.opacity(0.7)
                            ]),
                            startPoint: .top,
                            endPoint: .bottom
                        )
                        .frame(width: 300, height: 200)
                        .cornerRadius(16)

                        // Assistant name overlay
                        VStack {
                            Spacer()
                            HStack {
                                Text(assistant.name)
                                    .font(.title2)
                                    .fontWeight(.bold)
                                    .foregroundColor(.white)
                                    .shadow(color: .black.opacity(0.5), radius: 2, x: 0, y: 1)
                                Spacer()
                            }
                            .padding(.horizontal, 20)
                            .padding(.bottom, 16)
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 16)

                    // Description
                    Text(assistant.description)
                        .font(.subheadline)
                        .foregroundColor(.gray)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, 24)
                        .padding(.top, 16)

                    // Compatibility section
                    VStack(spacing: 12) {
                        Text("Compatibility")
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)

                        HStack(spacing: 8) {
                            Image(systemName: "brain.head.profile")
                                .font(.system(size: 16))
                                .foregroundColor(.orange)

                            Text("AI Assistant")
                                .font(.subheadline)
                                .foregroundColor(.white)

                            Spacer()
                        }
                        .padding(.horizontal, 16)
                        .padding(.vertical, 12)
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(12)
                        .padding(.horizontal, 24)
                    }
                    .padding(.top, 20)

                    // Suggested prompts section
                    if let suggestedPrompts = assistant.suggestedPrompts, !suggestedPrompts.isEmpty {
                        VStack(spacing: 16) {
                            Text("Try asking me...")
                                .font(.headline)
                                .fontWeight(.semibold)
                                .foregroundColor(.white)

                            VStack(spacing: 12) {
                                ForEach(suggestedPrompts) { prompt in
                                    Button(action: {
                                        onPromptSelected?(prompt.text)
                                        onContinue()
                                    }) {
                                        HStack(spacing: 12) {
                                            Image(systemName: prompt.icon)
                                                .font(.system(size: 16))
                                                .foregroundColor(.white)
                                                .frame(width: 24, height: 24)

                                            VStack(alignment: .leading, spacing: 4) {
                                                Text(prompt.text)
                                                    .font(.subheadline)
                                                    .fontWeight(.medium)
                                                    .foregroundColor(.white)
                                                    .multilineTextAlignment(.leading)

                                                if let description = prompt.description {
                                                    Text(description)
                                                        .font(.caption)
                                                        .foregroundColor(.gray)
                                                        .multilineTextAlignment(.leading)
                                                }
                                            }

                                            Spacer()

                                            Image(systemName: "arrow.right.circle.fill")
                                                .font(.system(size: 16))
                                                .foregroundColor(.white.opacity(0.6))
                                        }
                                        .padding(.horizontal, 16)
                                        .padding(.vertical, 12)
                                        .background(Color.gray.opacity(0.2))
                                        .cornerRadius(12)
                                    }
                                    .buttonStyle(PlainButtonStyle())
                                }
                            }
                        }
                        .padding(.horizontal, 24)
                        .padding(.top, 20)
                    }

                    // Continue button
                    Button(action: {
                        onContinue()
                    }) {
                        Text("Continue")
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.black)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 16)
                            .background(Color.white)
                            .cornerRadius(16)
                    }
                    .padding(.horizontal, 24)
                    .padding(.top, 24)
                    .padding(.bottom, 32)
                }
                .background(Color.black)
                .cornerRadius(24, corners: [.topLeft, .topRight])
                .offset(y: isVisible ? 0 : UIScreen.main.bounds.height)
                .offset(y: dragOffset.height > 0 ? dragOffset.height : 0)
                .gesture(
                    DragGesture()
                        .onChanged { value in
                            if value.translation.height > 0 {
                                dragOffset = value.translation
                            }
                        }
                        .onEnded { value in
                            if value.translation.height > 150 {
                                dismissPopup()
                            } else {
                                withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                                    dragOffset = .zero
                                }
                            }
                        }
                )
            }
        }
        .onAppear {
            withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                isVisible = true
            }
        }
    }

    private func dismissPopup() {
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            isVisible = false
        }

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            onDismiss()
        }
    }
}

// Extension to add corner radius to specific corners
extension View {
    func cornerRadius(_ radius: CGFloat, corners: UIRectCorner) -> some View {
        clipShape(RoundedCorner(radius: radius, corners: corners))
    }
}

struct RoundedCorner: Shape {
    var radius: CGFloat = .infinity
    var corners: UIRectCorner = .allCorners

    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(
            roundedRect: rect,
            byRoundingCorners: corners,
            cornerRadii: CGSize(width: radius, height: radius)
        )
        return Path(path.cgPath)
    }
}

#Preview {
    AssistantIntroPopupView(
        assistant: Assistant.assistants[0],
        onContinue: {},
        onDismiss: {},
        onPromptSelected: { prompt in
            print("Selected prompt: \(prompt)")
        }
    )
    .preferredColorScheme(.dark)
}
