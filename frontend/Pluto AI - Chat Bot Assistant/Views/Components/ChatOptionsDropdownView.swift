//
//  ChatOptionsDropdownView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.05.01.
//

import SwiftUI

struct ChatOptionsDropdownView: View {
    let onHistoryTapped: () -> Void
    let onAutoReadToggled: () -> Void
    let onFontSizeTapped: () -> Void
    let onDismiss: () -> Void
    
    @State private var autoReadEnabled = true
    @State private var isVisible = false
    
    var body: some View {
        ZStack {
            // Background overlay
            Color.black.opacity(0.3)
                .edgesIgnoringSafeArea(.all)
                .onTapGesture {
                    withAnimation(.easeOut(duration: 0.2)) {
                        isVisible = false
                    }
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                        onDismiss()
                    }
                }
            
            VStack {
                HStack {
                    Spacer()
                    
                    // Dropdown menu
                    VStack(spacing: 0) {
                        // History option
                        Button(action: {
                            withAnimation(.easeOut(duration: 0.2)) {
                                isVisible = false
                            }
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                                onHistoryTapped()
                                onDismiss()
                            }
                        }) {
                            HStack(spacing: 12) {
                                Image(systemName: "clock.arrow.circlepath")
                                    .font(.system(size: 16))
                                    .foregroundColor(.white)
                                
                                Text("History")
                                    .font(.system(size: 16))
                                    .foregroundColor(.white)
                                
                                Spacer()
                            }
                            .padding(.horizontal, 16)
                            .padding(.vertical, 12)
                        }
                        
                        Divider()
                            .background(Color.gray.opacity(0.3))
                        
                        // Auto-Read option
                        Button(action: {
                            autoReadEnabled.toggle()
                            onAutoReadToggled()
                        }) {
                            HStack(spacing: 12) {
                                Image(systemName: autoReadEnabled ? "speaker.wave.2.fill" : "speaker.slash.fill")
                                    .font(.system(size: 16))
                                    .foregroundColor(.white)
                                
                                Text("Auto-Read: \(autoReadEnabled ? "On" : "Off")")
                                    .font(.system(size: 16))
                                    .foregroundColor(.white)
                                
                                Spacer()
                            }
                            .padding(.horizontal, 16)
                            .padding(.vertical, 12)
                        }
                        
                        Divider()
                            .background(Color.gray.opacity(0.3))
                        
                        // Font Size option
                        Button(action: {
                            withAnimation(.easeOut(duration: 0.2)) {
                                isVisible = false
                            }
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                                onFontSizeTapped()
                                onDismiss()
                            }
                        }) {
                            HStack(spacing: 12) {
                                Image(systemName: "textformat.size")
                                    .font(.system(size: 16))
                                    .foregroundColor(.white)
                                
                                Text("Font Size")
                                    .font(.system(size: 16))
                                    .foregroundColor(.white)
                                
                                Spacer()
                            }
                            .padding(.horizontal, 16)
                            .padding(.vertical, 12)
                        }
                    }
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.black.opacity(0.9))
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                            )
                    )
                    .frame(width: 180)
                    .scaleEffect(isVisible ? 1.0 : 0.8)
                    .opacity(isVisible ? 1.0 : 0.0)
                    .padding(.trailing, 16)
                }
                .padding(.top, 100) // Position below the toolbar
                
                Spacer()
            }
        }
        .onAppear {
            withAnimation(.easeOut(duration: 0.2)) {
                isVisible = true
            }
        }
    }
}

#Preview {
    ChatOptionsDropdownView(
        onHistoryTapped: {},
        onAutoReadToggled: {},
        onFontSizeTapped: {},
        onDismiss: {}
    )
    .preferredColorScheme(.dark)
}
