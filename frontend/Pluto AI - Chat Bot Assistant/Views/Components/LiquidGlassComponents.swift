//
//  LiquidGlassComponents.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.01.18.
//  Liquid Glass Design System Implementation
//

import SwiftUI

// MARK: - Liquid Glass Material Effects

struct LiquidGlassMaterial: ViewModifier {
    let intensity: Double
    let tint: Color
    let cornerRadius: CGFloat
    
    init(intensity: Double = 0.8, tint: Color = .clear, cornerRadius: CGFloat = 16) {
        self.intensity = intensity
        self.tint = tint
        self.cornerRadius = cornerRadius
    }
    
    func body(content: Content) -> some View {
        content
            .background(
                ZStack {
                    // Base glass effect
                    RoundedRectangle(cornerRadius: cornerRadius)
                        .fill(.ultraThinMaterial)
                        .opacity(intensity)
                    
                    // Tint overlay
                    if tint != .clear {
                        RoundedRectangle(cornerRadius: cornerRadius)
                            .fill(tint.opacity(0.1))
                    }
                    
                    // Subtle border
                    RoundedRectangle(cornerRadius: cornerRadius)
                        .stroke(
                            LinearGradient(
                                colors: [
                                    .white.opacity(0.2),
                                    .white.opacity(0.05)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 0.5
                        )
                }
            )
    }
}

struct LiquidGlassCard: ViewModifier {
    let elevation: CGFloat
    let cornerRadius: CGFloat
    let glowColor: Color
    
    init(elevation: CGFloat = 8, cornerRadius: CGFloat = 20, glowColor: Color = .blue) {
        self.elevation = elevation
        self.cornerRadius = cornerRadius
        self.glowColor = glowColor
    }
    
    func body(content: Content) -> some View {
        content
            .background(
                ZStack {
                    // Shadow layer
                    RoundedRectangle(cornerRadius: cornerRadius)
                        .fill(.black.opacity(0.3))
                        .blur(radius: elevation)
                        .offset(y: elevation / 2)
                    
                    // Glass background
                    RoundedRectangle(cornerRadius: cornerRadius)
                        .fill(.ultraThinMaterial)
                    
                    // Subtle glow
                    RoundedRectangle(cornerRadius: cornerRadius)
                        .stroke(glowColor.opacity(0.3), lineWidth: 1)
                        .blur(radius: 2)
                    
                    // Border highlight
                    RoundedRectangle(cornerRadius: cornerRadius)
                        .stroke(
                            LinearGradient(
                                colors: [
                                    .white.opacity(0.3),
                                    .white.opacity(0.1),
                                    .clear,
                                    .white.opacity(0.1)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1
                        )
                }
            )
    }
}

// MARK: - Liquid Glass Button Styles

struct LiquidGlassButtonStyle: ButtonStyle {
    let variant: Variant
    let size: Size
    
    enum Variant {
        case primary, secondary, ghost
        
        var colors: (background: Color, foreground: Color, glow: Color) {
            switch self {
            case .primary:
                return (.blue, .white, .blue)
            case .secondary:
                return (.gray.opacity(0.3), .primary, .gray)
            case .ghost:
                return (.clear, .primary, .clear)
            }
        }
    }
    
    enum Size {
        case small, medium, large
        
        var dimensions: (height: CGFloat, padding: CGFloat, fontSize: CGFloat) {
            switch self {
            case .small:
                return (36, 12, 14)
            case .medium:
                return (44, 16, 16)
            case .large:
                return (52, 20, 18)
            }
        }
    }
    
    func makeBody(configuration: Configuration) -> some View {
        let colors = variant.colors
        let dimensions = size.dimensions
        
        configuration.label
            .font(.system(size: dimensions.fontSize, weight: .medium))
            .foregroundColor(colors.foreground)
            .frame(height: dimensions.height)
            .padding(.horizontal, dimensions.padding)
            .background(
                ZStack {
                    if variant != .ghost {
                        // Glass background
                        RoundedRectangle(cornerRadius: dimensions.height / 2)
                            .fill(.ultraThinMaterial)
                        
                        // Color overlay
                        RoundedRectangle(cornerRadius: dimensions.height / 2)
                            .fill(colors.background.opacity(0.8))
                        
                        // Glow effect
                        RoundedRectangle(cornerRadius: dimensions.height / 2)
                            .stroke(colors.glow.opacity(0.5), lineWidth: 1)
                            .blur(radius: 2)
                    }
                    
                    // Border
                    RoundedRectangle(cornerRadius: dimensions.height / 2)
                        .stroke(
                            LinearGradient(
                                colors: [
                                    .white.opacity(variant == .ghost ? 0.2 : 0.4),
                                    .white.opacity(variant == .ghost ? 0.1 : 0.2)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1
                        )
                }
            )
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .opacity(configuration.isPressed ? 0.8 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// MARK: - Liquid Glass Input Field

struct LiquidGlassTextField: View {
    @Binding var text: String
    let placeholder: String
    let icon: String?
    
    @FocusState private var isFocused: Bool
    
    init(_ placeholder: String, text: Binding<String>, icon: String? = nil) {
        self.placeholder = placeholder
        self._text = text
        self.icon = icon
    }
    
    var body: some View {
        HStack(spacing: 12) {
            if let icon = icon {
                Image(systemName: icon)
                    .foregroundColor(.secondary)
                    .font(.system(size: 16, weight: .medium))
            }
            
            TextField(placeholder, text: $text)
                .focused($isFocused)
                .textFieldStyle(PlainTextFieldStyle())
                .font(.system(size: 16))
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            ZStack {
                // Glass background
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
                
                // Focus glow
                if isFocused {
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(.blue.opacity(0.5), lineWidth: 2)
                        .blur(radius: 4)
                }
                
                // Border
                RoundedRectangle(cornerRadius: 12)
                    .stroke(
                        LinearGradient(
                            colors: [
                                .white.opacity(isFocused ? 0.4 : 0.2),
                                .white.opacity(isFocused ? 0.2 : 0.1)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 1
                    )
            }
        )
        .animation(.easeInOut(duration: 0.2), value: isFocused)
    }
}

// MARK: - Liquid Glass Navigation Bar

struct LiquidGlassNavigationBar: View {
    let title: String
    let leadingAction: (() -> Void)?
    let trailingAction: (() -> Void)?
    let leadingIcon: String?
    let trailingIcon: String?
    
    init(
        title: String,
        leadingIcon: String? = nil,
        leadingAction: (() -> Void)? = nil,
        trailingIcon: String? = nil,
        trailingAction: (() -> Void)? = nil
    ) {
        self.title = title
        self.leadingIcon = leadingIcon
        self.leadingAction = leadingAction
        self.trailingIcon = trailingIcon
        self.trailingAction = trailingAction
    }
    
    var body: some View {
        HStack {
            // Leading button
            if let leadingIcon = leadingIcon, let leadingAction = leadingAction {
                Button(action: leadingAction) {
                    Image(systemName: leadingIcon)
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.primary)
                        .frame(width: 32, height: 32)
                        .background(.ultraThinMaterial, in: Circle())
                }
            } else {
                Spacer()
                    .frame(width: 32)
            }
            
            Spacer()
            
            // Title
            Text(title)
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.primary)
            
            Spacer()
            
            // Trailing button
            if let trailingIcon = trailingIcon, let trailingAction = trailingAction {
                Button(action: trailingAction) {
                    Image(systemName: trailingIcon)
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.primary)
                        .frame(width: 32, height: 32)
                        .background(.ultraThinMaterial, in: Circle())
                }
            } else {
                Spacer()
                    .frame(width: 32)
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 12)
        .background(
            .ultraThinMaterial,
            in: RoundedRectangle(cornerRadius: 0)
        )
        .overlay(
            Rectangle()
                .frame(height: 0.5)
                .foregroundColor(.white.opacity(0.1)),
            alignment: .bottom
        )
    }
}

// MARK: - View Extensions

// MARK: - Liquid Glass Enhanced Components

struct LiquidGlassSectionHeader: View {
    let title: String
    let showSeeAll: Bool
    let seeAllAction: (() -> Void)?

    init(title: String, showSeeAll: Bool = false, seeAllAction: (() -> Void)? = nil) {
        self.title = title
        self.showSeeAll = showSeeAll
        self.seeAllAction = seeAllAction
    }

    var body: some View {
        HStack {
            Text(title)
                .font(.system(size: 22, weight: .bold))
                .foregroundColor(.primary)

            Spacer()

            if showSeeAll, let action = seeAllAction {
                Button("See All", action: action)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.blue)
            }
        }
        .padding(.horizontal, 20)
    }
}

struct LiquidGlassToolCard: View {
    let tool: Tool
    @State private var isPressed = false

    var body: some View {
        VStack(spacing: 8) {
            // Icon
            Image(systemName: tool.icon)
                .font(.system(size: 24, weight: .medium))
                .foregroundColor(.white)
                .frame(width: 48, height: 48)
                .background(
                    Circle()
                        .fill(tool.iconBackgroundColor.opacity(0.8))
                        .overlay(
                            Circle()
                                .stroke(.white.opacity(0.2), lineWidth: 1)
                        )
                )
                .shadow(color: tool.iconBackgroundColor.opacity(0.3), radius: 8)

            // Name
            Text(tool.name)
                .font(.system(size: 14, weight: .semibold))
                .foregroundColor(.primary)
                .multilineTextAlignment(.center)
                .lineLimit(2)
        }
        .frame(width: 80)
        .scaleEffect(isPressed ? 0.95 : 1.0)
        .animation(LiquidGlassAnimations.gentle, value: isPressed)
        .onTapGesture {
            withAnimation(LiquidGlassAnimations.bounce) {
                isPressed = true
            }

            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(LiquidGlassAnimations.gentle) {
                    isPressed = false
                }
            }
        }
    }
}

struct LiquidGlassAssistantCard: View {
    let assistant: Assistant
    @State private var showChatScreen = false

    var body: some View {
        Button(action: {
            showChatScreen = true
        }) {
            ZStack {
                // Background image or gradient
                Group {
                    if let uiImage = UIImage(named: assistant.name) {
                        Image(uiImage: uiImage)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                    } else {
                        LinearGradient(
                            gradient: Gradient(colors: [
                                assistant.color.opacity(0.8),
                                assistant.color.opacity(0.4)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                        .overlay(
                            Image(systemName: assistant.iconName)
                                .font(.system(size: 40, weight: .medium))
                                .foregroundColor(.white.opacity(0.3))
                        )
                    }
                }
                .frame(width: 160, height: 160)
                .clipped()

                // Glass overlay
                RoundedRectangle(cornerRadius: 20)
                    .fill(.ultraThinMaterial)
                    .opacity(0.3)

                // Gradient overlay for text readability
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.clear,
                        Color.black.opacity(0.6)
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                )

                // Content
                VStack {
                    Spacer()

                    VStack(alignment: .leading, spacing: 4) {
                        Text(assistant.name)
                            .font(.system(size: 16, weight: .bold))
                            .foregroundColor(.white)
                            .shadow(color: .black.opacity(0.5), radius: 2)

                        Text(assistant.description)
                            .font(.system(size: 12))
                            .foregroundColor(.white.opacity(0.9))
                            .lineLimit(2)
                            .shadow(color: .black.opacity(0.5), radius: 1)
                    }
                    .padding(.horizontal, 12)
                    .padding(.bottom, 12)
                    .frame(maxWidth: .infinity, alignment: .leading)
                }
            }
            .frame(width: 160, height: 160)
            .liquidGlassCard(elevation: 12, cornerRadius: 20, glowColor: assistant.color)
            .interactiveHover(scale: 1.05, glowColor: assistant.color)
        }
        .buttonStyle(PlainButtonStyle())
        .fullScreenCover(isPresented: $showChatScreen) {
            NavigationView {
                let assistantSuggestion = Suggestion(
                    text: "I'd like to work with the \(assistant.name) assistant",
                    icon: assistant.iconName,
                    category: assistant.name,
                    examplePrompts: [
                        "What can you help me with?",
                        "Show me what you're capable of",
                        "Let's get started with a conversation"
                    ]
                )

                ChatScreenView(suggestion: assistantSuggestion, autoSend: false, showExamplesOnAppear: false, assistant: assistant)
            }
        }
    }
}

struct LiquidGlassCreateAssistantCard: View {
    @State private var showCreateAssistant = false

    var body: some View {
        Button(action: {
            showCreateAssistant = true
        }) {
            ZStack {
                // Glass background
                RoundedRectangle(cornerRadius: 20)
                    .fill(.ultraThinMaterial)

                // Content
                VStack(spacing: 12) {
                    Image(systemName: "plus")
                        .font(.system(size: 32, weight: .medium))
                        .foregroundColor(.blue)

                    Text("Create Assistant")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.center)
                }
            }
            .frame(width: 160, height: 160)
            .liquidGlassCard(elevation: 8, cornerRadius: 20, glowColor: .blue)
            .interactiveHover(scale: 1.05, glowColor: .blue)
        }
        .buttonStyle(PlainButtonStyle())
        .fullScreenCover(isPresented: $showCreateAssistant) {
            CreateAssistantView()
        }
    }
}

extension View {
    func liquidGlass(
        intensity: Double = 0.8,
        tint: Color = .clear,
        cornerRadius: CGFloat = 16
    ) -> some View {
        modifier(LiquidGlassMaterial(intensity: intensity, tint: tint, cornerRadius: cornerRadius))
    }

    func liquidGlassCard(
        elevation: CGFloat = 8,
        cornerRadius: CGFloat = 20,
        glowColor: Color = .blue
    ) -> some View {
        modifier(LiquidGlassCard(elevation: elevation, cornerRadius: cornerRadius, glowColor: glowColor))
    }
}
