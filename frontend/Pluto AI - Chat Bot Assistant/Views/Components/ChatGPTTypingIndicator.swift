//
//  ChatGPTTypingIndicator.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.04.30.
//

import SwiftUI

// MARK: - ChatGPT-Style Typing Indicator
struct ChatGPTTypingIndicator: View {
    @State private var animationPhase = 0
    @State private var opacity1: Double = 0.3
    @State private var opacity2: Double = 0.3
    @State private var opacity3: Double = 0.3
    
    var body: some View {
        HStack(spacing: 4) {
            // Three animated dots like ChatGPT
            Circle()
                .fill(Color.gray)
                .frame(width: 8, height: 8)
                .opacity(opacity1)
            
            Circle()
                .fill(Color.gray)
                .frame(width: 8, height: 8)
                .opacity(opacity2)
            
            Circle()
                .fill(Color.gray)
                .frame(width: 8, height: 8)
                .opacity(opacity3)
        }
        .onAppear {
            startAnimation()
        }
    }
    
    private func startAnimation() {
        // Staggered animation like ChatGPT
        Timer.scheduledTimer(withTimeInterval: 0.6, repeats: true) { _ in
            withAnimation(.easeInOut(duration: 0.6)) {
                // Cycle through the dots
                switch animationPhase {
                case 0:
                    opacity1 = 1.0
                    opacity2 = 0.3
                    opacity3 = 0.3
                case 1:
                    opacity1 = 0.3
                    opacity2 = 1.0
                    opacity3 = 0.3
                case 2:
                    opacity1 = 0.3
                    opacity2 = 0.3
                    opacity3 = 1.0
                default:
                    opacity1 = 0.3
                    opacity2 = 0.3
                    opacity3 = 0.3
                }
                
                animationPhase = (animationPhase + 1) % 3
            }
        }
    }
}

// MARK: - AI Thinking Bubble (ChatGPT Style)
struct AIThinkingBubble: View {
    @State private var isAnimating = false
    
    var body: some View {
        HStack(spacing: 12) {
            // AI Avatar (optional)
            Circle()
                .fill(LinearGradient(
                    gradient: Gradient(colors: [Color.blue.opacity(0.8), Color.purple.opacity(0.8)]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                ))
                .frame(width: 32, height: 32)
                .overlay(
                    Image(systemName: "brain.head.profile")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                )
            
            // Thinking bubble with typing dots
            HStack(spacing: 8) {
                ChatGPTTypingIndicator()
                
                Text("Pluto AI is typing")
                    .font(.caption)
                    .foregroundColor(.gray)
                    .opacity(0.8)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color.systemGray6)
                    .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
            )
            
            Spacer()
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
        .scaleEffect(isAnimating ? 1.0 : 0.95)
        .opacity(isAnimating ? 1.0 : 0.0)
        .onAppear {
            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                isAnimating = true
            }
        }
    }
}

// MARK: - Connection Status Indicator
struct ConnectionStatusIndicator: View {
    let status: TypingConnectionStatus
    @State private var pulseAnimation = false
    
    enum TypingConnectionStatus {
        case connecting
        case connected
        case streaming
        case error

        var color: Color {
            switch self {
            case .connecting: return .orange
            case .connected: return .green
            case .streaming: return .blue
            case .error: return .red
            }
        }

        var text: String {
            switch self {
            case .connecting: return "Connecting..."
            case .connected: return "Connected"
            case .streaming: return "Streaming"
            case .error: return "Connection Error"
            }
        }

        var icon: String {
            switch self {
            case .connecting: return "wifi.circle"
            case .connected: return "checkmark.circle.fill"
            case .streaming: return "waveform.circle.fill"
            case .error: return "exclamationmark.circle.fill"
            }
        }
    }
    
    var body: some View {
        HStack(spacing: 6) {
            Image(systemName: status.icon)
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(status.color)
                .scaleEffect(pulseAnimation ? 1.2 : 1.0)
                .animation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true), value: pulseAnimation)
            
            Text(status.text)
                .font(.caption2)
                .foregroundColor(status.color)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(
            Capsule()
                .fill(status.color.opacity(0.1))
        )
        .onAppear {
            if status == .connecting || status == .streaming {
                pulseAnimation = true
            }
        }
    }
}

// MARK: - Enhanced Message Loading State
struct MessageLoadingState: View {
    let stage: LoadingStage
    @State private var animationOffset: CGFloat = 0
    
    enum LoadingStage {
        case initializing
        case connecting
        case thinking
        case generating
        
        var text: String {
            switch self {
            case .initializing: return "Initializing..."
            case .connecting: return "Connecting to AI..."
            case .thinking: return "AI is thinking..."
            case .generating: return "Generating response..."
            }
        }
        
        var icon: String {
            switch self {
            case .initializing: return "gear"
            case .connecting: return "wifi"
            case .thinking: return "brain.head.profile"
            case .generating: return "text.cursor"
            }
        }
    }
    
    var body: some View {
        HStack(spacing: 12) {
            // Animated icon
            Image(systemName: stage.icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.blue)
                .rotationEffect(.degrees(animationOffset))
                .animation(.linear(duration: 2.0).repeatForever(autoreverses: false), value: animationOffset)
            
            // Loading text with subtle animation
            Text(stage.text)
                .font(.subheadline)
                .foregroundColor(.gray)
            
            Spacer()
            
            // Subtle progress indicator
            ProgressView()
                .scaleEffect(0.8)
                .tint(.blue)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.systemGray6)
                .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
        )
        .onAppear {
            animationOffset = 360
        }
    }
}

#Preview {
    VStack(spacing: 20) {
        ChatGPTTypingIndicator()
        AIThinkingBubble()
        ConnectionStatusIndicator(status: .connecting)
        ConnectionStatusIndicator(status: .streaming)
        MessageLoadingState(stage: .thinking)
    }
    .padding()
    .background(Color.black)
}
