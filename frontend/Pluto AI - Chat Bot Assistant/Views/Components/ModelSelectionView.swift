//
//  ModelSelectionView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.04.30.
//

import SwiftUI

struct ModelSelectionView: View {
    @Binding var isShowing: Bo<PERSON>
    @Binding var selectedModel: AIModel

    var body: some View {
        VStack(spacing: 0) {
            ForEach(AIModel.models) { model in
                Button(action: {
                    selectedModel = model
                    isShowing = false
                }) {
                    HStack {
                        // Use the actual model logo from assets
                        Image(model.iconName)
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 24, height: 24)

                        Text(model.name)
                            .foregroundColor(.white)

                        if model.isPro {
                            Text("PRO")
                                .font(.caption)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(Color.green)
                                .cornerRadius(4)
                        }

                        Spacer()

                        Button(action: {
                            // Show info about the model
                        }) {
                            Image(systemName: "info.circle")
                                .foregroundColor(.gray)
                        }
                    }
                    .padding(.vertical, 12)
                    .padding(.horizontal)
                    .background(selectedModel.id == model.id ? Color.systemGray5 : Color.clear)
                }
            }
        }
        .background(Color.systemGray6)
        .cornerRadius(12)
        .padding()
    }
}

#Preview {
    ModelSelectionView(
        isShowing: .constant(true),
        selectedModel: .constant(AIModel.models[0])
    )
    .preferredColorScheme(.dark)
}
