//
//  SectionHeaderView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.04.30.
//

import SwiftUI

struct SectionHeaderView: View {
    let title: String
    let showSeeAll: Bool
    var action: (() -> Void)? = nil

    var body: some View {
        HStack {
            Text(title)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.white)

            Spacer()

            if showSeeAll {
                // Debug: Print when the button should be shown
                let _ = print("SectionHeaderView: Showing 'See All' button for \(title)")
                But<PERSON>(action: {
                    print("See All button tapped for: \(title)")
                    action?()
                }) {
                    Text("See All")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.gray)
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding(.horizontal)
        .padding(.top)
    }
}

#Preview {
    SectionHeaderView(title: "Elite Tools", showSeeAll: true)
        .preferredColorScheme(.dark)
}
