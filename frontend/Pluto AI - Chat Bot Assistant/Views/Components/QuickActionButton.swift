//
//  QuickActionButton.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.04.30.
//

import SwiftUI

struct QuickActionButton: View {
    let title: String
    let icon: String
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(.white)
                Text(title)
                    .foregroundColor(.white)
            }
            .padding(.vertical, 8)
            .padding(.horizontal, 12)
            .background(Color.systemGray6)
            .cornerRadius(20)
        }
    }
}

#Preview {
    HStack {
        QuickActionButton(title: "Suggestions", icon: "lightbulb") {}
        QuickActionButton(title: "Elite Tools", icon: "gear") {}
    }
    .padding()
    .background(Color.black)
    .preferredColorScheme(.dark)
}
