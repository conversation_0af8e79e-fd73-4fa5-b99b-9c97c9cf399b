//
//  LiquidGlassUIComponents.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.01.18.
//  Additional Liquid Glass UI Components
//

import SwiftUI

// MARK: - Liquid Glass History Card

struct LiquidGlassHistoryCard: View {
    let history: ChatHistory
    @State private var isPressed = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Header
            HStack {
                Image(systemName: "message.circle.fill")
                    .font(.system(size: 16))
                    .foregroundColor(.blue)
                
                Text(history.title ?? "Chat")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.primary)
                    .lineLimit(1)
                
                Spacer()
            }
            
            // Preview text
            if let lastMessage = history.messages.last {
                Text(lastMessage.content)
                    .font(.system(size: 14))
                    .foregroundColor(.secondary)
                    .lineLimit(2)
                    .multilineTextAlignment(.leading)
            } else {
                Text(history.description)
                    .font(.system(size: 14))
                    .foregroundColor(.secondary)
                    .lineLimit(2)
                    .multilineTextAlignment(.leading)
            }
            
            Spacer()
            
            // Date
            Text(history.date, style: .relative)
                .font(.system(size: 12))
                .foregroundColor(.gray)
        }
        .padding(16)
        .frame(width: 200, height: 120)
        .liquidGlass(intensity: 0.7, cornerRadius: 16)
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(.blue.opacity(0.2), lineWidth: 1)
        )
        .scaleEffect(isPressed ? 0.95 : 1.0)
        .animation(LiquidGlassAnimations.gentle, value: isPressed)
        .onTapGesture {
            withAnimation(LiquidGlassAnimations.bounce) {
                isPressed = true
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(LiquidGlassAnimations.gentle) {
                    isPressed = false
                }
            }
        }
    }
}

// MARK: - Liquid Glass Category Pill

struct LiquidGlassCategoryPill: View {
    let category: SuggestionCategory
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 8) {
                Image(systemName: category.icon)
                    .font(.system(size: 14, weight: .medium))
                
                Text(category.name)
                    .font(.system(size: 14, weight: .medium))
            }
            .foregroundColor(isSelected ? .white : .primary)
            .padding(.horizontal, 16)
            .padding(.vertical, 10)
            .background(
                ZStack {
                    if isSelected {
                        // Selected state
                        RoundedRectangle(cornerRadius: 20)
                            .fill(category.color.opacity(0.8))
                        
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(category.color, lineWidth: 1)
                            .blur(radius: 2)
                    } else {
                        // Unselected state
                        RoundedRectangle(cornerRadius: 20)
                            .fill(.ultraThinMaterial)
                        
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(.white.opacity(0.2), lineWidth: 1)
                    }
                }
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 1.05 : 1.0)
        .animation(LiquidGlassAnimations.gentle, value: isSelected)
    }
}

// MARK: - Liquid Glass Suggestion Card

struct LiquidGlassSuggestionCard: View {
    let suggestion: Suggestion
    let action: () -> Void
    @State private var isPressed = false
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                // Icon
                Image(systemName: suggestion.icon)
                    .font(.system(size: 20, weight: .medium))
                    .foregroundColor(.blue)
                    .frame(width: 40, height: 40)
                    .background(
                        Circle()
                            .fill(.blue.opacity(0.1))
                            .overlay(
                                Circle()
                                    .stroke(.blue.opacity(0.3), lineWidth: 1)
                            )
                    )
                
                // Content
                VStack(alignment: .leading, spacing: 4) {
                    Text(suggestion.text)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.leading)
                        .lineLimit(2)
                    
                    Text("Tap to start conversation")
                        .font(.system(size: 12))
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // Arrow
                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.gray)
            }
            .padding(16)
            .liquidGlass(intensity: 0.6, cornerRadius: 12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(.white.opacity(0.1), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(LiquidGlassAnimations.gentle, value: isPressed)
        .onTapGesture {
            withAnimation(LiquidGlassAnimations.bounce) {
                isPressed = true
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(LiquidGlassAnimations.gentle) {
                    isPressed = false
                }
                action()
            }
        }
    }
}

// MARK: - Liquid Glass Chat Input

struct LiquidGlassChatInput: View {
    @Binding var messageText: String
    let onSend: () -> Void
    
    @State private var isRecording = false
    @FocusState private var isTextFieldFocused: Bool
    
    var body: some View {
        HStack(spacing: 12) {
            // Text input
            HStack(spacing: 12) {
                TextField("Message Pluto AI...", text: $messageText)
                    .focused($isTextFieldFocused)
                    .font(.system(size: 16))
                    .textFieldStyle(PlainTextFieldStyle())
                
                // Voice button
                Button(action: {
                    withAnimation(LiquidGlassAnimations.bounce) {
                        isRecording.toggle()
                    }
                }) {
                    Image(systemName: isRecording ? "stop.circle.fill" : "mic.circle.fill")
                        .font(.system(size: 24))
                        .foregroundColor(isRecording ? .red : .blue)
                        .scaleEffect(isRecording ? 1.2 : 1.0)
                        .animation(LiquidGlassAnimations.gentle, value: isRecording)
                }
                .buttonStyle(PlainButtonStyle())
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                ZStack {
                    RoundedRectangle(cornerRadius: 25)
                        .fill(.ultraThinMaterial)
                    
                    if isTextFieldFocused {
                        RoundedRectangle(cornerRadius: 25)
                            .stroke(.blue.opacity(0.5), lineWidth: 2)
                            .blur(radius: 4)
                    }
                    
                    RoundedRectangle(cornerRadius: 25)
                        .stroke(
                            LinearGradient(
                                colors: [
                                    .white.opacity(0.3),
                                    .white.opacity(0.1)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1
                        )
                }
            )
            
            // Send button
            Button(action: onSend) {
                Image(systemName: "arrow.up.circle.fill")
                    .font(.system(size: 32))
                    .foregroundColor(messageText.isEmpty ? .gray : .blue)
                    .background(
                        Circle()
                            .fill(.ultraThinMaterial)
                            .frame(width: 36, height: 36)
                    )
            }
            .buttonStyle(PlainButtonStyle())
            .disabled(messageText.isEmpty)
            .scaleEffect(messageText.isEmpty ? 0.9 : 1.0)
            .animation(LiquidGlassAnimations.gentle, value: messageText.isEmpty)
        }
        .padding(.horizontal, 4)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 30)
                .fill(.ultraThinMaterial)
                .shadow(color: .black.opacity(0.1), radius: 10, y: 5)
        )
    }
}

// MARK: - Liquid Glass Loading View

struct LiquidGlassLoadingView: View {
    @State private var isAnimating = false
    let message: String
    
    init(message: String = "Loading...") {
        self.message = message
    }
    
    var body: some View {
        VStack(spacing: 20) {
            // Animated loading indicator
            ZStack {
                ForEach(0..<3) { index in
                    Circle()
                        .fill(.blue.opacity(0.6))
                        .frame(width: 12, height: 12)
                        .scaleEffect(isAnimating ? 1.0 : 0.5)
                        .opacity(isAnimating ? 0.8 : 0.3)
                        .animation(
                            Animation.easeInOut(duration: 0.6)
                                .repeatForever()
                                .delay(Double(index) * 0.2),
                            value: isAnimating
                        )
                        .offset(x: CGFloat(index - 1) * 20)
                }
            }
            
            // Loading text
            Text(message)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.secondary)
        }
        .padding(24)
        .liquidGlass(intensity: 0.8, cornerRadius: 16)
        .onAppear {
            isAnimating = true
        }
    }
}

// MARK: - Liquid Glass Alert

struct LiquidGlassAlert: View {
    let title: String
    let message: String
    let primaryButton: (title: String, action: () -> Void)
    let secondaryButton: (title: String, action: () -> Void)?
    
    init(
        title: String,
        message: String,
        primaryButton: (title: String, action: () -> Void),
        secondaryButton: (title: String, action: () -> Void)? = nil
    ) {
        self.title = title
        self.message = message
        self.primaryButton = primaryButton
        self.secondaryButton = secondaryButton
    }
    
    var body: some View {
        VStack(spacing: 20) {
            // Content
            VStack(spacing: 12) {
                Text(title)
                    .font(.system(size: 18, weight: .bold))
                    .foregroundColor(.primary)
                    .multilineTextAlignment(.center)
                
                Text(message)
                    .font(.system(size: 16))
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            // Buttons
            VStack(spacing: 12) {
                Button(primaryButton.title, action: primaryButton.action)
                    .buttonStyle(LiquidGlassButtonStyle(variant: .primary, size: .medium))
                
                if let secondary = secondaryButton {
                    Button(secondary.title, action: secondary.action)
                        .buttonStyle(LiquidGlassButtonStyle(variant: .secondary, size: .medium))
                }
            }
        }
        .padding(24)
        .liquidGlassCard(elevation: 16, cornerRadius: 20, glowColor: .blue)
        .frame(maxWidth: 300)
    }
}
