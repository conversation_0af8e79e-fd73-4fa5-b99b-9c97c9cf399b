//
//  TaskHeaderView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.05.01.
//

import SwiftUI

struct TaskHeaderView: View {
    let categoryName: String
    let icon: String
    let color: Color
    let assistant: Assistant?
    var onExamplesButtonTapped: () -> Void

    var body: some View {
        VStack(spacing: 16) {
            // Assistant image or category icon
            Group {
                if let assistant = assistant, let uiImage = UIImage(named: assistant.name) {
                    // Show assistant image
                    Image(uiImage: uiImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: 80, height: 80)
                        .clipShape(Circle())
                        .shadow(color: .black.opacity(0.3), radius: 8, x: 0, y: 4)
                } else {
                    // Category icon - handle both SF Symbols and emojis
                    if icon.count == 1 || icon.first?.isEmoji == true {
                        // Display emoji directly
                        Text(icon)
                            .font(.system(size: 50))
                            .padding()
                            .background(color.opacity(0.2))
                            .clipShape(Circle())
                    } else {
                        // Display SF Symbol
                        Image(systemName: icon)
                            .font(.system(size: 40))
                            .foregroundColor(color)
                            .padding()
                            .background(color.opacity(0.2))
                            .clipShape(Circle())
                    }
                }
            }

            // Category name
            Text(categoryName)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.white)
                .multilineTextAlignment(.center)

            // Description text
            Text(assistant?.description ?? getCategoryDescription(for: categoryName))
                .font(.subheadline)
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
                .padding(.horizontal)

            // Examples button
            Button(action: onExamplesButtonTapped) {
                HStack {
                    Image(systemName: "lightbulb.fill")
                        .font(.system(size: 14))
                    Text("See Examples")
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                .padding(.vertical, 8)
                .padding(.horizontal, 16)
                .background(Color.systemGray6)
                .cornerRadius(16)
                .foregroundColor(.white)
            }
            .padding(.top, 8)
        }
        .padding()
        .frame(maxWidth: .infinity)
        .background(Color.black)
    }

    // Helper function to get category description
    private func getCategoryDescription(for categoryName: String) -> String {
        if let category = SuggestionCategory.categories.first(where: { $0.name == categoryName }) {
            return category.description
        }
        return "Specialized AI assistant for \(categoryName)"
    }
}

#Preview {
    TaskHeaderView(
        categoryName: "Business & Marketing",
        icon: "briefcase.fill",
        color: .blue,
        assistant: nil,
        onExamplesButtonTapped: {}
    )
    .preferredColorScheme(.dark)
}
