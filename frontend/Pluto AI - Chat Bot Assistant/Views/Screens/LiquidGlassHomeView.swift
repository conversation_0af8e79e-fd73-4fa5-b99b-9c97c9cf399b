//
//  LiquidGlassHomeView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.01.18.
//  Liquid Glass Enhanced Home View
//

import SwiftUI
import CoreData

struct LiquidGlassHomeView: View {
    @State private var messageText = ""
    @State private var messageToSend = ""
    @State private var selectedCategory = "E-Mail"
    @State private var showNewChat = false
    @State private var showAllAssistants = false
    @State private var showAllHistory = false
    @State private var showSettings = false
    @State private var chatHistories: [ChatHistory] = []
    @State private var selectedSuggestion: Suggestion? = nil
    @State private var isVisible = false

    @Environment(\.managedObjectContext) private var viewContext
    @StateObject private var assistantManager = AssistantManager.shared
    
    func handleSuggestionTapped(_ suggestion: Suggestion) {
        print("LiquidGlassHomeView: Suggestion tapped: \(suggestion.text) from category \(suggestion.category)")
        messageToSend = suggestion.text
        showNewChat = true
        selectedSuggestion = suggestion
    }
    
    var body: some View {
        ZStack {
            // Dynamic background with morphing colors
            MorphingBackground(
                colors: [.blue.opacity(0.3), .purple.opacity(0.2), .pink.opacity(0.1)],
                duration: 12
            )
            .ignoresSafeArea()
            
            // Particle system overlay
            ParticleSystem(particleCount: 15, colors: [.blue, .purple, .cyan])
                .opacity(0.3)
                .ignoresSafeArea()
            
            // Main content
            ScrollView(.vertical, showsIndicators: false) {
                LazyVStack(alignment: .leading, spacing: 24) {
                    // Top spacing
                    Spacer()
                        .frame(height: 20)
                    
                    // Elite Tools Section
                    VStack(alignment: .leading, spacing: 16) {
                        LiquidGlassSectionHeader(title: "Elite Tools", showSeeAll: false)
                            .liquidTransition(isVisible: isVisible, direction: .up)
                        
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 0) {
                                ForEach(Array(Tool.displayedEliteTools.enumerated()), id: \.element.id) { index, tool in
                                    LiquidGlassToolCard(tool: tool)
                                        .liquidTransition(
                                            isVisible: isVisible,
                                            direction: .left
                                        )
                                        .animation(
                                            LiquidGlassAnimations.spring.delay(Double(index) * 0.1),
                                            value: isVisible
                                        )
                                }
                            }
                            .padding(.horizontal, 20)
                        }
                    }
                    
                    // Assistants Section
                    VStack(alignment: .leading, spacing: 16) {
                        LiquidGlassSectionHeader(title: "Assistants", showSeeAll: true) {
                            showAllAssistants = true
                        }
                        .liquidTransition(isVisible: isVisible, direction: .up)
                        
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 16) {
                                LiquidGlassCreateAssistantCard()
                                    .liquidTransition(isVisible: isVisible, direction: .scale)
                                
                                ForEach(Array(assistantManager.allAssistants.prefix(3).enumerated()), id: \.element.id) { index, assistant in
                                    LiquidGlassAssistantCard(assistant: assistant)
                                        .liquidTransition(
                                            isVisible: isVisible,
                                            direction: .right
                                        )
                                        .animation(
                                            LiquidGlassAnimations.spring.delay(Double(index + 1) * 0.1),
                                            value: isVisible
                                        )
                                }
                            }
                            .padding(.horizontal, 20)
                        }
                    }
                    
                    // History Section
                    VStack(alignment: .leading, spacing: 16) {
                        LiquidGlassSectionHeader(title: "My History", showSeeAll: chatHistories.count > 3) {
                            showAllHistory = true
                        }
                        .liquidTransition(isVisible: isVisible, direction: .up)
                        
                        if chatHistories.isEmpty {
                            Text("No chat history yet. Start a conversation!")
                                .foregroundColor(.secondary)
                                .font(.system(size: 16))
                                .padding(.horizontal, 20)
                                .liquidTransition(isVisible: isVisible, direction: .fade)
                        } else {
                            ScrollView(.horizontal, showsIndicators: false) {
                                HStack(spacing: 16) {
                                    ForEach(Array(chatHistories.prefix(3).enumerated()), id: \.element.id) { index, history in
                                        LiquidGlassHistoryCard(history: history)
                                            .liquidTransition(
                                                isVisible: isVisible,
                                                direction: .up
                                            )
                                            .animation(
                                                LiquidGlassAnimations.spring.delay(Double(index) * 0.1),
                                                value: isVisible
                                            )
                                    }
                                }
                                .padding(.horizontal, 20)
                            }
                        }
                    }
                    
                    // Suggestions Section
                    VStack(alignment: .leading, spacing: 16) {
                        LiquidGlassSectionHeader(title: "Suggestions", showSeeAll: false)
                            .liquidTransition(isVisible: isVisible, direction: .up)
                        
                        // Category pills
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 12) {
                                ForEach(Array(SuggestionCategory.categories.enumerated()), id: \.element.id) { index, category in
                                    LiquidGlassCategoryPill(
                                        category: category,
                                        isSelected: selectedCategory == category.name
                                    ) {
                                        withAnimation(LiquidGlassAnimations.gentle) {
                                            selectedCategory = category.name
                                        }
                                    }
                                    .liquidTransition(
                                        isVisible: isVisible,
                                        direction: .scale
                                    )
                                    .animation(
                                        LiquidGlassAnimations.spring.delay(Double(index) * 0.05),
                                        value: isVisible
                                    )
                                }
                            }
                            .padding(.horizontal, 20)
                        }
                        
                        // Suggestions list
                        VStack(spacing: 12) {
                            let suggestions = getSuggestionsForCategory(selectedCategory)
                            ForEach(Array(suggestions.enumerated()), id: \.element.id) { index, suggestion in
                                LiquidGlassSuggestionCard(suggestion: suggestion) {
                                    handleSuggestionTapped(suggestion)
                                }
                                .liquidTransition(
                                    isVisible: isVisible,
                                    direction: .left
                                )
                                .animation(
                                    LiquidGlassAnimations.gentle.delay(Double(index) * 0.05),
                                    value: isVisible
                                )
                            }
                        }
                        .padding(.horizontal, 20)
                    }
                    
                    // Bottom spacing for input
                    Spacer()
                        .frame(height: 100)
                }
            }
            .background(.clear)
            
            // Liquid Glass Navigation Bar
            VStack {
                LiquidGlassNavigationBar(
                    title: "Pluto AI",
                    trailingIcon: "gearshape.fill",
                    trailingAction: {
                        showSettings = true
                    }
                )
                .liquidTransition(isVisible: isVisible, direction: .up)
                
                Spacer()
            }
        }
        .safeAreaInset(edge: .bottom, spacing: 0) {
            LiquidGlassChatInput(messageText: $messageText) {
                if !messageText.isEmpty {
                    let trimmedMessage = messageText.trimmingCharacters(in: .whitespacesAndNewlines)
                    if !trimmedMessage.isEmpty {
                        messageToSend = trimmedMessage
                        messageText = ""
                        showNewChat = true
                    }
                }
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 8)
        }
        .sheet(isPresented: $showNewChat, onDismiss: {
            messageToSend = ""
            selectedSuggestion = nil
            // Reload chat histories to show the new chat
            chatHistories = ChatHistory.getAllChats(context: viewContext)
        }) {
            NavigationView {
                if let suggestion = selectedSuggestion {
                    ChatScreenView(suggestion: suggestion, autoSend: false, showExamplesOnAppear: false, assistant: nil)
                } else {
                    ChatScreenView(initialMessage: messageToSend, autoSend: true, assistant: nil)
                }
            }
        }
        .sheet(isPresented: $showSettings) {
            SettingsView()
        }
        .sheet(isPresented: $showAllAssistants) {
            NavigationView {
                AssistantsView()
            }
        }
        .sheet(isPresented: $showAllHistory) {
            HistoryView()
        }
        .onAppear {
            chatHistories = ChatHistory.getAllChats(context: viewContext)
            
            // Animate in with staggered timing
            withAnimation(LiquidGlassAnimations.spring.delay(0.2)) {
                isVisible = true
            }
        }
        .preferredColorScheme(.dark)
    }
    
    private func getSuggestionsForCategory(_ category: String) -> [Suggestion] {
        switch category {
        case "E-Mail": return Suggestion.emailSuggestions
        case "Business & Marketing": return Suggestion.businessSuggestions
        case "Astrology": return Suggestion.astrologySuggestions
        case "Education": return Suggestion.educationSuggestions
        case "Art": return Suggestion.artSuggestions
        case "Travel": return Suggestion.travelSuggestions
        case "Daily Lifestyle": return Suggestion.lifestyleSuggestions
        case "Relationship": return Suggestion.relationshipSuggestions
        case "Fun": return Suggestion.funSuggestions
        case "Social": return Suggestion.socialSuggestions
        case "Career": return Suggestion.careerSuggestions
        case "Health & Nutrition": return Suggestion.healthSuggestions
        case "Greetings": return Suggestion.greetingsSuggestions
        default: return []
        }
    }
}

#Preview {
    LiquidGlassHomeView()
        .preferredColorScheme(.dark)
}
