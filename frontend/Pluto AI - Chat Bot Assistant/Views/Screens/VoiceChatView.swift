//
//  VoiceChatView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.05.01.
//

import SwiftUI
import AVFoundation

struct VoiceChatView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var showChatScreen = false
    @State private var showVoiceSelection = false
    @State private var showVoiceChatActive = false
    @State private var selectedVoice: String? = nil

    var body: some View {
        ZStack {
            // Background color
            Color.black.edgesIgnoringSafeArea(.all)

            // Content
            VStack(spacing: 25) {
                // Header with close button
                HStack {
                    Button(action: {
                        dismiss()
                    }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 20))
                            .foregroundColor(.white)
                            .padding(.leading, 5)
                    }

                    Spacer()
                }
                .padding(.top, 10)

                // Title with microphone icon
                HStack(spacing: 10) {
                    Image(systemName: "mic.fill")
                        .font(.system(size: 28))
                        .foregroundColor(.white)
                        .padding(8)
                        .background(Color.gray.opacity(0.3))
                        .clipShape(Circle())

                    Text("Voice Chat")
                        .font(.system(size: 28, weight: .bold))
                        .foregroundColor(.white)
                }
                .padding(.top, 10)

                // Features list
                VStack(spacing: 25) {
                    // Speak to Chat feature
                    FeatureItem(
                        icon: "waveform",
                        title: "Speak to Chat",
                        description: "Start voice chats with Pluto AI, no typing needed.",
                        iconColor: Color(red: 0, green: 0.8, blue: 0.6) // Teal color
                    )

                    // Effortless Voice Chat feature
                    FeatureItem(
                        icon: "mic.fill",
                        title: "Effortless Voice Chat",
                        description: "Chat hands-free, perfect when you're busy.",
                        iconColor: Color(red: 0, green: 0.8, blue: 0.6) // Teal color
                    )

                    // Conversation Archive feature
                    FeatureItem(
                        icon: "archivebox.fill",
                        title: "Conversation Archive",
                        description: "Review your conversation transcripts in chat history. No audio kept.",
                        iconColor: Color(red: 0, green: 0.8, blue: 0.6) // Teal color
                    )

                    // Smart Language Detection feature
                    FeatureItem(
                        icon: "character.bubble.fill",
                        title: "Smart Language Detection",
                        description: "Just speak, and Pluto AI adapts to your language.",
                        iconColor: Color(red: 0, green: 0.8, blue: 0.6) // Teal color
                    )
                }
                .padding(.horizontal, 20)
                .padding(.top, 20)

                Spacer()

                // Choose a Voice button
                Button(action: {
                    // Show voice selection
                    showVoiceSelection = true
                }) {
                    Text("Choose a Voice")
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color(red: 0, green: 0.8, blue: 0.6)) // Teal color
                        .cornerRadius(12)
                        .padding(.horizontal, 20)
                }
                .padding(.bottom, 30)
            }
        }
        .fullScreenCover(isPresented: $showVoiceSelection) {
            VoiceSelectionView(selectedVoice: $selectedVoice)
        }
        .fullScreenCover(isPresented: $showChatScreen) {
            NavigationView {
                // Create a suggestion for voice chat
                let voiceChatSuggestion = Suggestion(
                    text: "I'd like to start a voice chat",
                    icon: "mic.fill",
                    category: "Voice Chat",
                    examplePrompts: [
                        "Let's have a natural conversation",
                        "I'd like to practice speaking with AI",
                        "Start a voice-based discussion"
                    ]
                )

                // Pass the suggestion to ChatScreenView
                ChatScreenView(suggestion: voiceChatSuggestion, autoSend: false, showExamplesOnAppear: false, assistant: nil)
            }
        }
        .fullScreenCover(isPresented: $showVoiceChatActive) {
            VoiceChatActiveView()
        }
        .onChange(of: selectedVoice) { voice in
            if voice != nil {
                // If a voice is selected, start the voice chat active screen
                showVoiceChatActive = true
            }
        }
    }
}

// Feature item component
struct FeatureItem: View {
    let icon: String
    let title: String
    let description: String
    let iconColor: Color

    var body: some View {
        HStack(alignment: .top, spacing: 15) {
            // Icon
            Image(systemName: icon)
                .font(.system(size: 22))
                .foregroundColor(iconColor)
                .frame(width: 30)

            VStack(alignment: .leading, spacing: 5) {
                // Title
                Text(title)
                    .font(.headline)
                    .foregroundColor(.white)

                // Description
                Text(description)
                    .font(.subheadline)
                    .foregroundColor(.gray)
                    .lineLimit(2)
                    .fixedSize(horizontal: false, vertical: true)
            }
        }
    }
}

#Preview {
    VoiceChatView()
        .preferredColorScheme(.dark)
}
