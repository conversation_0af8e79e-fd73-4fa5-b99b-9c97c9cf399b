//
//  PrivacyPolicyView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.01.18.
//

import SwiftUI

struct PrivacyPolicyView: View {
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // Header
                    VStack(alignment: .center, spacing: 10) {
                        Image(systemName: "hand.raised.fill")
                            .font(.system(size: 60))
                            .foregroundColor(.blue)
                        
                        Text("Privacy Policy")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .multilineTextAlignment(.center)
                        
                        Text("Your privacy is important to us")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding(.bottom, 20)
                    
                    // Privacy Policy Content
                    VStack(alignment: .leading, spacing: 20) {
                        PrivacySection(
                            title: "Information We Collect",
                            content: """
                            Pluto AI uses device-based authentication and does not require personal accounts. We collect:
                            
                            • Device identifiers for authentication
                            • Chat messages and conversations for AI processing
                            • User preferences and memory data (stored locally)
                            • Usage analytics to improve our service
                            
                            All data is processed securely and used solely to provide and improve our AI services.
                            """
                        )
                        
                        PrivacySection(
                            title: "How We Use Your Information",
                            content: """
                            Your information is used to:
                            
                            • Provide personalized AI responses
                            • Remember your preferences and context
                            • Improve our AI models and services
                            • Ensure secure device-based authentication
                            • Analyze usage patterns for service improvements
                            
                            We do not sell, rent, or share your personal information with third parties for marketing purposes.
                            """
                        )
                        
                        PrivacySection(
                            title: "Data Storage and Security",
                            content: """
                            • All conversations are processed securely
                            • Memory data is stored with encryption
                            • Device-based authentication ensures privacy
                            • No cross-device data sharing
                            • Regular security audits and updates
                            
                            Your data is protected using industry-standard security measures.
                            """
                        )
                        
                        PrivacySection(
                            title: "Third-Party Services",
                            content: """
                            Pluto AI integrates with AI service providers:
                            
                            • OpenAI (GPT models)
                            • Anthropic (Claude models)
                            • Google (Gemini models)
                            • DeepSeek (DeepSeek models)
                            
                            These services process your messages to generate responses. Please review their privacy policies for more information.
                            """
                        )
                        
                        PrivacySection(
                            title: "Your Rights",
                            content: """
                            You have the right to:
                            
                            • Access your stored data
                            • Delete your conversations and memory data
                            • Opt out of analytics collection
                            • Reset your device authentication
                            
                            Contact us if you have questions about your privacy rights.
                            """
                        )
                        
                        PrivacySection(
                            title: "Contact Us",
                            content: """
                            If you have questions about this Privacy Policy, please contact us at:
                            
                            Email: <EMAIL>
                            Website: oyu-intelligence.com
                            
                            Last updated: January 18, 2025
                            """
                        )
                    }
                }
                .padding()
            }
            .navigationTitle("Privacy Policy")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(trailing: Button("Done") {
                presentationMode.wrappedValue.dismiss()
            })
        }
    }
}

struct PrivacySection: View {
    let title: String
    let content: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text(title)
                .font(.title3)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            Text(content)
                .font(.body)
                .foregroundColor(.secondary)
                .fixedSize(horizontal: false, vertical: true)
        }
        .padding(.bottom, 10)
    }
}

#Preview {
    PrivacyPolicyView()
}
