//
//  UploadAndAskPopupView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.05.01.
//

import SwiftUI
import UniformTypeIdentifiers

struct UploadAndAskPopupView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var fileService = FileUploadService.shared
    @State private var showDocumentPicker = false
    @State private var showChatScreen = false
    @State private var showResults = false
    @State private var documentURL: URL?
    @State private var documentName: String = "Add your document"
    @State private var processingResult: FileProcessingResult?
    @State private var selectedModel: AIModel = AIModel.models.first!

    var body: some View {
        ZStack {
            // Background color - semi-transparent black
            Color.black.opacity(0.9).edgesIgnoringSafeArea(.all)

            // Content
            VStack(spacing: 20) {
                // Handle indicator
                RoundedRectangle(cornerRadius: 2.5)
                    .fill(Color.gray.opacity(0.5))
                    .frame(width: 40, height: 5)
                    .padding(.top, 10)

                // Close button
                HStack {
                    Button(action: {
                        dismiss()
                    }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 18))
                            .foregroundColor(.white)
                    }
                    .padding(.leading, 10)

                    Spacer()

                    // Info button
                    Button(action: {
                        // Show info about Upload & Ask
                    }) {
                        Image(systemName: "info.circle")
                            .font(.system(size: 18))
                            .foregroundColor(.gray)
                    }
                    .padding(.trailing, 10)
                }
                .padding(.horizontal, 10)

                // Icon and title
                HStack {
                    // Document icon
                    Image(systemName: "doc.fill")
                        .font(.system(size: 24))
                        .foregroundColor(.white)
                        .padding(4)

                    Text("Upload & Ask")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                }
                .padding(.top, 5)

                // Description
                Text("You can ask about or search anything in a document or let Pluto AI to summarize it.")
                    .font(.subheadline)
                    .foregroundColor(.gray)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
                    .padding(.top, -5)

                // Document upload button
                Button(action: {
                    showDocumentPicker = true
                }) {
                    HStack {
                        Image(systemName: "doc")
                            .font(.system(size: 22))
                            .foregroundColor(.gray)
                            .padding(.leading, 16)

                        Text(documentName)
                            .foregroundColor(documentURL == nil ? .gray : .white)
                            .padding(.vertical, 16)
                            .padding(.leading, 8)

                        Spacer()
                    }
                    .background(Color(UIColor.systemGray6).opacity(0.3))
                    .cornerRadius(12)
                    .padding(.horizontal, 20)
                    .padding(.top, 20)
                }

                Spacer()

                // Continue button
                Button(action: {
                    processFile()
                }) {
                    HStack {
                        if fileService.isProcessing {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .black))
                                .scaleEffect(0.8)
                        }

                        Text(fileService.isProcessing ? "Processing..." : "Analyze Document")
                            .font(.headline)
                            .foregroundColor(.black)
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.white)
                    .cornerRadius(12)
                    .padding(.horizontal, 20)
                }
                .padding(.bottom, 30)
                .disabled(documentURL == nil || fileService.isProcessing)
                .opacity(documentURL == nil || fileService.isProcessing ? 0.5 : 1.0)
            }
            .padding(.horizontal)
        }
        .sheet(isPresented: $showDocumentPicker) {
            DocumentPicker(documentURL: $documentURL, documentName: $documentName)
        }
        .fullScreenCover(isPresented: $showResults) {
            if let result = processingResult {
                FileResultsView(result: result, isPresented: $showResults)
            }
        }
        .fullScreenCover(isPresented: $showChatScreen) {
            NavigationView {
                // Create a suggestion for document analysis
                let documentSuggestion = Suggestion(
                    text: "Analyze this document: \(documentName)",
                    icon: "doc.fill",
                    category: "Upload & Ask",
                    examplePrompts: [
                        "Summarize the key points in this document",
                        "What are the main themes discussed?",
                        "Extract important information from this file"
                    ]
                )

                // Pass the suggestion to ChatScreenView with autoSend set to true
                ChatScreenView(suggestion: documentSuggestion, autoSend: true, documentURL: documentURL, assistant: nil)
            }
        }
    }

    // MARK: - Private Methods

    private func processFile() {
        guard let url = documentURL else { return }

        Task {
            do {
                let result = try await fileService.processFile(at: url, model: selectedModel)
                DispatchQueue.main.async {
                    self.processingResult = result
                    self.showResults = true
                }
            } catch {
                // Handle error - could show an alert or error message
                print("File processing error: \(error)")
            }
        }
    }
}

// Document Picker
struct DocumentPicker: UIViewControllerRepresentable {
    @Binding var documentURL: URL?
    @Binding var documentName: String

    func makeUIViewController(context: Context) -> UIDocumentPickerViewController {
        let supportedTypes = FileUploadService.shared.getSupportedFileTypes()
        let picker = UIDocumentPickerViewController(forOpeningContentTypes: supportedTypes)
        picker.allowsMultipleSelection = false
        picker.delegate = context.coordinator
        return picker
    }

    func updateUIViewController(_ uiViewController: UIDocumentPickerViewController, context: Context) {}

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, UIDocumentPickerDelegate {
        let parent: DocumentPicker

        init(_ parent: DocumentPicker) {
            self.parent = parent
        }

        func documentPicker(_ controller: UIDocumentPickerViewController, didPickDocumentsAt urls: [URL]) {
            guard let url = urls.first else { return }

            // Get secure access to the document
            let secureURL = url.startAccessingSecurityScopedResource() ? url : nil
            parent.documentURL = secureURL

            // Update document name
            parent.documentName = url.lastPathComponent
        }
    }
}

struct FileResultsView: View {
    let result: FileProcessingResult
    @Binding var isPresented: Bool
    @State private var showChatScreen = false

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // File info
                    VStack(alignment: .leading, spacing: 12) {
                        Text("File Information")
                            .font(.title2)
                            .fontWeight(.bold)

                        HStack {
                            Image(systemName: "doc.fill")
                                .font(.title)
                                .foregroundColor(.blue)

                            VStack(alignment: .leading, spacing: 4) {
                                Text(result.file.fileName)
                                    .font(.headline)
                                    .fontWeight(.semibold)

                                Text(result.file.fileType.displayName)
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)

                                Text("Size: \(ByteCountFormatter.string(fromByteCount: result.file.fileSize, countStyle: .file))")
                                    .font(.caption)
                                    .foregroundColor(.secondary)

                                Text("Words: \(result.wordCount)")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }

                            Spacer()
                        }
                        .padding()
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(12)
                    }

                    // Summary
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Summary")
                            .font(.title2)
                            .fontWeight(.bold)

                        Text(result.summary)
                            .font(.body)
                    }

                    // Key Points
                    if !result.keyPoints.isEmpty {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Key Points")
                                .font(.title2)
                                .fontWeight(.bold)

                            ForEach(Array(result.keyPoints.enumerated()), id: \.offset) { index, point in
                                HStack(alignment: .top, spacing: 8) {
                                    Text("\(index + 1).")
                                        .font(.body)
                                        .fontWeight(.medium)
                                        .foregroundColor(.blue)

                                    Text(point)
                                        .font(.body)
                                }
                            }
                        }
                    }

                    // Topics
                    if !result.topics.isEmpty {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Topics")
                                .font(.title2)
                                .fontWeight(.bold)

                            LazyVGrid(columns: [
                                GridItem(.adaptive(minimum: 100))
                            ], spacing: 8) {
                                ForEach(result.topics, id: \.self) { topic in
                                    Text(topic)
                                        .font(.caption)
                                        .padding(.horizontal, 12)
                                        .padding(.vertical, 6)
                                        .background(Color.blue.opacity(0.1))
                                        .foregroundColor(.blue)
                                        .cornerRadius(16)
                                }
                            }
                        }
                    }

                    // Chat button
                    Button(action: {
                        showChatScreen = true
                    }) {
                        Text("Ask Questions About This File")
                            .font(.headline)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.blue)
                            .cornerRadius(12)
                    }
                    .padding(.top, 20)
                }
                .padding()
            }
            .navigationTitle("File Analysis")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        isPresented = false
                    }
                }
            }
        }
        .fullScreenCover(isPresented: $showChatScreen) {
            NavigationView {
                let suggestion = Suggestion(
                    text: "I've analyzed this file: \(result.file.fileName). Ask me anything about it!",
                    icon: "doc.fill",
                    category: "File Q&A",
                    examplePrompts: [
                        "What were the main points discussed?",
                        "Can you explain the key concepts?",
                        "What are the practical takeaways?"
                    ]
                )

                ChatScreenView(suggestion: suggestion, autoSend: true, showExamplesOnAppear: false, assistant: nil)
            }
        }
    }
}

#Preview {
    UploadAndAskPopupView()
        .preferredColorScheme(.dark)
}
