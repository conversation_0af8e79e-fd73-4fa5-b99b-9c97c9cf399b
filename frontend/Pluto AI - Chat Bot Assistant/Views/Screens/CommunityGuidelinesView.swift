//
//  CommunityGuidelinesView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.01.18.
//

import SwiftUI

struct CommunityGuidelinesView: View {
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // Header
                    VStack(alignment: .center, spacing: 10) {
                        Image(systemName: "person.3.sequence.fill")
                            .font(.system(size: 60))
                            .foregroundColor(.blue)
                        
                        Text("Community Guidelines")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .multilineTextAlignment(.center)
                        
                        Text("Building a positive AI community together")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding(.bottom, 20)
                    
                    // Guidelines Content
                    VStack(alignment: .leading, spacing: 20) {
                        GuidelineSection(
                            title: "Our Mission",
                            content: """
                            Pluto AI is committed to providing a safe, helpful, and positive AI experience for everyone. These guidelines help ensure our community remains welcoming and productive.
                            """
                        )
                        
                        GuidelineSection(
                            title: "Respectful Interaction",
                            content: """
                            • Treat AI assistants and the service with respect
                            • Use appropriate language in your conversations
                            • Avoid generating offensive or harmful content
                            • Be mindful that AI responses are for informational purposes
                            • Report any issues or concerns to our support team
                            """
                        )
                        
                        GuidelineSection(
                            title: "Prohibited Content",
                            content: """
                            Do not use Pluto AI to generate:
                            
                            • Harmful, violent, or threatening content
                            • Illegal activities or instructions
                            • Hate speech or discriminatory content
                            • Personal information of others
                            • Spam or repetitive content
                            • Content that violates intellectual property rights
                            """
                        )
                        
                        GuidelineSection(
                            title: "Responsible AI Use",
                            content: """
                            • Verify important information from reliable sources
                            • Don't rely solely on AI for medical, legal, or financial advice
                            • Understand that AI responses may contain errors
                            • Use AI-generated content responsibly
                            • Respect the capabilities and limitations of AI
                            """
                        )
                        
                        GuidelineSection(
                            title: "Privacy and Security",
                            content: """
                            • Don't share sensitive personal information
                            • Protect your device and account security
                            • Be aware that conversations are processed by AI services
                            • Use the memory features responsibly
                            • Report security concerns immediately
                            """
                        )
                        
                        GuidelineSection(
                            title: "Feedback and Improvement",
                            content: """
                            Help us improve Pluto AI by:
                            
                            • Providing constructive feedback
                            • Reporting bugs or issues
                            • Suggesting new features
                            • Sharing positive experiences
                            • Participating in community discussions
                            """
                        )
                        
                        GuidelineSection(
                            title: "Enforcement",
                            content: """
                            Violations of these guidelines may result in:
                            
                            • Warning notifications
                            • Temporary service restrictions
                            • Permanent account suspension
                            • Legal action for serious violations
                            
                            We reserve the right to take appropriate action to maintain a safe community.
                            """
                        )
                        
                        GuidelineSection(
                            title: "Contact Us",
                            content: """
                            Questions about these guidelines?
                            
                            Email: <EMAIL>
                            Website: oyu-intelligence.com
                            
                            Last updated: January 18, 2025
                            """
                        )
                    }
                }
                .padding()
            }
            .navigationTitle("Community Guidelines")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(trailing: Button("Done") {
                presentationMode.wrappedValue.dismiss()
            })
        }
    }
}

struct GuidelineSection: View {
    let title: String
    let content: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text(title)
                .font(.title3)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            Text(content)
                .font(.body)
                .foregroundColor(.secondary)
                .fixedSize(horizontal: false, vertical: true)
        }
        .padding(.bottom, 10)
    }
}

#Preview {
    CommunityGuidelinesView()
}
