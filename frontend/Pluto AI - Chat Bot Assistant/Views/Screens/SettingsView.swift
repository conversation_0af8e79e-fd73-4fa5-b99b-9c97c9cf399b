//
//  SettingsView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.04.30.
//

import SwiftUI

struct SettingsView: View {
    @Environment(\.presentationMode) var presentationMode
    @State private var hapticFeedbackEnabled = true
    @State private var showProSubscription = false
    @State private var showDeviceTest = false
    @State private var showAlert = false
    @State private var alertMessage = ""
    @State private var isShowingWhatsNew = false
    @State private var isShowingPrivacyPolicy = false
    @State private var isShowingTermsOfService = false
    @State private var isShowingCommunityGuidelines = false
    @State private var isShowingFAQSupport = false

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Premium Banner
                    PremiumBannerView(showProSubscription: $showProSubscription)

                    // App Settings Section
                    SettingsSectionView(title: "") {
                        SettingsRowView(icon: "bell.fill", title: "What's New", showDivider: true, action: {
                            showWhatsNew()
                        })

                        SettingsRowView(icon: "square.and.arrow.up", title: "Share Pluto AI", showDivider: true, action: {
                            shareApp()
                        })

                        SettingsRowView(icon: "star.fill", title: "Like us, Rate us?", showDivider: false, action: {
                            openURL("https://oyu-intelligence.com")
                        })
                    }

                    // Language and Voice Section
                    SettingsSectionView(title: "") {
                        SettingsRowView(icon: "globe", title: "Language", showDivider: true, action: {
                            showFeatureComingSoon("Language Selection")
                        })

                        SettingsRowView(icon: "waveform", title: "Voice Selection", showDivider: true, action: {
                            showFeatureComingSoon("Voice Selection")
                        })

                        SettingsToggleView(icon: "iphone.radiowaves.left.and.right", title: "Haptic Feedback", isOn: $hapticFeedbackEnabled)

                        SettingsRowView(icon: "square.grid.2x2", title: "Widgets", showDivider: false, action: {
                            showFeatureComingSoon("Widgets")
                        })
                    }

                    // More Apps Section
                    SettingsSectionView(title: "") {
                        SettingsRowView(icon: "square.grid.2x2", title: "More Apps From Us", showDivider: true, action: {
                            openURL("https://oyu-intelligence.com")
                        })

                        SettingsRowView(icon: "book.closed", title: "About OYU Intelligence", showDivider: false, action: {
                            openURL("https://oyu-intelligence.com")
                        })
                    }

                    // Social Section
                    SettingsSectionView(title: "") {
                        SettingsRowView(icon: "gamecontroller", title: "Join Discord Channel", showDivider: true, action: {
                            openURL("https://oyu-intelligence.com")
                        })

                        SettingsRowView(icon: "x.circle", title: "Follow on X", showDivider: true, action: {
                            openURL("https://oyu-intelligence.com")
                        })

                        SettingsRowView(icon: "r.circle", title: "Follow on Reddit", showDivider: false, action: {
                            openURL("https://oyu-intelligence.com")
                        })
                    }

                    // Support Section
                    SettingsSectionView(title: "") {
                        SettingsRowView(icon: "questionmark.circle", title: "FAQ & Support", showDivider: false, action: {
                            showFAQSupport()
                        })
                    }

                    // Legal Section
                    SettingsSectionView(title: "") {
                        SettingsRowView(icon: "hand.raised", title: "Privacy Policy", showDivider: true, action: {
                            showPrivacyPolicy()
                        })

                        SettingsRowView(icon: "doc.text", title: "Terms of Service", showDivider: true, action: {
                            showTermsOfService()
                        })

                        SettingsRowView(icon: "list.bullet", title: "Community Guidelines", showDivider: false, action: {
                            showCommunityGuidelines()
                        })
                    }

                    // Debug Section (for testing)
                    SettingsSectionView(title: "Debug") {
                        SettingsRowView(icon: "wrench.and.screwdriver", title: "Device & StoreKit Test", showDivider: false, action: {
                            showDeviceTest = true
                        })
                    }

                    Spacer(minLength: 50)
                }
                .padding(.horizontal)
            }
            .background(Color.black)
            .navigationTitle("Settings")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: {
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        Image(systemName: "chevron.left")
                            .foregroundColor(.white)
                    }
                }
            }
        }
        .sheet(isPresented: $showProSubscription) {
            ProSubscriptionView()
        }
        .sheet(isPresented: $showDeviceTest) {
            DeviceTestView()
        }
        .sheet(isPresented: $isShowingWhatsNew) {
            WhatsNewView()
        }
        .sheet(isPresented: $isShowingPrivacyPolicy) {
            PrivacyPolicyView()
        }
        .sheet(isPresented: $isShowingTermsOfService) {
            TermsOfServiceView()
        }
        .sheet(isPresented: $isShowingCommunityGuidelines) {
            CommunityGuidelinesView()
        }
        .sheet(isPresented: $isShowingFAQSupport) {
            FAQSupportView()
        }
        .alert("Info", isPresented: $showAlert) {
            Button("OK") { }
        } message: {
            Text(alertMessage)
        }
    }

    // Helper functions
    private func openURL(_ urlString: String) {
        guard let url = URL(string: urlString) else { return }
        UIApplication.shared.open(url)
    }

    private func shareApp() {
        let activityVC = UIActivityViewController(
            activityItems: ["Check out Pluto AI - Chat Bot Assistant! https://oyu-intelligence.com"],
            applicationActivities: nil
        )

        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first {
            window.rootViewController?.present(activityVC, animated: true)
        }
    }

    private func showFeatureComingSoon(_ feature: String) {
        alertMessage = "\(feature) feature is coming soon! Stay tuned for updates."
        showAlert = true
    }

    private func showWhatsNew() {
        isShowingWhatsNew = true
    }

    private func showPrivacyPolicy() {
        isShowingPrivacyPolicy = true
    }

    private func showTermsOfService() {
        isShowingTermsOfService = true
    }

    private func showCommunityGuidelines() {
        isShowingCommunityGuidelines = true
    }

    private func showFAQSupport() {
        isShowingFAQSupport = true
    }


}

// Premium Banner View
struct PremiumBannerView: View {
    @Binding var showProSubscription: Bool

    var body: some View {
        Button(action: {
            showProSubscription = true
        }) {
            ZStack(alignment: .trailing) {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Upgrade to Premium")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)

                    HStack(alignment: .top, spacing: 4) {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.white)

                        Text("Access to GPT-4.1 & Claude & Gemini")
                            .font(.subheadline)
                            .foregroundColor(.white)
                    }

                    HStack(alignment: .top, spacing: 4) {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.white)

                        Text("Infinite Image Generations")
                            .font(.subheadline)
                            .foregroundColor(.white)

                        Text("and more")
                            .font(.subheadline)
                            .foregroundColor(.gray)
                    }
                }
                .padding()
                .frame(maxWidth: .infinity, alignment: .leading)

                // Premium icon/graphic
                Image(systemName: "sparkles")
                    .font(.system(size: 60))
                    .foregroundColor(.white.opacity(0.3))
                    .padding(.trailing, 20)
            }
        }
        .background(
            LinearGradient(
                gradient: Gradient(colors: [Color.green.opacity(0.7), Color.green.opacity(0.3)]),
                startPoint: .leading,
                endPoint: .trailing
            )
        )
        .cornerRadius(16)
    }
}



// Settings Section View
struct SettingsSectionView<Content: View>: View {
    let title: String
    let content: Content

    init(title: String, @ViewBuilder content: () -> Content) {
        self.title = title
        self.content = content()
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            if !title.isEmpty {
                Text(title)
                    .font(.headline)
                    .foregroundColor(.gray)
                    .padding(.horizontal)
                    .padding(.bottom, 8)
            }

            VStack(spacing: 0) {
                content
            }
            .background(Color.systemGray6)
            .cornerRadius(16)
        }
    }
}

// Settings Row View
struct SettingsRowView: View {
    let icon: String
    let title: String
    let showDivider: Bool
    let action: (() -> Void)?

    init(icon: String, title: String, showDivider: Bool, action: (() -> Void)? = nil) {
        self.icon = icon
        self.title = title
        self.showDivider = showDivider
        self.action = action
    }

    var body: some View {
        VStack(spacing: 0) {
            Button(action: {
                action?()
            }) {
                HStack {
                    Image(systemName: icon)
                        .font(.system(size: 20))
                        .foregroundColor(.white)
                        .frame(width: 24, height: 24)

                    Text(title)
                        .foregroundColor(.white)

                    Spacer()

                    Image(systemName: "chevron.right")
                        .font(.system(size: 14))
                        .foregroundColor(.gray)
                }
                .padding(.vertical, 16)
                .padding(.horizontal)
            }

            if showDivider {
                Divider()
                    .background(Color.gray.opacity(0.3))
                    .padding(.leading, 56)
            }
        }
    }
}

// Settings Toggle View
struct SettingsToggleView: View {
    let icon: String
    let title: String
    @Binding var isOn: Bool

    var body: some View {
        VStack(spacing: 0) {
            HStack {
                Image(systemName: icon)
                    .font(.system(size: 20))
                    .foregroundColor(.white)
                    .frame(width: 24, height: 24)

                Text(title)
                    .foregroundColor(.white)

                Spacer()

                Toggle("", isOn: $isOn)
                    .labelsHidden()
                    .toggleStyle(SwitchToggleStyle(tint: Color.green))
            }
            .padding(.vertical, 16)
            .padding(.horizontal)

            Divider()
                .background(Color.gray.opacity(0.3))
                .padding(.leading, 56)
        }
    }
}

#Preview {
    SettingsView()
        .preferredColorScheme(.dark)
}
