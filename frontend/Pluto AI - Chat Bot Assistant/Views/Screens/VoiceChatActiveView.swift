//
//  VoiceChatActiveView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.05.01.
//

import SwiftUI
import AVFoundation

struct VoiceChatActiveView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var voiceService = VoiceProcessingService.shared
    @StateObject private var ttsService = TextToSpeechService.shared
    @State private var pulseAmount: CGFloat = 1.0
    @State private var outerPulseAmount: CGFloat = 1.0
    @State private var timer: Timer?
    @State private var showTranscript = false

    var body: some View {
        ZStack {
            // Background color
            Color.black.edgesIgnoringSafeArea(.all)

            // Main content
            VStack(spacing: 30) {
                // Status text
                VStack(spacing: 8) {
                    Text(statusText)
                        .font(.title2)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                        .multilineTextAlignment(.center)

                    if !voiceService.currentTranscript.isEmpty {
                        Text(voiceService.currentTranscript)
                            .font(.body)
                            .foregroundColor(.gray)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, 20)
                    }

                    if let error = voiceService.errorMessage {
                        Text(error)
                            .font(.caption)
                            .foregroundColor(.red)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, 20)
                    }
                }
                .padding(.top, 60)

                Spacer()

                // Central visualization
                ZStack {
                    // Outer pulse ring
                    Circle()
                        .stroke(stateColor.opacity(0.3), lineWidth: 2)
                        .frame(width: 280, height: 280)
                        .scaleEffect(outerPulseAmount)
                        .opacity(shouldPulse ? 1.0 : 0.3)

                    // Middle pulse ring
                    Circle()
                        .stroke(stateColor.opacity(0.5), lineWidth: 3)
                        .frame(width: 220, height: 220)
                        .scaleEffect(pulseAmount)
                        .opacity(shouldPulse ? 1.0 : 0.5)

                    // Inner circle
                    Circle()
                        .fill(stateColor)
                        .frame(width: 160, height: 160)
                        .overlay(
                            Image(systemName: stateIcon)
                                .font(.system(size: 60, weight: .medium))
                                .foregroundColor(.white)
                        )
                        .shadow(color: stateColor.opacity(0.3), radius: 20)
                }
                .onAppear {
                    startVoiceChat()
                    startPulseAnimation()
                }
                .onDisappear {
                    stopVoiceChat()
                    timer?.invalidate()
                }

                Spacer()

                // Bottom controls
                HStack(spacing: 40) {
                    // Toggle listening button
                    Button(action: {
                        voiceService.toggleListening()
                    }) {
                        Image(systemName: voiceService.state == .listening ? "mic.slash.fill" : "mic.fill")
                            .font(.system(size: 24))
                            .foregroundColor(.black)
                            .padding(16)
                            .background(Color.white)
                            .clipShape(Circle())
                    }

                    // Transcript toggle button
                    Button(action: {
                        showTranscript.toggle()
                    }) {
                        Image(systemName: "text.bubble.fill")
                            .font(.system(size: 20))
                            .foregroundColor(.white)
                            .padding(16)
                            .background(Color.gray.opacity(0.3))
                            .clipShape(Circle())
                    }

                    // Close button
                    Button(action: {
                        dismiss()
                    }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 16))
                            .foregroundColor(.white)
                            .padding(16)
                            .background(Color.red)
                            .clipShape(Circle())
                    }
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 40)
            }

            // Transcript overlay
            if showTranscript {
                VoiceChatTranscriptView(
                    messages: voiceService.messages,
                    isPresented: $showTranscript
                )
            }
        }
    }

    // MARK: - Computed Properties

    private var statusText: String {
        switch voiceService.state {
        case .idle:
            return "Tap the microphone to speak"
        case .listening:
            return "Listening..."
        case .processing:
            return "Processing your message..."
        case .speaking:
            return "Speaking..."
        case .error(let message):
            return "Error: \(message)"
        }
    }

    private var stateColor: Color {
        switch voiceService.state {
        case .idle:
            return Color.gray
        case .listening:
            return Color.green
        case .processing:
            return Color.blue
        case .speaking:
            return Color.orange
        case .error:
            return Color.red
        }
    }

    private var stateIcon: String {
        switch voiceService.state {
        case .idle:
            return "mic"
        case .listening:
            return "waveform"
        case .processing:
            return "brain.head.profile"
        case .speaking:
            return "speaker.wave.3"
        case .error:
            return "exclamationmark.triangle"
        }
    }

    private var shouldPulse: Bool {
        switch voiceService.state {
        case .listening, .processing, .speaking:
            return true
        case .idle, .error:
            return false
        }
    }

    // MARK: - Private Methods

    private func startVoiceChat() {
        voiceService.startVoiceChat()
    }

    private func stopVoiceChat() {
        voiceService.stopVoiceChat()
    }

    private func startPulseAnimation() {
        withAnimation(Animation.easeInOut(duration: 2).repeatForever(autoreverses: true)) {
            pulseAmount = 1.1
        }

        withAnimation(Animation.easeInOut(duration: 3).repeatForever(autoreverses: true)) {
            outerPulseAmount = 1.2
        }
    }
}

struct VoiceChatTranscriptView: View {
    let messages: [VoiceChatMessage]
    @Binding var isPresented: Bool

    var body: some View {
        ZStack {
            Color.black.opacity(0.8)
                .edgesIgnoringSafeArea(.all)
                .onTapGesture {
                    isPresented = false
                }

            VStack {
                // Header
                HStack {
                    Text("Conversation")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)

                    Spacer()

                    Button(action: {
                        isPresented = false
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.title2)
                            .foregroundColor(.gray)
                    }
                }
                .padding()

                // Messages
                ScrollView {
                    LazyVStack(spacing: 12) {
                        ForEach(messages) { message in
                            VoiceChatMessageBubble(message: message)
                        }
                    }
                    .padding(.horizontal)
                }
                .background(Color.black.opacity(0.9))
                .cornerRadius(20)
                .padding()
            }
        }
    }
}

struct VoiceChatMessageBubble: View {
    let message: VoiceChatMessage

    var body: some View {
        HStack {
            if message.isUser {
                Spacer()
            }

            VStack(alignment: message.isUser ? .trailing : .leading, spacing: 4) {
                Text(message.content)
                    .font(.body)
                    .foregroundColor(.white)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)
                    .background(
                        message.isUser ?
                        Color.blue.opacity(0.8) :
                        Color.gray.opacity(0.3)
                    )
                    .cornerRadius(18)

                Text(formatTime(message.timestamp))
                    .font(.caption2)
                    .foregroundColor(.gray)
                    .padding(.horizontal, 4)
            }

            if !message.isUser {
                Spacer()
            }
        }
    }

    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
}

#Preview {
    VoiceChatActiveView()
        .preferredColorScheme(.dark)
}
