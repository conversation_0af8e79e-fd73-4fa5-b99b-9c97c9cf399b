//
//  LiquidGlassChatView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.01.18.
//  Liquid Glass Enhanced Chat View
//

import SwiftUI

struct LiquidGlassChatView: View {
    @State private var messages: [ChatMessage] = []
    @State private var inputText = ""
    @State private var isTyping = false
    @State private var showModelSelection = false
    @State private var selectedModel = "GPT-4.1 nano"
    @State private var isVisible = false
    
    let assistant: Assistant?
    let initialMessage: String?
    
    init(assistant: Assistant? = nil, initialMessage: String? = nil) {
        self.assistant = assistant
        self.initialMessage = initialMessage
    }
    
    var body: some View {
        ZStack {
            // Dynamic background
            MorphingBackground(
                colors: [.blue.opacity(0.1), .purple.opacity(0.05), .cyan.opacity(0.08)],
                duration: 15
            )
            .ignoresSafeArea()
            
            VStack(spacing: 0) {
                // Navigation bar
                LiquidGlassNavigationBar(
                    title: assistant?.name ?? "Pluto AI",
                    leadingIcon: "chevron.left",
                    leadingAction: {
                        // Handle back action
                    },
                    trailingIcon: "ellipsis.circle",
                    trailingAction: {
                        showModelSelection = true
                    }
                )
                .liquidTransition(isVisible: isVisible, direction: .up)
                
                // Messages area
                ScrollViewReader { proxy in
                    ScrollView {
                        LazyVStack(spacing: 16) {
                            // Welcome message
                            if messages.isEmpty {
                                LiquidGlassWelcomeMessage(assistant: assistant)
                                    .liquidTransition(isVisible: isVisible, direction: .scale)
                                    .padding(.top, 20)
                            }
                            
                            // Messages
                            ForEach(Array(messages.enumerated()), id: \.element.id) { index, message in
                                LiquidGlassMessageBubble(message: message)
                                    .liquidTransition(
                                        isVisible: isVisible,
                                        direction: message.isUser ? .right : .left
                                    )
                                    .animation(
                                        LiquidGlassAnimations.spring.delay(Double(index) * 0.1),
                                        value: isVisible
                                    )
                                    .id(message.id)
                            }
                            
                            // Typing indicator
                            if isTyping {
                                LiquidGlassTypingIndicator()
                                    .liquidTransition(isVisible: isTyping, direction: .left)
                            }
                            
                            // Bottom spacing
                            Spacer()
                                .frame(height: 100)
                        }
                        .padding(.horizontal, 16)
                    }
                    .onChange(of: messages.count) { _ in
                        if let lastMessage = messages.last {
                            withAnimation(LiquidGlassAnimations.gentle) {
                                proxy.scrollTo(lastMessage.id, anchor: .bottom)
                            }
                        }
                    }
                }
                
                // Input area
                LiquidGlassChatInput(messageText: $inputText) {
                    sendMessage()
                }
                .padding(.horizontal, 16)
                .padding(.bottom, 8)
                .liquidTransition(isVisible: isVisible, direction: .down)
            }
        }
        .sheet(isPresented: $showModelSelection) {
            LiquidGlassModelSelection(selectedModel: $selectedModel)
        }
        .onAppear {
            // Send initial message if provided
            if let initial = initialMessage, !initial.isEmpty {
                inputText = initial
                sendMessage()
            }
            
            // Animate in
            withAnimation(LiquidGlassAnimations.spring.delay(0.3)) {
                isVisible = true
            }
        }
        .preferredColorScheme(.dark)
    }
    
    private func sendMessage() {
        guard !inputText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else { return }
        
        let userMessage = ChatMessage(
            id: UUID().uuidString,
            content: inputText,
            isUser: true,
            timestamp: Date()
        )
        
        withAnimation(LiquidGlassAnimations.gentle) {
            messages.append(userMessage)
        }
        
        let messageToSend = inputText
        inputText = ""
        
        // Show typing indicator
        withAnimation(LiquidGlassAnimations.gentle) {
            isTyping = true
        }
        
        // Simulate AI response (replace with actual API call)
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            let aiResponse = ChatMessage(
                id: UUID().uuidString,
                content: generateMockResponse(for: messageToSend),
                isUser: false,
                timestamp: Date()
            )
            
            withAnimation(LiquidGlassAnimations.gentle) {
                isTyping = false
                messages.append(aiResponse)
            }
        }
    }
    
    private func generateMockResponse(for message: String) -> String {
        let responses = [
            "That's an interesting question! Let me help you with that.",
            "I understand what you're looking for. Here's my perspective on this topic.",
            "Great question! Based on my knowledge, I can provide some insights.",
            "I'd be happy to assist you with that. Let me break this down for you.",
            "That's a thoughtful inquiry. Here's what I think about this."
        ]
        return responses.randomElement() ?? "Thank you for your message!"
    }
}

// MARK: - Supporting Views

struct LiquidGlassWelcomeMessage: View {
    let assistant: Assistant?
    
    var body: some View {
        VStack(spacing: 16) {
            // Assistant avatar or icon
            if let assistant = assistant {
                if let uiImage = UIImage(named: assistant.name) {
                    Image(uiImage: uiImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: 80, height: 80)
                        .clipShape(Circle())
                        .overlay(
                            Circle()
                                .stroke(.white.opacity(0.2), lineWidth: 2)
                        )
                        .shadow(color: assistant.color.opacity(0.3), radius: 10)
                } else {
                    Image(systemName: assistant.iconName)
                        .font(.system(size: 40, weight: .medium))
                        .foregroundColor(assistant.color)
                        .frame(width: 80, height: 80)
                        .background(
                            Circle()
                                .fill(.ultraThinMaterial)
                                .overlay(
                                    Circle()
                                        .stroke(assistant.color.opacity(0.3), lineWidth: 2)
                                )
                        )
                        .shadow(color: assistant.color.opacity(0.3), radius: 10)
                }
            } else {
                Image(systemName: "brain.head.profile")
                    .font(.system(size: 40, weight: .medium))
                    .foregroundColor(.blue)
                    .frame(width: 80, height: 80)
                    .background(
                        Circle()
                            .fill(.ultraThinMaterial)
                            .overlay(
                                Circle()
                                    .stroke(.blue.opacity(0.3), lineWidth: 2)
                            )
                    )
                    .shadow(color: .blue.opacity(0.3), radius: 10)
            }
            
            // Welcome text
            VStack(spacing: 8) {
                Text("Hello! I'm \(assistant?.name ?? "Pluto AI")")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(.primary)
                
                Text(assistant?.description ?? "How can I help you today?")
                    .font(.system(size: 16))
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(3)
            }
        }
        .padding(24)
        .liquidGlass(intensity: 0.6, cornerRadius: 20)
        .frame(maxWidth: 300)
    }
}

struct LiquidGlassMessageBubble: View {
    let message: ChatMessage
    
    var body: some View {
        HStack {
            if message.isUser {
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text(message.content)
                        .font(.system(size: 16))
                        .foregroundColor(.white)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 12)
                        .background(
                            ZStack {
                                RoundedRectangle(cornerRadius: 18)
                                    .fill(.blue.opacity(0.8))
                                
                                RoundedRectangle(cornerRadius: 18)
                                    .stroke(.blue, lineWidth: 1)
                                    .blur(radius: 2)
                            }
                        )
                    
                    Text(message.timestamp, style: .time)
                        .font(.system(size: 12))
                        .foregroundColor(.gray)
                        .padding(.trailing, 8)
                }
                .frame(maxWidth: UIScreen.main.bounds.width * 0.7, alignment: .trailing)
            } else {
                VStack(alignment: .leading, spacing: 4) {
                    Text(message.content)
                        .font(.system(size: 16))
                        .foregroundColor(.primary)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 18)
                                .fill(.ultraThinMaterial)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 18)
                                        .stroke(.white.opacity(0.2), lineWidth: 1)
                                )
                        )
                    
                    Text(message.timestamp, style: .time)
                        .font(.system(size: 12))
                        .foregroundColor(.gray)
                        .padding(.leading, 8)
                }
                .frame(maxWidth: UIScreen.main.bounds.width * 0.7, alignment: .leading)
                
                Spacer()
            }
        }
    }
}

struct LiquidGlassTypingIndicator: View {
    @State private var animationPhase: CGFloat = 0
    
    var body: some View {
        HStack {
            HStack(spacing: 4) {
                ForEach(0..<3) { index in
                    Circle()
                        .fill(.secondary)
                        .frame(width: 8, height: 8)
                        .scaleEffect(
                            1 + sin(animationPhase + Double(index) * .pi / 2) * 0.5
                        )
                        .animation(
                            Animation.easeInOut(duration: 1)
                                .repeatForever()
                                .delay(Double(index) * 0.2),
                            value: animationPhase
                        )
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 18)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 18)
                            .stroke(.white.opacity(0.2), lineWidth: 1)
                    )
            )
            
            Spacer()
        }
        .onAppear {
            animationPhase = .pi * 2
        }
    }
}

struct LiquidGlassModelSelection: View {
    @Binding var selectedModel: String
    @Environment(\.dismiss) private var dismiss
    
    let models = ["GPT-4.1 nano", "GPT-4o", "Claude Sonnet 4", "DeepSeek", "Gemini 2.0 Flash"]
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                ForEach(models, id: \.self) { model in
                    Button(action: {
                        selectedModel = model
                        dismiss()
                    }) {
                        HStack {
                            Text(model)
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(.primary)
                            
                            Spacer()
                            
                            if selectedModel == model {
                                Image(systemName: "checkmark.circle.fill")
                                    .foregroundColor(.blue)
                            }
                        }
                        .padding(16)
                        .liquidGlass(intensity: 0.6, cornerRadius: 12)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
                
                Spacer()
            }
            .padding(20)
            .navigationTitle("Select Model")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - Chat Message Model

struct ChatMessage: Identifiable {
    let id: String
    let content: String
    let isUser: Bool
    let timestamp: Date
}

#Preview {
    LiquidGlassChatView()
        .preferredColorScheme(.dark)
}
