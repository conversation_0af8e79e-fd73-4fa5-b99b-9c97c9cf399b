//
//  FAQSupportView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.01.18.
//

import SwiftUI

struct FAQSupportView: View {
    @Environment(\.presentationMode) var presentationMode
    @State private var searchText = ""
    
    let faqs = [
        FAQ(
            question: "How does device-based authentication work?",
            answer: "Pluto AI uses your device ID for authentication, so no account signup is required. Each device gets independent access and subscriptions are device-specific."
        ),
        FAQ(
            question: "What's the difference between weekly and lifetime subscriptions?",
            answer: "Weekly subscription costs $7.99/week and renews automatically. Lifetime subscription is a one-time payment of $39.99 for permanent access on your device."
        ),
        FAQ(
            question: "Can I transfer my subscription to another device?",
            answer: "No, subscriptions are device-specific and cannot be transferred. This ensures privacy and security with our device-based authentication system."
        ),
        FAQ(
            question: "How does the AI memory system work?",
            answer: "Pluto AI remembers your preferences, goals, and important details from conversations to provide personalized responses. All memory data is stored securely and privately."
        ),
        FAQ(
            question: "Which AI models are available?",
            answer: "Pluto AI offers access to GPT-4.1 nano (free), GPT-4o, Claude Sonnet 4, DeepSeek R1, Gemini 2.0 Flash, and more cutting-edge AI models."
        ),
        FAQ(
            question: "How do I generate images with AI?",
            answer: "Use the AI Image Generator tool to create images with DALL-E 3 or GPT-Image-1. You can generate from text prompts or edit existing images."
        ),
        FAQ(
            question: "Is my data secure and private?",
            answer: "Yes, we use device-based authentication, encrypt stored data, and don't share personal information. Each device operates independently for maximum privacy."
        ),
        FAQ(
            question: "How do I reset my device authentication?",
            answer: "Go to Settings > Device Test to view your device information. Contact support if you need to reset your device authentication."
        ),
        FAQ(
            question: "Why can't I access premium features?",
            answer: "Premium features require a subscription. Upgrade to weekly ($7.99/week) or lifetime ($39.99) to access all AI models and advanced features."
        ),
        FAQ(
            question: "How do I contact support?",
            answer: "Email <NAME_EMAIL> or visit oyu-intelligence.com. We typically respond within 24 hours."
        )
    ]
    
    var filteredFAQs: [FAQ] {
        if searchText.isEmpty {
            return faqs
        } else {
            return faqs.filter { faq in
                faq.question.localizedCaseInsensitiveContains(searchText) ||
                faq.answer.localizedCaseInsensitiveContains(searchText)
            }
        }
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                VStack(alignment: .center, spacing: 10) {
                    Image(systemName: "questionmark.circle.fill")
                        .font(.system(size: 60))
                        .foregroundColor(.blue)
                    
                    Text("FAQ & Support")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .multilineTextAlignment(.center)
                    
                    Text("Find answers to common questions")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .padding()
                
                // Search Bar
                SearchBar(text: $searchText)
                    .padding(.horizontal)
                
                // FAQ List
                ScrollView {
                    LazyVStack(spacing: 12) {
                        ForEach(filteredFAQs, id: \.question) { faq in
                            FAQRow(faq: faq)
                        }
                        
                        // Contact Support Section
                        if searchText.isEmpty {
                            ContactSupportSection()
                                .padding(.top, 20)
                        }
                    }
                    .padding()
                }
            }
            .navigationTitle("FAQ & Support")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(trailing: Button("Done") {
                presentationMode.wrappedValue.dismiss()
            })
        }
    }
}

struct FAQ {
    let question: String
    let answer: String
}

struct FAQRow: View {
    let faq: FAQ
    @State private var isExpanded = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            Button(action: {
                withAnimation(.easeInOut(duration: 0.3)) {
                    isExpanded.toggle()
                }
            }) {
                HStack {
                    Text(faq.question)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.leading)
                    
                    Spacer()
                    
                    Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(.secondary)
                }
                .padding()
            }
            .buttonStyle(PlainButtonStyle())
            
            if isExpanded {
                Text(faq.answer)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .padding(.horizontal)
                    .padding(.bottom)
                    .fixedSize(horizontal: false, vertical: true)
            }
        }
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

struct SearchBar: View {
    @Binding var text: String
    
    var body: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
            
            TextField("Search FAQ...", text: $text)
                .textFieldStyle(PlainTextFieldStyle())
            
            if !text.isEmpty {
                Button(action: {
                    text = ""
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
        .cornerRadius(10)
    }
}

struct ContactSupportSection: View {
    var body: some View {
        VStack(spacing: 15) {
            Text("Still need help?")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("Contact our support team for personalized assistance")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            VStack(spacing: 10) {
                Button(action: {
                    if let url = URL(string: "mailto:<EMAIL>") {
                        UIApplication.shared.open(url)
                    }
                }) {
                    HStack {
                        Image(systemName: "envelope.fill")
                        Text("Email Support")
                    }
                    .font(.headline)
                    .foregroundColor(.white)
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(Color.blue)
                    .cornerRadius(12)
                }
                
                Button(action: {
                    if let url = URL(string: "https://oyu-intelligence.com") {
                        UIApplication.shared.open(url)
                    }
                }) {
                    HStack {
                        Image(systemName: "globe")
                        Text("Visit Website")
                    }
                    .font(.headline)
                    .foregroundColor(.blue)
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(12)
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(16)
    }
}

#Preview {
    FAQSupportView()
}
