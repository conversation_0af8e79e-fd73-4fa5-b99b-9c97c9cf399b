//
//  SuggestionsView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.05.19.
//

import SwiftUI

struct SuggestionsView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var selectedCategory = "E-Mail"
    @GestureState private var dragOffset = CGSize.zero

    var body: some View {
        ZStack(alignment: .top) {
            // Header with title and close button
            HStack {
                Button(action: {
                    dismiss()
                }) {
                    Image(systemName: "xmark")
                        .font(.system(size: 20))
                        .foregroundColor(.white)
                        .padding()
                }

                Spacer()

                Text("Suggestions")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)

                Spacer()

                But<PERSON>(action: {
                    // Show info
                }) {
                    Image(systemName: "info.circle")
                        .font(.system(size: 20))
                        .foregroundColor(.white)
                        .padding()
                }
            }
            .padding(.top, 8)
            .background(Color.black)
            .zIndex(1)

            // Main content
            ScrollView {
                VStack(spacing: 16) {
                    // Add padding to account for the header
                    Spacer()
                        .frame(height: 60)

                    // Category pills
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 10) {
                            ForEach(SuggestionCategory.categories) { category in
                                CategoryPillView(category: category, selectedCategory: $selectedCategory)
                            }
                        }
                        .padding(.horizontal)
                    }

                    // Suggestions list based on selected category
                    VStack(spacing: 10) {
                        if selectedCategory == "E-Mail" {
                            SuggestionsListView(suggestions: Suggestion.emailSuggestions, onSuggestionTapped: { _ in })
                        } else if selectedCategory == "Business & Marketing" {
                            SuggestionsListView(suggestions: Suggestion.businessSuggestions, onSuggestionTapped: { _ in })
                        } else if selectedCategory == "Astrology" {
                            SuggestionsListView(suggestions: Suggestion.astrologySuggestions, onSuggestionTapped: { _ in })
                        } else if selectedCategory == "Education" {
                            SuggestionsListView(suggestions: Suggestion.educationSuggestions, onSuggestionTapped: { _ in })
                        } else if selectedCategory == "Art" {
                            SuggestionsListView(suggestions: Suggestion.artSuggestions, onSuggestionTapped: { _ in })
                        } else if selectedCategory == "Travel" {
                            SuggestionsListView(suggestions: Suggestion.travelSuggestions, onSuggestionTapped: { _ in })
                        } else if selectedCategory == "Daily Lifestyle" {
                            SuggestionsListView(suggestions: Suggestion.lifestyleSuggestions, onSuggestionTapped: { _ in })
                        } else if selectedCategory == "Relationship" {
                            SuggestionsListView(suggestions: Suggestion.relationshipSuggestions, onSuggestionTapped: { _ in })
                        } else if selectedCategory == "Fun" {
                            SuggestionsListView(suggestions: Suggestion.funSuggestions, onSuggestionTapped: { _ in })
                        } else if selectedCategory == "Social" {
                            SuggestionsListView(suggestions: Suggestion.socialSuggestions, onSuggestionTapped: { _ in })
                        } else if selectedCategory == "Career" {
                            SuggestionsListView(suggestions: Suggestion.careerSuggestions, onSuggestionTapped: { _ in })
                        } else if selectedCategory == "Health & Nutrition" {
                            SuggestionsListView(suggestions: Suggestion.healthSuggestions, onSuggestionTapped: { _ in })
                        } else if selectedCategory == "Greetings" {
                            SuggestionsListView(suggestions: Suggestion.greetingsSuggestions, onSuggestionTapped: { _ in })
                        } else {
                            Text("No suggestions available for this category")
                                .foregroundColor(.gray)
                                .padding()
                        }
                    }
                    .padding(.horizontal)

                    // Add some bottom padding
                    Spacer()
                        .frame(height: 20)
                }
                .padding(.bottom)
            }
        }
        .background(Color.black.edgesIgnoringSafeArea(.all))
        .navigationBarHidden(true)
        // Enable swipe to dismiss
        .gesture(
            DragGesture().updating($dragOffset, body: { (value, state, transaction) in
                // Only allow right-to-left swipe
                if value.startLocation.x < 20 && value.translation.width > 100 {
                    state = value.translation
                }
            }).onEnded({ (value) in
                // Dismiss if swipe is significant enough
                if value.startLocation.x < 20 && value.translation.width > 100 {
                    dismiss()
                }
            })
        )
    }
}

#Preview {
    SuggestionsView()
        .preferredColorScheme(.dark)
}
