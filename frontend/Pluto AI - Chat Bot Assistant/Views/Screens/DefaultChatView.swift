//
//  DefaultChatView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.05.01.
//

import SwiftUI

@MainActor
struct DefaultChatView: View, ChatStreamingDelegate {
    @State private var messageText = ""
    @State private var messages: [Message] = []
    @State private var isStreaming = false
    @State private var currentStreamingMessage: Message?
    @State private var refreshTrigger = 0 // Force UI refresh
    @State private var selectedModel = AIModel.models.first(where: { $0.name == "GPT-4.1 nano" }) ?? AIModel.models[0]
    @State private var showModelSelection = false
    @State private var showSuggestions = false
    @State private var showEliteTools = false
    @State private var showAIImageGenerator = false
    @State private var showAssistants = false
    @FocusState private var isInputFieldFocused: Bool
    @Environment(\.dismiss) private var dismiss
    @Environment(\.managedObjectContext) private var viewContext

    // Backend chat management
    @StateObject private var chatHistoryService = ChatHistoryService.shared
    @State private var currentChatId: String?
    @State private var isLoading = false

    var body: some View {
        ZStack {
            VStack(spacing: 0) {
                // Chat content
                VStack(spacing: 0) {
                    if messages.isEmpty {
                        // Welcome screen with introduction and service buttons
                        ScrollView {
                            VStack(spacing: 24) {
                                Spacer(minLength: 60)

                                // AI Avatar with Pluto AI branding
                                Circle()
                                    .fill(Color.green.gradient)
                                    .frame(width: 40, height: 40)
                                    .overlay(
                                        Image(systemName: "sparkles")
                                            .font(.system(size: 20))
                                            .foregroundColor(.white)
                                    )

                                // Introduction message
                                VStack(spacing: 12) {
                                    Text("Hi! You can send your message or get inspired from suggestions. 😊")
                                        .font(.body)
                                        .foregroundColor(.white)
                                        .multilineTextAlignment(.center)
                                        .padding(.horizontal, 20)
                                }

                                // Service buttons in 2x2 grid
                                VStack(spacing: 12) {
                                    HStack(spacing: 12) {
                                        ServiceButton(
                                            title: "Suggestions",
                                            icon: "lightbulb",
                                            action: { showSuggestions = true }
                                        )

                                        ServiceButton(
                                            title: "Elite Tools",
                                            icon: "gearshape.2",
                                            action: { showEliteTools = true }
                                        )
                                    }

                                    HStack(spacing: 12) {
                                        ServiceButton(
                                            title: "AI Image Generator",
                                            icon: "photo.artframe",
                                            action: { showAIImageGenerator = true }
                                        )

                                        ServiceButton(
                                            title: "Assistants",
                                            icon: "person.3",
                                            action: { showAssistants = true }
                                        )
                                    }
                                }
                                .padding(.horizontal, 20)

                                Spacer(minLength: 100)
                            }
                        }
                    } else {
                        // Chat messages
                        ScrollViewReader { proxy in
                            ScrollView {
                                LazyVStack(spacing: 8) {
                                    ForEach(messages) { message in
                                        ModernMessageBubble(message: message)
                                            .id(message.id)
                                    }

                                    // Show ChatGPT-style typing indicator when streaming
                                    if isStreaming {
                                        AIThinkingBubble()
                                            .id("typing-indicator")
                                    }
                                }
                                .id(refreshTrigger) // Force refresh when refreshTrigger changes
                                .padding(.vertical, 16)
                            }
                            .onChange(of: messages.count) { _ in
                                if let lastMessage = messages.last {
                                    withAnimation(.easeOut(duration: 0.2)) {
                                        proxy.scrollTo(lastMessage.id, anchor: .bottom)
                                    }
                                }
                            }
                            .onChange(of: currentStreamingMessage?.content) { _ in
                                if isStreaming, let lastMessage = messages.last {
                                    proxy.scrollTo(lastMessage.id, anchor: .bottom)
                                }
                            }
                        }
                    }
                }

                // Streaming indicator
                if isStreaming {
                    HStack {
                        ProgressView()
                            .scaleEffect(0.8)
                        Text("🔥 AI is responding...")
                            .font(.caption)
                            .foregroundColor(.green)
                    }
                    .padding()
                }

                // Input area
                ChatInputView(messageText: $messageText, onSend: {
                    sendMessage()
                }, disableNavigation: true)
                .focused($isInputFieldFocused)
            }

            // Model selection overlay
            if showModelSelection {
                Color.black.opacity(0.3)
                    .edgesIgnoringSafeArea(.all)
                    .onTapGesture {
                        showModelSelection = false
                    }

                VStack {
                    Spacer()
                    ModelSelectionView(isShowing: $showModelSelection, selectedModel: $selectedModel)
                    Spacer()
                }
            }
        }
        .background(Color.black)
        #if os(iOS)
        .navigationBarTitleDisplayMode(.inline)
        #endif
        .toolbar {
            ChatToolbarContent(
                showModelSelection: $showModelSelection,
                selectedModel: selectedModel,
                dismissAction: { dismiss() },
                categoryName: nil,
                categoryIcon: nil,
                assistant: nil
            )
        }
        .sheet(isPresented: $showSuggestions) {
            SuggestionsView()
        }
        .sheet(isPresented: $showEliteTools) {
            ToolsView()
        }
        .sheet(isPresented: $showAIImageGenerator) {
            AIImageGeneratorView()
        }
        .sheet(isPresented: $showAssistants) {
            AssistantsView()
        }
        .onAppear {
            // Set up streaming delegate
            print("DefaultChatView: Setting streaming delegate")
            ChatService.shared.streamingDelegate = self
            print("DefaultChatView: Delegate set to: \(ChatService.shared.streamingDelegate != nil ? "SET" : "NIL")")

            // Auto-focus the input field for immediate typing
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                isInputFieldFocused = true
            }
        }
    }

    // MARK: - OPTIMIZED ChatStreamingDelegate for Real-time Streaming
    nonisolated func didReceiveStreamingToken(_ token: String) {
        Task { @MainActor in
            print("🔥 RECEIVED TOKEN: '\(token)'")

            // Update the last AI message with streaming content
            if !messages.isEmpty && !messages.last!.isUser {
                let lastIndex = messages.count - 1
                let currentContent = messages[lastIndex].content
                let newContent = currentContent + token

                // Create new message with updated content
                let newMessage = Message(
                    content: newContent,
                    isUser: false,
                    timestamp: Date()
                )

                // Replace the last message
                messages[lastIndex] = newMessage
                currentStreamingMessage = newMessage

                print("🔥 UPDATED MESSAGE: '\(newContent)'")

                // Trigger UI update
                refreshTrigger += 1
            }
        }
    }

    nonisolated func didCompleteStreaming() {
        Task { @MainActor in
            isStreaming = false
            currentStreamingMessage = nil
        }
    }

    nonisolated func didFailStreaming(with error: Error) {
        Task { @MainActor in
            isStreaming = false
            if let currentMessage = currentStreamingMessage,
               let index = messages.firstIndex(where: { $0.id == currentMessage.id }) {
                messages[index].content = "Sorry, I encountered an error: \(error.localizedDescription)"
            }
            currentStreamingMessage = nil
        }
    }

    // MARK: - Helper Methods
    private func sendQuickMessage(_ text: String) {
        messageText = text
        sendMessage()
    }

    private func sendMessage() {
        guard !messageText.isEmpty && !isStreaming else {
            print("🔥 DefaultChatView BLOCKED: messageText='\(messageText)', isStreaming=\(isStreaming)")
            return
        }

        print("🔥 DefaultChatView SENDING MESSAGE: '\(messageText)'")

        // Add user message
        let userMessage = Message(content: messageText, isUser: true, timestamp: Date())
        messages.append(userMessage)
        print("🔥 DefaultChatView ADDED USER MESSAGE, total messages: \(messages.count)")

        let userMessageContent = messageText

        // Clear input
        messageText = ""

        // Set loading state
        isLoading = true
        isStreaming = true

        // Create empty AI message for streaming
        let aiMessage = Message(content: "", isUser: false, timestamp: Date())
        messages.append(aiMessage)
        currentStreamingMessage = aiMessage

        // Handle backend chat with proper saving
        handleBackendChat(userMessageContent: userMessageContent)
    }

    private func handleBackendChat(userMessageContent: String) {
        Task {
            do {
                // Create or get chat ID
                if currentChatId == nil {
                    print("🔥 DefaultChatView: Creating new chat")
                    currentChatId = try await chatHistoryService.createChat(
                        assistantId: selectedModel.name,
                        title: "New Chat"
                    )
                    print("🔥 DefaultChatView: Created chat with ID: \(currentChatId ?? "nil")")
                }

                guard let chatId = currentChatId else {
                    throw ChatService.ChatServiceError.invalidResponse
                }

                // Send message to backend and get AI response
                print("🔥 DefaultChatView: Sending message to backend")
                let aiResponse = try await chatHistoryService.sendMessage(
                    chatId: chatId,
                    message: userMessageContent
                )

                // Update the AI message with the response
                await MainActor.run {
                    if let lastIndex = messages.lastIndex(where: { !$0.isUser }) {
                        messages[lastIndex] = Message(
                            content: aiResponse,
                            isUser: false,
                            timestamp: Date()
                        )
                    }

                    isLoading = false
                    isStreaming = false
                    currentStreamingMessage = nil
                    refreshTrigger += 1
                }

                print("🔥 DefaultChatView: Backend response received and saved")

            } catch {
                print("🔥 DefaultChatView: Error in backend chat: \(error)")
                await MainActor.run {
                    // Update AI message with error
                    if let lastIndex = messages.lastIndex(where: { !$0.isUser }) {
                        messages[lastIndex] = Message(
                            content: "Sorry, I encountered an error: \(error.localizedDescription)",
                            isUser: false,
                            timestamp: Date()
                        )
                    }

                    isLoading = false
                    isStreaming = false
                    currentStreamingMessage = nil
                    refreshTrigger += 1
                }
            }
        }
    }
}

// Service button component for the 2x2 grid
struct ServiceButton: View {
    let title: String
    let icon: String
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.system(size: 16))
                    .foregroundColor(.white)
                    .frame(width: 20, height: 20)

                Text(title)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white)
                    .lineLimit(1)

                Spacer()
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 10)
            .frame(maxWidth: .infinity)
            .background(Color.systemGray6)
            .cornerRadius(20)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// Modern Message Bubble with enhanced design
struct ModernMessageBubble: View {
    let message: Message
    @State private var showActions = false
    @State private var isAnimating = false
    @State private var showImageResults = false
    @State private var selectedImageURL: URL?

    var body: some View {
        HStack(alignment: .bottom, spacing: 8) {
            if message.isUser {
                Spacer(minLength: 50)
            } else {
                // AI Avatar
                Circle()
                    .fill(LinearGradient(
                        gradient: Gradient(colors: [Color.blue, Color.purple]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ))
                    .frame(width: 32, height: 32)
                    .overlay(
                        Image(systemName: "brain.head.profile")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.white)
                    )
                    .shadow(color: .blue.opacity(0.3), radius: 4, x: 0, y: 2)
            }

            VStack(alignment: message.isUser ? .trailing : .leading, spacing: 6) {
                // Message Content
                VStack(alignment: .leading, spacing: 8) {
                    // Text content
                    if !message.content.isEmpty {
                        Text(formatMessageContent(message.content))
                            .font(.system(size: 16, weight: .regular, design: .default))
                            .lineSpacing(4)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 12)
                            .background(
                                RoundedRectangle(cornerRadius: 20, style: .continuous)
                                    .fill(message.isUser ?
                                        LinearGradient(
                                            gradient: Gradient(colors: [Color.blue, Color.blue.opacity(0.8)]),
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        ) :
                                        LinearGradient(
                                            gradient: Gradient(colors: [Color.gray.opacity(0.1), Color.gray.opacity(0.05)]),
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        )
                                    )
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 20, style: .continuous)
                                            .stroke(message.isUser ? Color.clear : Color.gray.opacity(0.1), lineWidth: 1)
                                    )
                            )
                            .foregroundColor(message.isUser ? .white : .primary)
                            .shadow(
                                color: message.isUser ? Color.blue.opacity(0.2) : Color.black.opacity(0.05),
                                radius: message.isUser ? 8 : 4,
                                x: 0,
                                y: message.isUser ? 4 : 2
                            )
                    }

                    // Image content
                    if let imageURL = message.imageURL {
                        AsyncImage(url: imageURL) { image in
                            image
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                                .frame(maxWidth: 250, maxHeight: 250)
                                .cornerRadius(12)
                                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
                        } placeholder: {
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.gray.opacity(0.2))
                                .frame(width: 250, height: 250)
                                .overlay(
                                    ProgressView()
                                        .scaleEffect(1.2)
                                )
                        }
                        .onTapGesture {
                            selectedImageURL = imageURL
                            showImageResults = true
                        }
                    }
                }

                // Action buttons for AI messages
                if !message.isUser && !message.content.isEmpty && showActions {
                    HStack(spacing: 12) {
                        ModernActionButton(icon: "doc.on.doc", action: {
                            copyToClipboard(message.content)
                        })

                        ModernActionButton(icon: "arrow.clockwise", action: {
                            regenerateResponse()
                        })

                        ModernActionButton(icon: "hand.thumbsup", action: {
                            // Like action
                        })

                        ModernActionButton(icon: "hand.thumbsdown", action: {
                            // Dislike action
                        })
                    }
                    .padding(.top, 8)
                    .padding(.leading, 16)
                    .transition(.opacity.combined(with: .scale(scale: 0.9)))
                }

                // Timestamp
                Text(message.timestamp, style: .time)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 4)
            }
            .onTapGesture {
                if !message.isUser {
                    withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                        showActions.toggle()
                    }
                }
            }

            if !message.isUser {
                Spacer(minLength: 50)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 6)
        .scaleEffect(isAnimating ? 1.0 : 0.95)
        .opacity(isAnimating ? 1.0 : 0.0)
        .onAppear {
            withAnimation(.spring(response: 0.4, dampingFraction: 0.8).delay(0.1)) {
                isAnimating = true
            }
        }
        .onChange(of: message.content) { _ in
            // Subtle animation on content change for streaming
            withAnimation(.easeOut(duration: 0.1)) {
                // Trigger subtle visual feedback
            }
        }
        .fullScreenCover(isPresented: $showImageResults) {
            if let imageURL = selectedImageURL {
                ImageResultsView(imageURL: imageURL, isPresented: $showImageResults)
            }
        }
    }

    private func formatMessageContent(_ content: String) -> AttributedString {
        // Try to parse markdown first
        if #available(iOS 15.0, *) {
            if let markdownString = try? AttributedString(
                markdown: content,
                options: AttributedString.MarkdownParsingOptions(interpretedSyntax: .inlineOnlyPreservingWhitespace)
            ) {
                var attributedString = markdownString

                // Apply base formatting
                let fullRange = attributedString.startIndex..<attributedString.endIndex
                attributedString[fullRange].font = .system(size: 16, weight: .regular, design: .default)

                return attributedString
            }
        }

        // Fallback: manual markdown parsing for older iOS versions or if markdown parsing fails
        return parseMarkdownManually(content)
    }

    private func parseMarkdownManually(_ content: String) -> AttributedString {
        var attributedString = AttributedString()
        let lines = content.components(separatedBy: .newlines)

        for (index, line) in lines.enumerated() {
            var lineAttributedString = AttributedString()

            // Handle headers
            if line.hasPrefix("### ") {
                let headerText = String(line.dropFirst(4))
                var headerAttr = AttributedString(headerText)
                headerAttr.font = .system(size: 18, weight: .bold, design: .default)
                lineAttributedString.append(headerAttr)
            } else if line.hasPrefix("## ") {
                let headerText = String(line.dropFirst(3))
                var headerAttr = AttributedString(headerText)
                headerAttr.font = .system(size: 20, weight: .bold, design: .default)
                lineAttributedString.append(headerAttr)
            } else if line.hasPrefix("# ") {
                let headerText = String(line.dropFirst(2))
                var headerAttr = AttributedString(headerText)
                headerAttr.font = .system(size: 22, weight: .bold, design: .default)
                lineAttributedString.append(headerAttr)
            } else if line.hasPrefix("- ") || line.hasPrefix("* ") {
                // Handle bullet points
                let bulletText = String(line.dropFirst(2))
                var bulletAttr = AttributedString("• " + bulletText)
                bulletAttr.font = .system(size: 16, weight: .regular, design: .default)
                lineAttributedString.append(bulletAttr)
            } else {
                // Regular text with bold formatting
                var processedLine = line
                var lineAttr = AttributedString()

                // Simple bold text parsing
                while let boldRange = processedLine.range(of: #"\*\*([^*]+)\*\*"#, options: .regularExpression) {
                    // Add text before bold
                    let beforeBold = String(processedLine[..<boldRange.lowerBound])
                    if !beforeBold.isEmpty {
                        var beforeAttr = AttributedString(beforeBold)
                        beforeAttr.font = .system(size: 16, weight: .regular, design: .default)
                        lineAttr.append(beforeAttr)
                    }

                    // Add bold text
                    let boldText = String(processedLine[boldRange])
                    let cleanBoldText = boldText.replacingOccurrences(of: "**", with: "")
                    var boldAttr = AttributedString(cleanBoldText)
                    boldAttr.font = .system(size: 16, weight: .bold, design: .default)
                    lineAttr.append(boldAttr)

                    // Remove processed part
                    processedLine = String(processedLine[boldRange.upperBound...])
                }

                // Add remaining text
                if !processedLine.isEmpty {
                    var remainingAttr = AttributedString(processedLine)
                    remainingAttr.font = .system(size: 16, weight: .regular, design: .default)
                    lineAttr.append(remainingAttr)
                }

                lineAttributedString = lineAttr
            }

            // Add the line to the main attributed string
            attributedString.append(lineAttributedString)

            // Add line break if not the last line
            if index < lines.count - 1 {
                attributedString.append(AttributedString("\n"))
            }
        }

        return attributedString
    }

    private func copyToClipboard(_ text: String) {
        UIPasteboard.general.string = text
        // Add haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
    }

    private func regenerateResponse() {
        // Implement regenerate functionality
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }
}

// Modern Action Button Component
struct ModernActionButton: View {
    let icon: String
    let action: () -> Void
    @State private var isPressed = false

    var body: some View {
        Button(action: {
            withAnimation(.spring(response: 0.2, dampingFraction: 0.8)) {
                isPressed = true
            }
            action()
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(.spring(response: 0.2, dampingFraction: 0.8)) {
                    isPressed = false
                }
            }
        }) {
            Image(systemName: icon)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.secondary)
                .frame(width: 32, height: 32)
                .background(
                    Circle()
                        .fill(Color.gray.opacity(0.1))
                        .overlay(
                            Circle()
                                .stroke(Color.gray.opacity(0.2), lineWidth: 1)
                        )
                )
                .scaleEffect(isPressed ? 0.9 : 1.0)
        }
        .buttonStyle(PlainButtonStyle())
    }
}



#Preview {
    DefaultChatView()
        .preferredColorScheme(.dark)
}
