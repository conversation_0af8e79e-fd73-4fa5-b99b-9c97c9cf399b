//
//  DeviceTestView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.01.14.
//

import SwiftUI
import StoreKit

struct DeviceTestView: View {
    @State private var deviceInfo: [String: String] = [:]
    @State private var revenueCatStatus = "Not checked"
    @State private var products: [Product] = []
    @State private var isLoading = false
    @State private var subscriptionStatus = "Unknown"
    @StateObject private var storeKitService = StoreKitService.shared
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    
                    // Device ID Section
                    VStack(alignment: .leading, spacing: 10) {
                        Text("📱 Device Information")
                            .font(.headline)
                            .foregroundColor(.primary)
                        
                        VStack(alignment: .leading, spacing: 8) {
                            ForEach(Array(deviceInfo.keys.sorted()), id: \.self) { key in
                                HStack {
                                    Text("\(key.capitalized):")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                        .frame(width: 100, alignment: .leading)
                                    
                                    Text(deviceInfo[key] ?? "")
                                        .font(.caption)
                                        .foregroundColor(.primary)
                                        .textSelection(.enabled)
                                }
                            }
                        }
                        .padding()
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(8)
                        
                        Button("Refresh Device Info") {
                            loadDeviceInfo()
                        }
                        .buttonStyle(.bordered)
                    }
                    
                    // StoreKit Section
                    VStack(alignment: .leading, spacing: 10) {
                        Text("💰 StoreKit Status")
                            .font(.headline)
                            .foregroundColor(.primary)

                        Text("Status: \(revenueCatStatus)")
                            .font(.body)
                            .foregroundColor(.secondary)

                        Text("Subscription: \(subscriptionStatus)")
                            .font(.body)
                            .foregroundColor(.secondary)

                        if !products.isEmpty {
                            VStack(alignment: .leading, spacing: 8) {
                                Text("Available Products:")
                                    .font(.subheadline)
                                    .fontWeight(.semibold)

                                ForEach(products, id: \.id) { product in
                                    HStack {
                                        Text(product.displayName)
                                            .font(.caption)
                                        Spacer()
                                        Text(product.displayPrice)
                                            .font(.caption)
                                            .fontWeight(.semibold)
                                    }
                                    .padding(.vertical, 4)
                                    .padding(.horizontal, 8)
                                    .background(Color.blue.opacity(0.1))
                                    .cornerRadius(4)
                                }
                            }
                            .padding()
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(8)
                        }
                        
                        HStack {
                            Button("Test StoreKit") {
                                testStoreKit()
                            }
                            .buttonStyle(.bordered)
                            .disabled(isLoading)

                            Button("Check Subscription") {
                                checkSubscription()
                            }
                            .buttonStyle(.bordered)
                            .disabled(isLoading)
                        }
                        
                        if isLoading {
                            ProgressView("Loading...")
                                .progressViewStyle(CircularProgressViewStyle())
                        }
                    }
                    
                    // Storage Test Section
                    VStack(alignment: .leading, spacing: 10) {
                        Text("💾 Storage Test")
                            .font(.headline)
                            .foregroundColor(.primary)
                        
                        Text("Device IDs are stored in UserDefaults:")
                            .font(.body)
                            .foregroundColor(.secondary)
                        
                        VStack(alignment: .leading, spacing: 4) {
                            Text("• PlutoAI_DeviceID: \(UserDefaults.standard.string(forKey: "PlutoAI_DeviceID") ?? "Not set")")
                                .font(.caption)
                                .textSelection(.enabled)
                            
                            Text("• PlutoAI_UserID: \(UserDefaults.standard.string(forKey: "PlutoAI_UserID") ?? "Not set")")
                                .font(.caption)
                                .textSelection(.enabled)
                        }
                        .padding()
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(8)
                        
                        Button("Reset Device ID (Test Only)") {
                            DeviceService.shared.resetDevice()
                            loadDeviceInfo()
                        }
                        .buttonStyle(.bordered)
                        .foregroundColor(.red)
                    }
                    
                    Spacer()
                }
                .padding()
            }
            .navigationTitle("Device & StoreKit Test")
            .navigationBarTitleDisplayMode(.inline)
            .onAppear {
                loadDeviceInfo()
                testStoreKit()
            }
        }
    }
    
    private func loadDeviceInfo() {
        deviceInfo = DeviceService.shared.getDeviceInfo()
    }
    
    private func testStoreKit() {
        isLoading = true
        revenueCatStatus = "Testing..."

        let deviceUserID = DeviceService.shared.userId
        print("Testing StoreKit with User ID: \(deviceUserID)")

        Task {
            await storeKitService.loadProducts()

            DispatchQueue.main.async {
                self.isLoading = false
                self.products = self.storeKitService.products

                if !self.products.isEmpty {
                    self.revenueCatStatus = "✅ Connected - Found \(self.products.count) products"
                    print("StoreKit products found:")
                    for product in self.products {
                        print("- \(product.displayName): \(product.displayPrice)")
                    }
                } else if let errorMessage = self.storeKitService.errorMessage {
                    self.revenueCatStatus = "❌ Error: \(errorMessage)"
                } else {
                    self.revenueCatStatus = "⚠️ No products configured in App Store Connect"
                }
            }
        }
    }
    
    private func checkSubscription() {
        isLoading = true
        subscriptionStatus = "Checking..."

        Task {
            await storeKitService.checkSubscriptionStatus()

            DispatchQueue.main.async {
                self.isLoading = false
                self.subscriptionStatus = self.storeKitService.isPremium ? "✅ Premium Active" : "❌ Free User"
            }
        }
    }
}

#Preview {
    DeviceTestView()
}
