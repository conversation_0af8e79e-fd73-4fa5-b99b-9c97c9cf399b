//
//  ImageResultsView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.06.02.
//

import SwiftUI
import Photos

struct ImageResultsView: View {
    let imageURL: URL
    @Binding var isPresented: Bool
    @State private var downloadedImage: UIImage?
    @State private var isDownloading = false
    @State private var showingShareSheet = false
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @State private var alertTitle = ""
    
    var body: some View {
        ZStack {
            // Black background
            Color.black
                .edgesIgnoringSafeArea(.all)
            
            VStack(spacing: 0) {
                // Header
                HStack {
                    Button(action: {
                        isPresented = false
                    }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 18, weight: .medium))
                            .foregroundColor(.white)
                            .frame(width: 44, height: 44)
                    }
                    
                    Spacer()
                    
                    Text("Results")
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.white)
                    
                    Spacer()
                    
                    // Invisible button for balance
                    Button(action: {}) {
                        Image(systemName: "xmark")
                            .font(.system(size: 18, weight: .medium))
                            .foregroundColor(.clear)
                            .frame(width: 44, height: 44)
                    }
                    .disabled(true)
                }
                .padding(.horizontal, 16)
                .padding(.top, 8)
                
                Spacer()
                
                // Main image display
                VStack(spacing: 0) {
                    AsyncImage(url: imageURL) { phase in
                        switch phase {
                        case .empty:
                            RoundedRectangle(cornerRadius: 0)
                                .fill(Color.gray.opacity(0.2))
                                .aspectRatio(1.0, contentMode: .fit)
                                .overlay(
                                    ProgressView()
                                        .scaleEffect(1.5)
                                        .tint(.white)
                                )
                        case .success(let image):
                            image
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                                .clipped()
                                .onAppear {
                                    // Convert to UIImage for sharing/saving
                                    downloadedImage = convertToUIImage(image: image)
                                }
                        case .failure(_):
                            RoundedRectangle(cornerRadius: 0)
                                .fill(Color.gray.opacity(0.2))
                                .aspectRatio(1.0, contentMode: .fit)
                                .overlay(
                                    VStack {
                                        Image(systemName: "exclamationmark.triangle")
                                            .font(.system(size: 24))
                                            .foregroundColor(.white)
                                        Text("Failed to load image")
                                            .font(.caption)
                                            .foregroundColor(.white)
                                    }
                                )
                        @unknown default:
                            EmptyView()
                        }
                    }
                    
                    // Branding
                    Text("Made by Pluto AI")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.gray)
                        .padding(.top, 20)
                        .padding(.bottom, 40)
                }
                
                // Social sharing buttons
                HStack(spacing: 20) {
                    // Save button
                    SocialButton(
                        icon: "arrow.down.circle.fill",
                        label: "Save",
                        color: .white,
                        backgroundColor: Color.gray.opacity(0.2)
                    ) {
                        saveImageToPhotos()
                    }
                    
                    // Instagram
                    SocialButton(
                        icon: "camera.circle.fill",
                        label: "Instagram",
                        color: .white,
                        backgroundColor: LinearGradient(
                            gradient: Gradient(colors: [
                                Color(red: 0.8, green: 0.4, blue: 0.9),
                                Color(red: 1.0, green: 0.6, blue: 0.0)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    ) {
                        shareToInstagram()
                    }
                    
                    // Facebook
                    SocialButton(
                        icon: "f.circle.fill",
                        label: "Facebook",
                        color: .white,
                        backgroundColor: Color(red: 0.23, green: 0.35, blue: 0.60)
                    ) {
                        shareToFacebook()
                    }
                    
                    // Twitter/X
                    SocialButton(
                        icon: "x.circle.fill",
                        label: "Twitter",
                        color: .white,
                        backgroundColor: Color.black
                    ) {
                        shareToTwitter()
                    }
                    
                    // TikTok
                    SocialButton(
                        icon: "music.note.circle.fill",
                        label: "TikTok",
                        color: .white,
                        backgroundColor: Color.black
                    ) {
                        shareToTikTok()
                    }
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 40)
            }
        }
        .alert(alertTitle, isPresented: $showingAlert) {
            Button("OK") { }
        } message: {
            Text(alertMessage)
        }
        .sheet(isPresented: $showingShareSheet) {
            if let image = downloadedImage {
                ShareSheet(activityItems: [image])
            }
        }
    }
    
    // MARK: - Helper Functions
    
    private func convertToUIImage(image: Image) -> UIImage? {
        // This is a simplified approach - in a real app you might want to
        // download the image data directly from the URL
        let controller = UIHostingController(rootView: image)
        let view = controller.view
        
        let targetSize = CGSize(width: 1024, height: 1024)
        view?.bounds = CGRect(origin: .zero, size: targetSize)
        view?.backgroundColor = UIColor.clear
        
        let renderer = UIGraphicsImageRenderer(size: targetSize)
        return renderer.image { _ in
            view?.drawHierarchy(in: controller.view.bounds, afterScreenUpdates: true)
        }
    }
    
    private func downloadImageData() async -> UIImage? {
        do {
            let (data, _) = try await URLSession.shared.data(from: imageURL)
            return UIImage(data: data)
        } catch {
            print("Failed to download image: \(error)")
            return nil
        }
    }
    
    private func saveImageToPhotos() {
        guard let image = downloadedImage else {
            // Try to download the image first
            Task {
                if let downloadedImg = await downloadImageData() {
                    await MainActor.run {
                        self.downloadedImage = downloadedImg
                        saveImageToPhotos()
                    }
                } else {
                    await MainActor.run {
                        showAlert(title: "Error", message: "Failed to download image")
                    }
                }
            }
            return
        }
        
        // Check photo library permission
        PHPhotoLibrary.requestAuthorization(for: .addOnly) { status in
            DispatchQueue.main.async {
                switch status {
                case .authorized, .limited:
                    saveImage(image)
                case .denied, .restricted:
                    showAlert(title: "Permission Denied", message: "Please allow photo library access in Settings to save images.")
                case .notDetermined:
                    showAlert(title: "Permission Required", message: "Photo library access is required to save images.")
                @unknown default:
                    showAlert(title: "Error", message: "Unable to access photo library.")
                }
            }
        }
    }
    
    private func saveImage(_ image: UIImage) {
        // Add watermark
        let watermarkedImage = addWatermark(to: image)

        // Use PHPhotoLibrary for better error handling
        PHPhotoLibrary.shared().performChanges({
            PHAssetChangeRequest.creationRequestForAsset(from: watermarkedImage)
        }) { success, error in
            DispatchQueue.main.async {
                if success {
                    showAlert(title: "Saved!", message: "Image saved to your photo library")

                    // Haptic feedback
                    let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                    impactFeedback.impactOccurred()
                } else if let error = error {
                    showAlert(title: "Save Failed", message: error.localizedDescription)
                } else {
                    showAlert(title: "Save Failed", message: "Unknown error occurred")
                }
            }
        }
    }
    
    private func addWatermark(to image: UIImage) -> UIImage {
        let renderer = UIGraphicsImageRenderer(size: image.size)
        return renderer.image { context in
            // Draw original image
            image.draw(at: .zero)
            
            // Add watermark
            let watermarkText = "PlutoAI_Generated_\(Int(Date().timeIntervalSince1970))"
            let attributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: 24, weight: .medium),
                .foregroundColor: UIColor.white.withAlphaComponent(0.7)
            ]
            
            let textSize = watermarkText.size(withAttributes: attributes)
            let textRect = CGRect(
                x: image.size.width - textSize.width - 20,
                y: image.size.height - textSize.height - 20,
                width: textSize.width,
                height: textSize.height
            )
            
            watermarkText.draw(in: textRect, withAttributes: attributes)
        }
    }
    
    private func shareToInstagram() {
        guard let image = downloadedImage else { return }
        
        if let instagramURL = URL(string: "instagram://app") {
            if UIApplication.shared.canOpenURL(instagramURL) {
                showingShareSheet = true
            } else {
                showAlert(title: "Instagram Not Found", message: "Instagram app is not installed")
            }
        }
    }
    
    private func shareToFacebook() {
        showingShareSheet = true
    }
    
    private func shareToTwitter() {
        showingShareSheet = true
    }
    
    private func shareToTikTok() {
        showingShareSheet = true
    }
    
    private func showAlert(title: String, message: String) {
        alertTitle = title
        alertMessage = message
        showingAlert = true
    }
}

// MARK: - Social Button Component
struct SocialButton<Background: ShapeStyle>: View {
    let icon: String
    let label: String
    let color: Color
    let backgroundColor: Background
    let action: () -> Void
    @State private var isPressed = false

    var body: some View {
        VStack(spacing: 8) {
            Button(action: {
                withAnimation(.spring(response: 0.2, dampingFraction: 0.8)) {
                    isPressed = true
                }

                // Haptic feedback
                let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                impactFeedback.impactOccurred()

                action()

                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    withAnimation(.spring(response: 0.2, dampingFraction: 0.8)) {
                        isPressed = false
                    }
                }
            }) {
                Image(systemName: icon)
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(color)
                    .frame(width: 60, height: 60)
                    .background(
                        Circle()
                            .fill(backgroundColor)
                    )
                    .scaleEffect(isPressed ? 0.9 : 1.0)
            }
            .buttonStyle(PlainButtonStyle())

            Text(label)
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(.white)
        }
    }
}

// MARK: - Share Sheet
struct ShareSheet: UIViewControllerRepresentable {
    let activityItems: [Any]

    func makeUIViewController(context: Context) -> UIActivityViewController {
        let controller = UIActivityViewController(
            activityItems: activityItems,
            applicationActivities: nil
        )
        return controller
    }

    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
}

#Preview {
    ImageResultsView(
        imageURL: URL(string: "https://example.com/image.jpg")!,
        isPresented: .constant(true)
    )
    .preferredColorScheme(.dark)
}
