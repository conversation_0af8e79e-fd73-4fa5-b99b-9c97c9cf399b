//
//  ToolsView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.04.30.
//

import SwiftUI

struct ToolsView: View {
    @Environment(\.presentationMode) var presentationMode
    @Environment(\.dismiss) private var dismiss
    @GestureState private var dragOffset = CGSize.zero

    var body: some View {
        ZStack(alignment: .top) {
            // Header with title and close button
            HStack {
                Button(action: {
                    // Dismiss
                    dismiss()
                }) {
                    Image(systemName: "xmark")
                        .font(.system(size: 20))
                        .foregroundColor(.white)
                        .padding()
                }

                Spacer()

                Text("Elite Tools")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)

                Spacer()

                Button(action: {
                    // Show info
                }) {
                    Image(systemName: "info.circle")
                        .font(.system(size: 20))
                        .foregroundColor(.white)
                        .padding()
                }
            }
            .padding(.top, 8)
            .background(Color.black)
            .zIndex(1)

            // Main content
            ScrollView {
                VStack(spacing: 8) {
                    // Add padding to account for the header
                    Spacer()
                        .frame(height: 60)

                    // Grid of tools - 2 columns, 3 rows layout
                    LazyVGrid(columns: [
                        GridItem(.flexible(), spacing: 0),
                        GridItem(.flexible(), spacing: 0)
                    ], spacing: 0) {
                        // Display all elite tools
                        ForEach(Tool.eliteTools) { tool in
                            ToolCardView(tool: tool)
                                .frame(height: 150)
                        }
                    }
                    .padding(.horizontal)

                    // Add some bottom padding
                    Spacer()
                        .frame(height: 20)
                }
                .padding(.bottom)
            }
        }
        .background(Color.black.edgesIgnoringSafeArea(.all))
        .navigationBarHidden(true)
        // Enable swipe to dismiss
        .gesture(
            DragGesture().updating($dragOffset, body: { (value, state, transaction) in
                // Only allow right-to-left swipe
                if value.startLocation.x < 50 && value.translation.width > 0 {
                    state = value.translation
                }
            })
            .onEnded({ value in
                // Dismiss if swiped far enough
                if value.startLocation.x < 50 && value.translation.width > 100 {
                    dismiss()
                }
            })
        )
        // Apply the drag offset to move the view
        .offset(x: dragOffset.width > 0 ? dragOffset.width : 0)
    }
}

#Preview {
    NavigationView {
        ToolsView()
    }
    .preferredColorScheme(.dark)
}
