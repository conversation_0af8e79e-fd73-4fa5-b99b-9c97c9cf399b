//
//  LiquidGlassContentView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.01.18.
//  Enhanced ContentView with Liquid Glass Design
//

import SwiftUI
import CoreData

struct LiquidGlassContentView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.colorScheme) private var colorScheme
    @State private var isInitialized = false
    @State private var loadingProgress: Double = 0
    @State private var showSplash = true
    
    var body: some View {
        ZStack {
            if showSplash {
                // Liquid Glass Splash Screen
                LiquidGlassSplashScreen(
                    progress: $loadingProgress,
                    isComplete: $isInitialized
                )
                .transition(.opacity.combined(with: .scale))
            } else if isInitialized {
                // Main app interface with Liquid Glass
                NavigationView {
                    LiquidGlassHomeView()
                }
                .navigationViewStyle(StackNavigationViewStyle())
                .preferredColorScheme(.dark)
                .environment(\.managedObjectContext, viewContext)
                .transition(.opacity.combined(with: .move(edge: .bottom)))
            }
        }
        .animation(LiquidGlassAnimations.spring, value: showSplash)
        .animation(LiquidGlassAnimations.spring, value: isInitialized)
        .onAppear {
            initializeDevice()
        }
    }
    
    private func initializeDevice() {
        // Simulate loading progress
        Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { timer in
            loadingProgress += 0.05
            
            if loadingProgress >= 1.0 {
                timer.invalidate()
                
                // Initialize device service
                Task {
                    await DeviceService.shared.initializeDevice()
                    await MainActor.run {
                        withAnimation(LiquidGlassAnimations.spring.delay(0.5)) {
                            isInitialized = true
                        }
                        
                        withAnimation(LiquidGlassAnimations.spring.delay(1.0)) {
                            showSplash = false
                        }
                    }
                }
            }
        }
    }
}

// MARK: - Liquid Glass Splash Screen

struct LiquidGlassSplashScreen: View {
    @Binding var progress: Double
    @Binding var isComplete: Bool
    @State private var logoScale: CGFloat = 0.8
    @State private var glowIntensity: Double = 0
    @State private var particlesVisible = false
    
    var body: some View {
        ZStack {
            // Dynamic background
            MorphingBackground(
                colors: [.blue.opacity(0.4), .purple.opacity(0.3), .cyan.opacity(0.2)],
                duration: 8
            )
            .ignoresSafeArea()
            
            // Particle system
            if particlesVisible {
                ParticleSystem(
                    particleCount: 25,
                    colors: [.blue, .purple, .cyan, .white]
                )
                .opacity(0.6)
                .ignoresSafeArea()
            }
            
            VStack(spacing: 40) {
                Spacer()
                
                // Logo section
                VStack(spacing: 24) {
                    // Main logo
                    ZStack {
                        // Glow effect
                        Circle()
                            .fill(
                                RadialGradient(
                                    colors: [.blue.opacity(glowIntensity), .clear],
                                    center: .center,
                                    startRadius: 0,
                                    endRadius: 80
                                )
                            )
                            .frame(width: 160, height: 160)
                            .blur(radius: 20)
                        
                        // Logo background
                        Circle()
                            .fill(.ultraThinMaterial)
                            .frame(width: 120, height: 120)
                            .overlay(
                                Circle()
                                    .stroke(
                                        LinearGradient(
                                            colors: [.white.opacity(0.4), .white.opacity(0.1)],
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        ),
                                        lineWidth: 2
                                    )
                            )
                        
                        // Logo icon
                        Image(systemName: "brain.head.profile")
                            .font(.system(size: 50, weight: .medium))
                            .foregroundColor(.blue)
                            .shadow(color: .blue.opacity(0.5), radius: 10)
                    }
                    .scaleEffect(logoScale)
                    .animation(
                        Animation.spring(response: 1.5, dampingFraction: 0.6)
                            .repeatForever(autoreverses: true),
                        value: logoScale
                    )
                    
                    // App name
                    VStack(spacing: 8) {
                        Text("Pluto AI")
                            .font(.system(size: 36, weight: .bold))
                            .foregroundColor(.white)
                            .shadow(color: .blue.opacity(0.5), radius: 5)
                        
                        Text("Chat Bot Assistant")
                            .font(.system(size: 18, weight: .medium))
                            .foregroundColor(.white.opacity(0.8))
                            .shadow(color: .black.opacity(0.5), radius: 2)
                    }
                }
                
                Spacer()
                
                // Loading section
                VStack(spacing: 20) {
                    // Progress bar
                    VStack(spacing: 12) {
                        HStack {
                            Text("Initializing AI...")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(.white.opacity(0.9))
                            
                            Spacer()
                            
                            Text("\(Int(progress * 100))%")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(.blue)
                        }
                        
                        // Progress bar
                        ZStack(alignment: .leading) {
                            // Background
                            RoundedRectangle(cornerRadius: 8)
                                .fill(.ultraThinMaterial)
                                .frame(height: 8)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 8)
                                        .stroke(.white.opacity(0.2), lineWidth: 1)
                                )
                            
                            // Progress fill
                            RoundedRectangle(cornerRadius: 8)
                                .fill(
                                    LinearGradient(
                                        colors: [.blue, .cyan],
                                        startPoint: .leading,
                                        endPoint: .trailing
                                    )
                                )
                                .frame(width: max(0, UIScreen.main.bounds.width * 0.6 * progress), height: 8)
                                .shadow(color: .blue.opacity(0.5), radius: 4)
                                .animation(LiquidGlassAnimations.gentle, value: progress)
                        }
                        .frame(width: UIScreen.main.bounds.width * 0.6)
                    }
                    
                    // Loading dots
                    HStack(spacing: 8) {
                        ForEach(0..<3) { index in
                            Circle()
                                .fill(.blue)
                                .frame(width: 8, height: 8)
                                .scaleEffect(
                                    1 + sin(Date().timeIntervalSince1970 * 3 + Double(index) * 0.5) * 0.5
                                )
                                .animation(
                                    Animation.easeInOut(duration: 0.6)
                                        .repeatForever()
                                        .delay(Double(index) * 0.2),
                                    value: progress
                                )
                        }
                    }
                }
                .padding(.horizontal, 40)
                .padding(.bottom, 60)
            }
        }
        .onAppear {
            // Start animations
            withAnimation(LiquidGlassAnimations.spring) {
                logoScale = 1.1
                glowIntensity = 0.8
                particlesVisible = true
            }
        }
        .onChange(of: isComplete) { complete in
            if complete {
                withAnimation(LiquidGlassAnimations.spring) {
                    logoScale = 1.2
                    glowIntensity = 1.0
                }
            }
        }
    }
}

// MARK: - Alternative Simple Integration

struct SimpleLiquidGlassContentView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @State private var isInitialized = false
    
    var body: some View {
        Group {
            if isInitialized {
                // Use Liquid Glass Home View
                NavigationView {
                    LiquidGlassHomeView()
                }
                .navigationViewStyle(StackNavigationViewStyle())
                .preferredColorScheme(.dark)
                .environment(\.managedObjectContext, viewContext)
            } else {
                // Enhanced loading screen
                ZStack {
                    // Dynamic background
                    MorphingBackground(
                        colors: [.blue.opacity(0.3), .purple.opacity(0.2)],
                        duration: 6
                    )
                    .ignoresSafeArea()
                    
                    // Loading content
                    VStack(spacing: 30) {
                        // Logo
                        Image(systemName: "brain.head.profile")
                            .font(.system(size: 60))
                            .foregroundColor(.blue)
                            .frame(width: 100, height: 100)
                            .background(
                                Circle()
                                    .fill(.ultraThinMaterial)
                                    .overlay(
                                        Circle()
                                            .stroke(.blue.opacity(0.3), lineWidth: 2)
                                    )
                            )
                            .shadow(color: .blue.opacity(0.3), radius: 20)
                            .pulse()
                        
                        // Text
                        VStack(spacing: 12) {
                            Text("Pluto AI")
                                .font(.system(size: 32, weight: .bold))
                                .foregroundColor(.white)
                            
                            Text("Initializing...")
                                .font(.system(size: 18))
                                .foregroundColor(.secondary)
                        }
                        
                        // Loading indicator
                        LiquidLoadingView(size: 50, color: .blue)
                    }
                }
            }
        }
        .onAppear {
            initializeDevice()
        }
    }
    
    private func initializeDevice() {
        Task {
            // Add a small delay for the loading animation
            try? await Task.sleep(nanoseconds: 2_000_000_000) // 2 seconds
            
            await DeviceService.shared.initializeDevice()
            await MainActor.run {
                withAnimation(LiquidGlassAnimations.spring) {
                    isInitialized = true
                }
            }
        }
    }
}

#Preview("Full Liquid Glass") {
    LiquidGlassContentView()
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}

#Preview("Simple Integration") {
    SimpleLiquidGlassContentView()
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
