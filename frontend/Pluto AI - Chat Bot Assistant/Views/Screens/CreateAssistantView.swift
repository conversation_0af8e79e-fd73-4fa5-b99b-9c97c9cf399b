//
//  CreateAssistantView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.05.01.
//

import SwiftUI

struct CreateAssistantView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var assistantManager = AssistantManager.shared

    // Form fields
    @State private var assistantName = ""
    @State private var selectedAvatar: String? = nil
    @State private var selectedTone = "Standard"
    @State private var selectedRole = ""
    @State private var assistantDescription = ""

    // Available options
    private let avatarOptions = ["Add Photo", "Artist", "Business Planner", "Creative Writer", "Chef", "Doctor"]
    private let toneOptions = ["Standard", "Friendly", "Professional", "Romantic"]
    private let roleOptions = ["Friend", "Girlfriend", "Boyfriend", "Wife", "Husband"]

    var body: some View {
        ZStack {
            Color.black.edgesIgnoringSafeArea(.all)

            VStack(spacing: 0) {
                // Header
                HStack {
                    Button(action: {
                        dismiss()
                    }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 20, weight: .medium))
                            .foregroundColor(.white)
                    }

                    Spacer()

                    Text("Create Your Assistant")
                        .font(.title2)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)

                    Spacer()
                }
                .padding(.horizontal, 20)
                .padding(.top, 16)
                .padding(.bottom, 24)

                // Form content
                ScrollView {
                    VStack(alignment: .leading, spacing: 24) {
                        // Assistant Name
                        VStack(alignment: .leading, spacing: 12) {
                            Text("Assistant Name")
                                .font(.title3)
                                .fontWeight(.semibold)
                                .foregroundColor(.white)

                            TextField("e.g John", text: $assistantName)
                                .font(.body)
                                .foregroundColor(.white)
                                .padding(.horizontal, 16)
                                .padding(.vertical, 16)
                                .background(Color.gray.opacity(0.2))
                                .cornerRadius(12)
                        }

                        // Choose Avatar
                        VStack(alignment: .leading, spacing: 12) {
                            Text("Choose Avatar")
                                .font(.title3)
                                .fontWeight(.semibold)
                                .foregroundColor(.white)

                            ScrollView(.horizontal, showsIndicators: false) {
                                HStack(spacing: 12) {
                                    ForEach(avatarOptions, id: \.self) { avatar in
                                        AvatarOptionView(
                                            avatar: avatar,
                                            isSelected: selectedAvatar == avatar,
                                            onTap: {
                                                selectedAvatar = avatar
                                            }
                                        )
                                    }
                                }
                                .padding(.horizontal, 4)
                            }
                        }

                        // Tone
                        VStack(alignment: .leading, spacing: 12) {
                            Text("Tone")
                                .font(.title3)
                                .fontWeight(.semibold)
                                .foregroundColor(.white)

                            ScrollView(.horizontal, showsIndicators: false) {
                                HStack(spacing: 12) {
                                    ForEach(toneOptions, id: \.self) { tone in
                                        ToneOptionView(
                                            tone: tone,
                                            isSelected: selectedTone == tone,
                                            onTap: {
                                                selectedTone = tone
                                            }
                                        )
                                    }
                                }
                                .padding(.horizontal, 4)
                            }
                        }

                        // Role
                        VStack(alignment: .leading, spacing: 12) {
                            HStack {
                                Text("Role")
                                    .font(.title3)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.white)

                                Text("Optional")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                            }

                            ScrollView(.horizontal, showsIndicators: false) {
                                HStack(spacing: 12) {
                                    ForEach(roleOptions, id: \.self) { role in
                                        RoleOptionView(
                                            role: role,
                                            isSelected: selectedRole == role,
                                            onTap: {
                                                selectedRole = selectedRole == role ? "" : role
                                            }
                                        )
                                    }
                                }
                                .padding(.horizontal, 4)
                            }
                        }

                        // Description
                        VStack(alignment: .leading, spacing: 12) {
                            HStack {
                                Text("Description")
                                    .font(.title3)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.white)

                                Text("Optional")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                            }

                            TextField("e.g coding mentor", text: $assistantDescription, axis: .vertical)
                                .font(.body)
                                .foregroundColor(.white)
                                .padding(.horizontal, 16)
                                .padding(.vertical, 16)
                                .background(Color.gray.opacity(0.2))
                                .cornerRadius(12)
                                .lineLimit(3...6)
                        }

                        // Spacer for bottom padding
                        Spacer(minLength: 100)
                    }
                    .padding(.horizontal, 20)
                }

                // Create Button
                VStack {
                    Button(action: {
                        createAssistant()
                    }) {
                        Text("Create")
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.black)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 16)
                            .background(Color.white)
                            .cornerRadius(12)
                    }
                    .disabled(assistantName.isEmpty)
                    .opacity(assistantName.isEmpty ? 0.5 : 1.0)
                    .padding(.horizontal, 20)
                    .padding(.bottom, 32)
                }
            }
        }
        .navigationBarHidden(true)
    }

    private func createAssistant() {
        // Generate a random color for the assistant
        let colors: [Color] = [.blue, .green, .orange, .purple, .red, .pink, .cyan, .yellow, .indigo, .mint]
        let randomColor = colors.randomElement() ?? .blue

        // Determine icon based on selected avatar or use a default
        let iconName: String
        if let avatar = selectedAvatar, avatar != "Add Photo" {
            // Map avatar names to system icons
            switch avatar {
            case "Artist":
                iconName = "paintbrush.fill"
            case "Business Planner":
                iconName = "briefcase.fill"
            case "Creative Writer":
                iconName = "pencil"
            case "Chef":
                iconName = "fork.knife"
            case "Doctor":
                iconName = "heart.text.square"
            default:
                iconName = "person.fill"
            }
        } else {
            iconName = "person.fill"
        }

        // Create the new assistant
        let newAssistant = Assistant(
            name: assistantName,
            description: assistantDescription.isEmpty ? "Custom assistant created by you." : assistantDescription,
            iconName: iconName,
            backgroundColor: randomColor,
            suggestedPrompts: [
                SuggestedPrompt(text: "What can you help me with?", icon: "questionmark.circle.fill", description: "Learn about this assistant's capabilities"),
                SuggestedPrompt(text: "Let's have a conversation", icon: "bubble.left.and.bubble.right.fill", description: "Start chatting with your custom assistant"),
                SuggestedPrompt(text: "Help me with a task", icon: "checkmark.circle.fill", description: "Get assistance with your work")
            ],
            isCustom: true
        )

        // Add to the assistant manager
        assistantManager.addCustomAssistant(newAssistant)

        print("Created custom assistant: \(assistantName)")

        // Dismiss the view
        dismiss()
    }
}

// MARK: - Avatar Option View
struct AvatarOptionView: View {
    let avatar: String
    let isSelected: Bool
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            if avatar == "Add Photo" {
                // Add Photo option
                VStack(spacing: 8) {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.gray.opacity(0.3))
                        .frame(width: 80, height: 80)
                        .overlay(
                            Image(systemName: "camera.fill")
                                .font(.system(size: 24))
                                .foregroundColor(.white)
                        )
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(isSelected ? Color.white : Color.clear, lineWidth: 2)
                        )

                    Text("Add Photo")
                        .font(.caption)
                        .foregroundColor(.white)
                        .multilineTextAlignment(.center)
                }
            } else {
                // Avatar image option
                VStack(spacing: 8) {
                    if let uiImage = UIImage(named: avatar) {
                        Image(uiImage: uiImage)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 80, height: 80)
                            .clipShape(RoundedRectangle(cornerRadius: 12))
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(isSelected ? Color.white : Color.clear, lineWidth: 2)
                            )
                    } else {
                        // Fallback for missing images
                        RoundedRectangle(cornerRadius: 12)
                            .fill(LinearGradient(
                                gradient: Gradient(colors: [Color.blue, Color.purple]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ))
                            .frame(width: 80, height: 80)
                            .overlay(
                                Image(systemName: "person.fill")
                                    .font(.system(size: 24))
                                    .foregroundColor(.white)
                            )
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(isSelected ? Color.white : Color.clear, lineWidth: 2)
                            )
                    }
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Tone Option View
struct ToneOptionView: View {
    let tone: String
    let isSelected: Bool
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            Text(tone)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(isSelected ? .black : .white)
                .padding(.horizontal, 16)
                .padding(.vertical, 10)
                .background(isSelected ? Color.white : Color.gray.opacity(0.3))
                .cornerRadius(20)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Role Option View
struct RoleOptionView: View {
    let role: String
    let isSelected: Bool
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            Text(role)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(isSelected ? .black : .white)
                .padding(.horizontal, 16)
                .padding(.vertical, 10)
                .background(isSelected ? Color.white : Color.gray.opacity(0.3))
                .cornerRadius(20)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    CreateAssistantView()
        .preferredColorScheme(.dark)
}
