//
//  GPTImageGeneratorView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.05.10.
//

import SwiftUI

struct GPTImageGeneratorView: View {
    @Environment(\.presentationMode) var presentationMode
    @State private var promptText = ""
    @State private var generatedImageURL: URL?
    @State private var isGenerating = false
    @State private var errorMessage: String?
    @State private var navigateToChat = false
    @State private var selectedSize = "1024x1024"
    
    // Available sizes for GPT-Image-1
    let availableSizes = ["1024x1024", "1792x1024", "1024x1792"]
    
    var body: some View {
        NavigationView {
            ZStack {
                // Background color
                Color.black.edgesIgnoringSafeArea(.all)
                
                // Loading overlay
                if isGenerating {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: Color(red: 0, green: 0.8, blue: 0.6)))
                        .scaleEffect(1.5)
                        .zIndex(1)
                }
                
                // Main content
                ScrollView {
                    VStack(spacing: 20) {
                        // Header
                        Text("GPT-Image-1 Generator")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .padding(.top, 20)
                        
                        Text("Create high-quality, photorealistic images with OpenAI's latest model")
                            .font(.subheadline)
                            .foregroundColor(.gray)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)
                        
                        // Size selector
                        VStack(alignment: .leading) {
                            Text("Image Size")
                                .font(.headline)
                                .foregroundColor(.white)
                                .padding(.leading)
                            
                            Picker("Size", selection: $selectedSize) {
                                ForEach(availableSizes, id: \.self) { size in
                                    Text(size).tag(size)
                                }
                            }
                            .pickerStyle(SegmentedPickerStyle())
                            .padding(.horizontal)
                        }
                        .padding(.top)
                        
                        // Prompt input
                        VStack(alignment: .leading) {
                            Text("Describe the image you want to create")
                                .font(.headline)
                                .foregroundColor(.white)
                                .padding(.leading)
                            
                            TextEditor(text: $promptText)
                                .padding()
                                .background(Color(UIColor.systemGray6))
                                .cornerRadius(10)
                                .frame(height: 150)
                                .padding(.horizontal)
                        }
                        
                        // Generate button
                        Button(action: {
                            generateImage()
                        }) {
                            Text("Generate Image")
                                .font(.headline)
                                .foregroundColor(.white)
                                .padding()
                                .frame(maxWidth: .infinity)
                                .background(Color(red: 0, green: 0.8, blue: 0.6))
                                .cornerRadius(10)
                                .padding(.horizontal)
                        }
                        .disabled(promptText.isEmpty || isGenerating)
                        .opacity(promptText.isEmpty || isGenerating ? 0.5 : 1.0)
                        
                        // Error message
                        if let errorMessage = errorMessage {
                            Text(errorMessage)
                                .foregroundColor(.red)
                                .padding()
                        }
                        
                        // Generated image
                        if let imageURL = generatedImageURL {
                            VStack {
                                Text("Generated Image")
                                    .font(.headline)
                                    .foregroundColor(.white)
                                
                                AsyncImage(url: imageURL) { phase in
                                    switch phase {
                                    case .empty:
                                        ProgressView()
                                    case .success(let image):
                                        image
                                            .resizable()
                                            .aspectRatio(contentMode: .fit)
                                            .cornerRadius(10)
                                    case .failure:
                                        Text("Failed to load image")
                                            .foregroundColor(.red)
                                    @unknown default:
                                        EmptyView()
                                    }
                                }
                                .frame(maxWidth: .infinity)
                                .padding()
                                
                                // Save image button
                                Button(action: {
                                    // Save image to photo library
                                    saveImage(from: imageURL)
                                }) {
                                    Text("Save to Photos")
                                        .font(.headline)
                                        .foregroundColor(.white)
                                        .padding()
                                        .frame(maxWidth: .infinity)
                                        .background(Color.blue)
                                        .cornerRadius(10)
                                        .padding(.horizontal)
                                }
                            }
                        }
                        
                        Spacer()
                    }
                    .padding(.bottom, 50)
                }
            }
            .navigationBarTitle("", displayMode: .inline)
            .navigationBarItems(leading: Button(action: {
                presentationMode.wrappedValue.dismiss()
            }) {
                Image(systemName: "chevron.left")
                    .foregroundColor(Color(red: 0, green: 0.8, blue: 0.6))
                Text("Back")
                    .foregroundColor(Color(red: 0, green: 0.8, blue: 0.6))
            })
        }
    }
    
    // Generate image
    private func generateImage() {
        // Validate prompt
        guard !promptText.isEmpty else {
            errorMessage = "Please enter a prompt"
            return
        }
        
        // Start loading
        isGenerating = true
        errorMessage = nil
        
        // Call image generation service
        Task {
            do {
                let imageURL = try await ImageGenerationService.shared.generateImage(
                    prompt: promptText,
                    model: "gpt-image-1",
                    size: selectedSize
                )
                
                // Update UI on main thread
                DispatchQueue.main.async {
                    self.generatedImageURL = imageURL
                    self.isGenerating = false
                }
            } catch {
                // Handle error
                DispatchQueue.main.async {
                    self.errorMessage = "Error generating image: \(error.localizedDescription)"
                    self.isGenerating = false
                }
            }
        }
    }
    
    // Save image to photo library
    private func saveImage(from url: URL) {
        // Download image and save to photo library
        URLSession.shared.dataTask(with: url) { data, response, error in
            if let error = error {
                DispatchQueue.main.async {
                    self.errorMessage = "Error downloading image: \(error.localizedDescription)"
                }
                return
            }
            
            guard let data = data, let image = UIImage(data: data) else {
                DispatchQueue.main.async {
                    self.errorMessage = "Error creating image from data"
                }
                return
            }
            
            // Save to photo library
            UIImageWriteToSavedPhotosAlbum(image, nil, nil, nil)
            
            DispatchQueue.main.async {
                // Show success message
                self.errorMessage = "Image saved to photo library"
            }
        }.resume()
    }
}

struct GPTImageGeneratorView_Previews: PreviewProvider {
    static var previews: some View {
        GPTImageGeneratorView()
    }
}
