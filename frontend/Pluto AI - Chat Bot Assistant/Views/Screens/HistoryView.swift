//
//  HistoryView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.05.01.
//

import SwiftUI
import CoreData

struct HistoryView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.managedObjectContext) private var viewContext

    @State private var selectedTab = 0 // 0 for Recent, 1 for Saved
    @State private var showFilter = false
    @State private var searchText = ""
    @State private var isSearching = false
    @State private var refreshID = UUID() // Used to force refresh the view

    // Use ChatHistoryService to load chats from backend
    @StateObject private var chatHistoryService = ChatHistoryService.shared

    // Computed property to get chat histories from the service
    private var chatHistories: [ChatHistory] {
        chatHistoryService.chats.map { backendChat in
            var chatHistory = ChatHistory(
                id: UUID(uuidString: backendChat.id) ?? UUID(),
                title: backendChat.title,
                date: backendChat.createdAt,
                isSaved: false, // Backend doesn't have saved status yet
                modelName: backendChat.assistantId
            )

            // Convert backend messages to local Message objects
            chatHistory.messages = backendChat.messages.map { backendMessage in
                Message(
                    id: UUID(uuidString: backendMessage.id) ?? UUID(),
                    content: backendMessage.content,
                    isUser: backendMessage.isUser,
                    timestamp: backendMessage.createdAt
                )
            }

            return chatHistory
        }
    }

    // Filtered histories based on search and tab
    private var filteredHistories: [ChatHistory] {
        let histories = chatHistories

        // First filter by tab (Recent or Saved)
        let tabFiltered = selectedTab == 0
            ? histories // Recent tab shows all
            : histories.filter { $0.isSaved } // Saved tab shows only saved chats

        // Then filter by search text if not empty
        let searchFiltered = searchText.isEmpty ? tabFiltered : tabFiltered.filter {
            $0.title.localizedCaseInsensitiveContains(searchText)
        }

        return searchFiltered
    }

    // Group histories by date section
    private var groupedHistories: [(String, [ChatHistory])] {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())

        var result: [(String, [ChatHistory])] = []

        // Today's chats
        let todayChats = filteredHistories.filter {
            calendar.isDate(calendar.startOfDay(for: $0.date), inSameDayAs: today)
        }
        if !todayChats.isEmpty {
            result.append(("TODAY", todayChats))
        }

        // Last 7 days (excluding today)
        if let sixDaysAgo = calendar.date(byAdding: .day, value: -6, to: today) {
            let recentChats = filteredHistories.filter {
                let chatDate = calendar.startOfDay(for: $0.date)
                return chatDate < today && chatDate >= sixDaysAgo
            }
            if !recentChats.isEmpty {
                result.append(("6 DAYS AGO", recentChats))
            }
        }

        // Older chats
        if let sevenDaysAgo = calendar.date(byAdding: .day, value: -7, to: today) {
            let olderChats = filteredHistories.filter {
                calendar.startOfDay(for: $0.date) < sevenDaysAgo
            }
            if !olderChats.isEmpty {
                result.append(("OLDER", olderChats))
            }
        }

        return result
    }

    var body: some View {
        ZStack {
            Color.black.edgesIgnoringSafeArea(.all)

            VStack(spacing: 0) {
                // Custom navigation bar
                HStack {
                    Button(action: {
                        dismiss()
                    }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 20, weight: .medium))
                            .foregroundColor(.white)
                    }

                    Spacer()

                    Text("History")
                        .font(.system(size: 20, weight: .semibold))
                        .foregroundColor(.white)

                    Spacer()

                    HStack(spacing: 20) {
                        Button(action: {
                            // Select all action
                        }) {
                            Image(systemName: "checkmark.circle")
                                .font(.system(size: 20, weight: .medium))
                                .foregroundColor(.white)
                        }

                        Button(action: {
                            isSearching.toggle()
                        }) {
                            Image(systemName: "magnifyingglass")
                                .font(.system(size: 20, weight: .medium))
                                .foregroundColor(.white)
                        }
                    }
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)

                // Search bar (shown when isSearching is true)
                if isSearching {
                    HStack {
                        Image(systemName: "magnifyingglass")
                            .foregroundColor(.gray)
                            .padding(.leading, 12)

                        TextField("Search", text: $searchText)
                            .foregroundColor(.white)
                            .padding(.vertical, 12)

                        if !searchText.isEmpty {
                            Button(action: {
                                searchText = ""
                            }) {
                                Image(systemName: "xmark.circle.fill")
                                    .foregroundColor(.gray)
                                    .padding(.trailing, 12)
                            }
                        }
                    }
                    .background(Color.gray.opacity(0.2))
                    .cornerRadius(12)
                    .padding(.horizontal, 20)
                    .padding(.bottom, 16)
                }

                // Tab selector
                HStack(spacing: 0) {
                    TabButton(title: "Recent", isSelected: selectedTab == 0) {
                        selectedTab = 0
                    }

                    TabButton(title: "Saved", isSelected: selectedTab == 1) {
                        selectedTab = 1
                    }

                    Spacer()

                    // Filter button
                    Button(action: {
                        showFilter.toggle()
                    }) {
                        HStack(spacing: 6) {
                            Text("Filter")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(.white)

                            Image(systemName: "chevron.down")
                                .font(.system(size: 12))
                                .foregroundColor(.white)
                        }
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(Color.gray.opacity(0.3))
                        .cornerRadius(20)
                    }
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 16)

                // Chat history list
                ScrollView {
                    if chatHistoryService.isLoading {
                        // Loading state
                        VStack(spacing: 16) {
                            ProgressView()
                                .scaleEffect(1.2)
                                .tint(.white)

                            Text("Loading chat history...")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(.gray)
                        }
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .padding(.top, 100)
                    } else if chatHistories.isEmpty {
                        // Empty state
                        VStack(spacing: 16) {
                            Image(systemName: "message.circle")
                                .font(.system(size: 48))
                                .foregroundColor(.gray)

                            Text("No chat history")
                                .font(.system(size: 18, weight: .semibold))
                                .foregroundColor(.white)

                            Text("Start a conversation to see your chat history here")
                                .font(.system(size: 14))
                                .foregroundColor(.gray)
                                .multilineTextAlignment(.center)
                        }
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .padding(.top, 100)
                    } else {
                        LazyVStack(alignment: .leading, spacing: 16) {
                            ForEach(groupedHistories, id: \.0) { section, histories in
                            // Section header
                            Text(section)
                                .font(.system(size: 14, weight: .semibold))
                                .foregroundColor(.gray)
                                .padding(.horizontal, 20)
                                .padding(.top, section == groupedHistories.first?.0 ? 24 : 32)

                            // Chat items
                            ForEach(histories) { history in
                                HistoryItemView(history: history)
                                    .padding(.horizontal, 20)
                            }
                        }
                        }
                        .padding(.bottom, 100) // Extra bottom padding for safe area
                    }
                }
            }
        }
        .id(refreshID) // Force view refresh when this ID changes
        .onAppear {
            // Load chats from backend when view appears
            Task {
                await chatHistoryService.loadChats()
            }

            // Set up notification observer for refresh events
            NotificationCenter.default.addObserver(forName: NSNotification.Name("RefreshHistoryView"), object: nil, queue: .main) { _ in
                // Force refresh by changing the ID
                refreshID = UUID()
                // Reload chats when refreshing
                Task {
                    await chatHistoryService.loadChats()
                }
            }
        }
    }
}

// Tab button for Recent/Saved tabs
struct TabButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Text(title)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(isSelected ? Color(red: 0.0, green: 0.8, blue: 0.6) : Color.gray)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)

                // Indicator line - teal for selected
                Rectangle()
                    .fill(isSelected ? Color(red: 0.0, green: 0.8, blue: 0.6) : Color.clear)
                    .frame(height: 2)
                    .frame(maxWidth: .infinity)
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// Individual history item view
struct HistoryItemView: View {
    let history: ChatHistory
    @State private var showOptions = false
    @State private var navigateToChat = false
    @State private var isSaved: Bool
    @Environment(\.managedObjectContext) private var viewContext

    init(history: ChatHistory) {
        self.history = history
        // Initialize the state with the history's saved status
        _isSaved = State(initialValue: history.isSaved)
    }

    var body: some View {
        Button(action: {
            navigateToChat = true
        }) {
            VStack(alignment: .leading, spacing: 16) {
                // Title
                Text(history.title)
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                    .lineLimit(1)
                    .frame(maxWidth: .infinity, alignment: .leading)

                // Preview text
                if let firstMessage = history.messages.first {
                    Text(firstMessage.content)
                        .font(.system(size: 15, weight: .regular))
                        .foregroundColor(Color.gray.opacity(0.8))
                        .lineLimit(2)
                        .frame(maxWidth: .infinity, alignment: .leading)
                }

                HStack(alignment: .center) {
                    // Model badge with improved styling
                    HStack(spacing: 6) {
                        Image(systemName: "sparkles")
                            .font(.system(size: 11, weight: .medium))
                            .foregroundColor(.white)

                        Text(history.modelName)
                            .font(.system(size: 13, weight: .medium))
                            .foregroundColor(.white)
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 7)
                    .background(Color.gray.opacity(0.4))
                    .cornerRadius(18)

                    Spacer()

                    HStack(spacing: 16) {
                        // Bookmark button
                        Button(action: {
                            // Toggle saved status in CoreData
                            ChatHistory.toggleSaved(id: history.id, context: viewContext)
                            // Update local state
                            isSaved.toggle()
                            // Notify parent view to refresh
                            NotificationCenter.default.post(name: NSNotification.Name("RefreshHistoryView"), object: nil)
                        }) {
                            Image(systemName: isSaved ? "bookmark.fill" : "bookmark")
                                .font(.system(size: 18, weight: .medium))
                                .foregroundColor(.white)
                        }

                        // Options button
                        Button(action: {
                            showOptions = true
                        }) {
                            Image(systemName: "ellipsis")
                                .font(.system(size: 18, weight: .medium))
                                .foregroundColor(.white)
                        }
                        .actionSheet(isPresented: $showOptions) {
                            ActionSheet(
                                title: Text("Options"),
                                buttons: [
                                    .default(Text("Share")) { /* Share action */ },
                                    .default(Text("Rename")) { /* Rename action */ },
                                    .destructive(Text("Delete")) {
                                        // Delete the chat history
                                        ChatHistory.deleteChat(id: history.id, context: viewContext)
                                        // Notify parent view to refresh
                                        NotificationCenter.default.post(name: NSNotification.Name("RefreshHistoryView"), object: nil)
                                    },
                                    .cancel()
                                ]
                            )
                        }
                    }
                }
            }
            .padding(20)
            .background(Color.gray.opacity(0.2))
            .cornerRadius(16)
        }
        .buttonStyle(PlainButtonStyle())
        .fullScreenCover(isPresented: $navigateToChat) {
            NavigationView {
                ChatScreenView(withMessages: history.messages)
            }
        }
    }
}

// We're using the Color+Extensions.swift file for system colors

#Preview {
    HistoryView()
        .preferredColorScheme(.dark)
}
