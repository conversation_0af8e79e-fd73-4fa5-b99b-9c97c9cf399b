//
//  YouTubeSummaryPopupView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.05.01.
//

import SwiftUI

struct YouTubeSummaryPopupView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var youtubeService = YouTubeService.shared
    @State private var youtubeURL: String = ""
    @State private var showChatScreen = false
    @State private var showProcessing = false
    @State private var showResults = false
    @State private var processingResult: YouTubeProcessingResult?
    @State private var selectedModel: AIModel = AIModel.models.first!

    var body: some View {
        ZStack {
            // Background color - semi-transparent black
            Color.black.opacity(0.9).edgesIgnoringSafeArea(.all)

            // Content
            VStack(spacing: 20) {
                // Handle indicator
                RoundedRectangle(cornerRadius: 2.5)
                    .fill(Color.gray.opacity(0.5))
                    .frame(width: 40, height: 5)
                    .padding(.top, 10)

                // Close button
                HStack {
                    Button(action: {
                        dismiss()
                    }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 18))
                            .foregroundColor(.white)
                    }
                    .padding(.leading, 10)

                    Spacer()

                    // Info button
                    Button(action: {
                        // Show info about YouTube summary
                    }) {
                        Image(systemName: "info.circle")
                            .font(.system(size: 18))
                            .foregroundColor(.gray)
                    }
                    .padding(.trailing, 10)
                }
                .padding(.horizontal, 10)

                // Icon and title
                HStack {
                    // YouTube icon
                    HStack(spacing: 2) {
                        Rectangle()
                            .fill(Color.red)
                            .frame(width: 32, height: 24)
                            .cornerRadius(6)
                            .overlay(
                                Image(systemName: "play.fill")
                                    .font(.system(size: 12))
                                    .foregroundColor(.white)
                            )

                        // Small white square to match the screenshot
                        Rectangle()
                            .fill(Color.white.opacity(0.7))
                            .frame(width: 10, height: 10)
                            .offset(x: -5, y: 0)
                    }

                    Text("YouTube Summary")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                }
                .padding(.top, 5)

                // Description
                Text("You can add the Youtube URL to let Pluto AI to answer your questions or summarize it.")
                    .font(.subheadline)
                    .foregroundColor(.gray)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
                    .padding(.top, -5)

                // YouTube URL input field
                HStack {
                    Image(systemName: "play.circle")
                        .font(.system(size: 22))
                        .foregroundColor(.gray)
                        .padding(.leading, 16)

                    TextField("Paste your YouTube link", text: $youtubeURL)
                        .padding(.vertical, 16)
                        .padding(.leading, 8)
                        .foregroundColor(.white)
                }
                .background(Color(UIColor.systemGray6).opacity(0.3))
                .cornerRadius(12)
                .padding(.horizontal, 20)
                .padding(.top, 20)

                Spacer()

                // Continue button
                Button(action: {
                    processYouTubeURL()
                }) {
                    HStack {
                        if youtubeService.isProcessing {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .black))
                                .scaleEffect(0.8)
                        }

                        Text(youtubeService.isProcessing ? "Processing..." : "Analyze Video")
                            .font(.headline)
                            .foregroundColor(.black)
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.white)
                    .cornerRadius(12)
                    .padding(.horizontal, 20)
                }
                .padding(.bottom, 30)
                .disabled(youtubeURL.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty || youtubeService.isProcessing)
                .opacity(youtubeURL.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty || youtubeService.isProcessing ? 0.5 : 1.0)
            }
            .padding(.horizontal)
        }
        .fullScreenCover(isPresented: $showResults) {
            if let result = processingResult {
                YouTubeResultsView(result: result, isPresented: $showResults)
            }
        }
        .fullScreenCover(isPresented: $showChatScreen) {
            NavigationView {
                // Create a suggestion for YouTube summary
                let youtubeSuggestion = Suggestion(
                    text: "Summarize this YouTube video: \(youtubeURL)",
                    icon: "play.rectangle.fill",
                    category: "YouTube Summary",
                    examplePrompts: [
                        "Provide a detailed summary of this video",
                        "What are the key takeaways from this content?",
                        "Extract the main points and insights"
                    ]
                )

                // Pass the suggestion to ChatScreenView with autoSend set to true
                ChatScreenView(suggestion: youtubeSuggestion, autoSend: true, showExamplesOnAppear: false, assistant: nil)
            }
        }
    }

    // MARK: - Private Methods

    private func processYouTubeURL() {
        Task {
            do {
                let result = try await youtubeService.processYouTubeURL(youtubeURL, model: selectedModel)
                DispatchQueue.main.async {
                    self.processingResult = result
                    self.showResults = true
                }
            } catch {
                // Handle error - could show an alert or error message
                print("YouTube processing error: \(error)")
            }
        }
    }
}

struct YouTubeResultsView: View {
    let result: YouTubeProcessingResult
    @Binding var isPresented: Bool
    @State private var showChatScreen = false

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // Video info
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Video Information")
                            .font(.title2)
                            .fontWeight(.bold)

                        AsyncImage(url: URL(string: result.video.thumbnailURL)) { image in
                            image
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                        } placeholder: {
                            Rectangle()
                                .fill(Color.gray.opacity(0.3))
                                .aspectRatio(16/9, contentMode: .fit)
                        }
                        .cornerRadius(12)

                        Text(result.video.title)
                            .font(.headline)
                            .fontWeight(.semibold)

                        Text("Channel: \(result.video.channelName)")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }

                    // Summary
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Summary")
                            .font(.title2)
                            .fontWeight(.bold)

                        Text(result.summary)
                            .font(.body)
                    }

                    // Key Points
                    if !result.keyPoints.isEmpty {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Key Points")
                                .font(.title2)
                                .fontWeight(.bold)

                            ForEach(Array(result.keyPoints.enumerated()), id: \.offset) { index, point in
                                HStack(alignment: .top, spacing: 8) {
                                    Text("\(index + 1).")
                                        .font(.body)
                                        .fontWeight(.medium)
                                        .foregroundColor(.blue)

                                    Text(point)
                                        .font(.body)
                                }
                            }
                        }
                    }

                    // Topics
                    if !result.topics.isEmpty {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Topics")
                                .font(.title2)
                                .fontWeight(.bold)

                            LazyVGrid(columns: [
                                GridItem(.adaptive(minimum: 100))
                            ], spacing: 8) {
                                ForEach(result.topics, id: \.self) { topic in
                                    Text(topic)
                                        .font(.caption)
                                        .padding(.horizontal, 12)
                                        .padding(.vertical, 6)
                                        .background(Color.blue.opacity(0.1))
                                        .foregroundColor(.blue)
                                        .cornerRadius(16)
                                }
                            }
                        }
                    }

                    // Chat button
                    Button(action: {
                        showChatScreen = true
                    }) {
                        Text("Ask Questions About This Video")
                            .font(.headline)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.blue)
                            .cornerRadius(12)
                    }
                    .padding(.top, 20)
                }
                .padding()
            }
            .navigationTitle("YouTube Analysis")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        isPresented = false
                    }
                }
            }
        }
        .fullScreenCover(isPresented: $showChatScreen) {
            NavigationView {
                let suggestion = Suggestion(
                    text: "I've analyzed this YouTube video: \(result.video.title). Ask me anything about it!",
                    icon: "play.rectangle.fill",
                    category: "YouTube Q&A",
                    examplePrompts: [
                        "What were the main points discussed?",
                        "Can you explain the key concepts?",
                        "What are the practical takeaways?"
                    ]
                )

                ChatScreenView(suggestion: suggestion, autoSend: true, showExamplesOnAppear: false, assistant: nil)
            }
        }
    }
}

#Preview {
    YouTubeSummaryPopupView()
        .preferredColorScheme(.dark)
}
