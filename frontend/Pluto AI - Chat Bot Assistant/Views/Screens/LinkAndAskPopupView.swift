//
//  LinkAndAskPopupView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.05.01.
//

import SwiftUI

struct LinkAndAskPopupView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var urlService = URLProcessingService.shared
    @State private var linkURL: String = ""
    @State private var showChatScreen = false
    @State private var showResults = false
    @State private var processingResult: URLProcessingResult?
    @State private var selectedModel: AIModel = AIModel.models.first!

    var body: some View {
        ZStack {
            // Background color - semi-transparent black
            Color.black.opacity(0.9).edgesIgnoringSafeArea(.all)

            // Content
            VStack(spacing: 20) {
                // Handle indicator
                RoundedRectangle(cornerRadius: 2.5)
                    .fill(Color.gray.opacity(0.5))
                    .frame(width: 40, height: 5)
                    .padding(.top, 10)

                // Close button
                HStack {
                    Button(action: {
                        dismiss()
                    }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 18))
                            .foregroundColor(.white)
                    }
                    .padding(.leading, 10)

                    Spacer()

                    // Info button
                    Button(action: {
                        // Show info about Link & Ask
                    }) {
                        Image(systemName: "info.circle")
                            .font(.system(size: 18))
                            .foregroundColor(.gray)
                    }
                    .padding(.trailing, 10)
                }
                .padding(.horizontal, 10)

                // Icon and title
                HStack {
                    // Link icon
                    Image(systemName: "link")
                        .font(.system(size: 24))
                        .foregroundColor(.white)
                        .padding(4)

                    Text("Link & Ask")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                }
                .padding(.top, 5)

                // Description
                Text("You can ask about or search anything in a webpage or Pluto AI to summarize it.")
                    .font(.subheadline)
                    .foregroundColor(.gray)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
                    .padding(.top, -5)

                // Link input field
                HStack {
                    Image(systemName: "link")
                        .font(.system(size: 22))
                        .foregroundColor(.gray)
                        .padding(.leading, 16)

                    TextField("Paste your link", text: $linkURL)
                        .foregroundColor(.white)
                        .padding(.vertical, 16)
                        .padding(.leading, 8)
                        .autocapitalization(.none)
                        .keyboardType(.URL)
                        .disableAutocorrection(true)
                }
                .background(Color(UIColor.systemGray6).opacity(0.3))
                .cornerRadius(12)
                .padding(.horizontal, 20)
                .padding(.top, 20)

                Spacer()

                // Continue button
                Button(action: {
                    processURL()
                }) {
                    HStack {
                        if urlService.isProcessing {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .black))
                                .scaleEffect(0.8)
                        }

                        Text(urlService.isProcessing ? "Processing..." : "Analyze Link")
                            .font(.headline)
                            .foregroundColor(.black)
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.white)
                    .cornerRadius(12)
                    .padding(.horizontal, 20)
                }
                .padding(.bottom, 30)
                .disabled(linkURL.isEmpty || urlService.isProcessing)
                .opacity(linkURL.isEmpty || urlService.isProcessing ? 0.5 : 1.0)
            }
            .padding(.horizontal)
        }
        .fullScreenCover(isPresented: $showResults) {
            if let result = processingResult {
                URLResultsView(result: result, isPresented: $showResults)
            }
        }
        .fullScreenCover(isPresented: $showChatScreen) {
            NavigationView {
                // Create a suggestion for link analysis
                let linkSuggestion = Suggestion(
                    text: "Analyze this webpage: \(linkURL)",
                    icon: "link",
                    category: "Link & Ask",
                    examplePrompts: [
                        "Summarize the content of this webpage",
                        "What are the key points from this article?",
                        "Extract the main information from this link"
                    ]
                )

                // Pass the suggestion to ChatScreenView with autoSend set to true
                ChatScreenView(suggestion: linkSuggestion, autoSend: true, webURL: URL(string: linkURL), assistant: nil)
            }
        }
    }

    // MARK: - Private Methods

    private func processURL() {
        Task {
            do {
                let result = try await urlService.processURL(linkURL, model: selectedModel)
                DispatchQueue.main.async {
                    self.processingResult = result
                    self.showResults = true
                }
            } catch {
                // Handle error - could show an alert or error message
                print("URL processing error: \(error)")
            }
        }
    }
}

struct URLResultsView: View {
    let result: URLProcessingResult
    @Binding var isPresented: Bool
    @State private var showChatScreen = false

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // Webpage info
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Webpage Information")
                            .font(.title2)
                            .fontWeight(.bold)

                        if let imageURL = result.webpage.imageURL {
                            AsyncImage(url: URL(string: imageURL)) { image in
                                image
                                    .resizable()
                                    .aspectRatio(contentMode: .fit)
                            } placeholder: {
                                Rectangle()
                                    .fill(Color.gray.opacity(0.3))
                                    .aspectRatio(16/9, contentMode: .fit)
                            }
                            .cornerRadius(12)
                        }

                        Text(result.webpage.title)
                            .font(.headline)
                            .fontWeight(.semibold)

                        if let siteName = result.webpage.siteName {
                            Text("Site: \(siteName)")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }

                        Text("Word Count: \(result.wordCount)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    // Summary
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Summary")
                            .font(.title2)
                            .fontWeight(.bold)

                        Text(result.summary)
                            .font(.body)
                    }

                    // Key Points
                    if !result.keyPoints.isEmpty {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Key Points")
                                .font(.title2)
                                .fontWeight(.bold)

                            ForEach(Array(result.keyPoints.enumerated()), id: \.offset) { index, point in
                                HStack(alignment: .top, spacing: 8) {
                                    Text("\(index + 1).")
                                        .font(.body)
                                        .fontWeight(.medium)
                                        .foregroundColor(.blue)

                                    Text(point)
                                        .font(.body)
                                }
                            }
                        }
                    }

                    // Topics
                    if !result.topics.isEmpty {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Topics")
                                .font(.title2)
                                .fontWeight(.bold)

                            LazyVGrid(columns: [
                                GridItem(.adaptive(minimum: 100))
                            ], spacing: 8) {
                                ForEach(result.topics, id: \.self) { topic in
                                    Text(topic)
                                        .font(.caption)
                                        .padding(.horizontal, 12)
                                        .padding(.vertical, 6)
                                        .background(Color.blue.opacity(0.1))
                                        .foregroundColor(.blue)
                                        .cornerRadius(16)
                                }
                            }
                        }
                    }

                    // Chat button
                    Button(action: {
                        showChatScreen = true
                    }) {
                        Text("Ask Questions About This Page")
                            .font(.headline)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.blue)
                            .cornerRadius(12)
                    }
                    .padding(.top, 20)
                }
                .padding()
            }
            .navigationTitle("Webpage Analysis")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        isPresented = false
                    }
                }
            }
        }
        .fullScreenCover(isPresented: $showChatScreen) {
            NavigationView {
                let suggestion = Suggestion(
                    text: "I've analyzed this webpage: \(result.webpage.title). Ask me anything about it!",
                    icon: "link",
                    category: "Webpage Q&A",
                    examplePrompts: [
                        "What were the main points discussed?",
                        "Can you explain the key concepts?",
                        "What are the practical takeaways?"
                    ]
                )

                ChatScreenView(suggestion: suggestion, autoSend: true, showExamplesOnAppear: false, assistant: nil)
            }
        }
    }
}

#Preview {
    LinkAndAskPopupView()
        .preferredColorScheme(.dark)
}
