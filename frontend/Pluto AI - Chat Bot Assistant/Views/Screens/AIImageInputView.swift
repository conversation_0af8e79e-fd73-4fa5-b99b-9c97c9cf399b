//
//  AIImageInputView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.05.01.
//

import SwiftUI

struct AIImageInputView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var promptText = ""
    @State private var navigateToChat = false
    @State private var characterCount = 0
    @FocusState private var isTextFieldFocused: Bool

    // New properties for image editing support
    let selectedImage: UIImage?
    let generationMode: ImageGenerationMode

    enum ImageGenerationMode {
        case textToImage
        case imageToImage
    }

    // Callback to pass the entered text back to the parent view
    var onTextEntered: ((String) -> Void)? = nil

    init(selectedImage: UIImage? = nil, generationMode: ImageGenerationMode = .textToImage, onTextEntered: ((String) -> Void)? = nil) {
        self.selectedImage = selectedImage
        self.generationMode = generationMode
        self.onTextEntered = onTextEntered
    }

    var body: some View {
        ZStack {
            // Semi-transparent background
            Color.black.opacity(0.5)
                .edgesIgnoringSafeArea(.all)
                .onTapGesture {
                    // Dismiss when tapping outside
                    dismiss()
                }

            // Main content in a draggable sheet
            VStack(spacing: 0) {
                // Drag indicator at the top
                Capsule()
                    .fill(Color.gray.opacity(0.5))
                    .frame(width: 40, height: 5)
                    .padding(.top, 10)
                    .padding(.bottom, 5)

                // Show selected image if in edit mode
                if let selectedImage = selectedImage, generationMode == .imageToImage {
                    VStack(spacing: 8) {
                        Text("Editing Image")
                            .font(.subheadline)
                            .foregroundColor(.white)

                        Image(uiImage: selectedImage)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(height: 80)
                            .clipped()
                            .cornerRadius(8)
                            .overlay(
                                RoundedRectangle(cornerRadius: 8)
                                    .stroke(Color.white.opacity(0.3), lineWidth: 1)
                            )
                    }
                    .padding(.horizontal)
                    .padding(.bottom, 10)
                }

                // Text input area
                ZStack(alignment: .topLeading) {
                    // Text field
                    TextEditor(text: $promptText)
                        .focused($isTextFieldFocused)
                        .scrollContentBackground(.hidden)
                        .background(Color.clear)
                        .foregroundColor(.white)
                        .padding()
                        .onChange(of: promptText) { newValue in
                            characterCount = newValue.count
                        }
                        .onAppear {
                            // Auto-focus the text field when view appears
                            isTextFieldFocused = true
                        }

                    // Placeholder text
                    if promptText.isEmpty {
                        Text(generationMode == .imageToImage ?
                             "Describe how to edit this image..." :
                             "What do you want to create today?")
                            .foregroundColor(.gray)
                            .padding(.horizontal)
                            .padding(.top, 24)
                            .padding(.leading, 5)
                    }
                }
                .frame(height: 150) // Reduced height

                // Character count
                HStack {
                    Spacer()

                    // Character count
                    Text("\(characterCount) / 1500")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
                .padding(.horizontal)

                // Continue button
                Button(action: {
                    if !promptText.isEmpty {
                        // Call the callback to pass the text back
                        onTextEntered?(promptText)
                        // Dismiss this view
                        dismiss()
                        // Navigate to chat if needed
                        navigateToChat = true
                    }
                }) {
                    Text("Continue")
                        .font(.headline)
                        .foregroundColor(.black)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.white.opacity(0.8))
                        .cornerRadius(12)
                }
                .padding(.horizontal)
                .padding(.bottom, 15)
            }
            .background(Color(UIColor.systemGray6).opacity(0.3))
            .cornerRadius(20)
            .frame(maxWidth: 300, maxHeight: 300) // Smaller size for a more compact popup
        }
        // Make the view dismissible by dragging down
        .interactiveDismissDisabled(false)
        // We don't need this anymore since we're passing the text back to the parent view
        // which will handle the navigation
        // .sheet(isPresented: $navigateToChat) {
        //     NavigationView {
        //         // Create a specialized chat view for image generation
        //         let imageGenSuggestion = Suggestion(
        //             text: promptText.isEmpty ? "Generate an image of a beautiful landscape" : promptText,
        //             icon: "🖼️",
        //             category: "AI Image Generator"
        //         )
        //
        //         ChatScreenView(suggestion: imageGenSuggestion, autoSend: true)
        //     }
        // }
    }
}

#Preview {
    AIImageInputView()
        .preferredColorScheme(.dark)
}
