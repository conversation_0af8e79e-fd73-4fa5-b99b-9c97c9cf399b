//
//  ProSubscriptionView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.05.01.
//

import SwiftUI
import StoreKit

enum SubscriptionOption: String, CaseIterable {
    case weekly = "Weekly"
    case lifetime = "Lifetime"

    var identifier: String {
        switch self {
        case .weekly:
            return "weekly"
        case .lifetime:
            return "lifetime"
        }
    }

    var displayPrice: String {
        switch self {
        case .weekly:
            return "$7.99/week"
        case .lifetime:
            return "$39.99"
        }
    }

    var buttonText: String {
        switch self {
        case .weekly:
            return "Try For Free"
        case .lifetime:
            return "Buy Now"
        }
    }

    var hasTrial: Bool {
        switch self {
        case .weekly:
            return true
        case .lifetime:
            return false
        }
    }
}

struct ProSubscriptionView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var isFreeTrialEnabled = true
    @State private var products: [Product]?
    @State private var isLoading = false
    @State private var errorMessage: String?
    @State private var selectedOption: SubscriptionOption = .weekly

    // Computed property for due date string
    private var dueDateString: String {
        let dueDate = Calendar.current.date(byAdding: .day, value: 7, to: Date()) ?? Date()
        let formatter = DateFormatter()
        formatter.dateFormat = "MMM d, yyyy"
        return formatter.string(from: dueDate)
    }

    // Background icons for the grid pattern
    private let backgroundIcons = [
        "camera.fill", "photo.fill", "doc.fill", "folder.fill", "paperplane.fill",
        "bell.fill", "bookmark.fill", "tag.fill", "link", "map.fill",
        "location.fill", "lock.fill", "key.fill", "gear", "hammer.fill",
        "wrench.fill", "scissors", "pencil", "trash.fill", "doc.text.fill",
        "chart.bar.fill", "calendar", "clock.fill", "star.fill", "heart.fill"
    ]

    var body: some View {
        ZStack {
            // Background color
            Color.black.edgesIgnoringSafeArea(.all)

            // Loading overlay
            if isLoading {
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: Color(red: 0.2, green: 0.8, blue: 0.6)))
                    .scaleEffect(1.5)
                    .zIndex(1)
            }

            // Content
            VStack(spacing: 0) {
                // Header with close button
                HStack {
                    Button(action: {
                        dismiss()
                    }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 18, weight: .medium))
                            .foregroundColor(.white)
                    }

                    Spacer()
                }
                .padding(.horizontal, 20)
                .padding(.top, 10)

                Spacer()
                    .frame(height: 40)

                // GET PRO ACCESS text
                HStack(spacing: 8) {
                    Text("GET")
                        .font(.system(size: 32, weight: .bold))
                        .foregroundColor(.white)

                    Text("PRO")
                        .font(.system(size: 32, weight: .bold))
                        .foregroundColor(Color(red: 0.2, green: 0.8, blue: 0.6))

                    Text("ACCESS")
                        .font(.system(size: 32, weight: .bold))
                        .foregroundColor(.white)
                }
                .padding(.bottom, 30)

                // App icon with background grid
                ZStack {
                    // Background grid of icons - exactly matching the design
                    VStack(spacing: 25) {
                        ForEach(0..<5) { row in
                            HStack(spacing: 25) {
                                ForEach(0..<5) { col in
                                    let isCenter = row == 2 && col == 2
                                    if !isCenter {
                                        let iconIndex = (row * 5 + col) % backgroundIcons.count
                                        Image(systemName: backgroundIcons[iconIndex])
                                            .font(.system(size: 20, weight: .medium))
                                            .foregroundColor(.gray.opacity(0.3))
                                            .frame(width: 40, height: 40)
                                    } else {
                                        // Empty space for center icon
                                        Color.clear.frame(width: 40, height: 40)
                                    }
                                }
                            }
                        }
                    }
                    .frame(width: 320, height: 320)

                    // App icon - using the actual app icon
                    ZStack {
                        // App icon with proper styling
                        Image("app_icon_display")
                            .resizable()
                            .frame(width: 140, height: 140)
                            .clipShape(RoundedRectangle(cornerRadius: 28))
                            .shadow(color: Color(red: 0.2, green: 0.8, blue: 0.6).opacity(0.4), radius: 20, x: 0, y: 10)
                    }
                }
                .padding(.bottom, 40)

                // Pro features card - matching exact design
                VStack(alignment: .leading, spacing: 8) {
                    Text("Chat & Ask AI Pro")
                        .font(.system(size: 20, weight: .semibold))
                        .foregroundColor(.white)

                    Text("Unlimited Chat Messages, Answers From GPT-4.1, Infinite Image Generations, PDF & URL & Youtube Summary")
                        .font(.system(size: 14))
                        .foregroundColor(.gray)
                        .multilineTextAlignment(.leading)
                        .lineLimit(nil)

                    Spacer()
                        .frame(height: 8)

                    // Dynamic pricing text based on toggle
                    Text(isFreeTrialEnabled ? "Free for 7 days, then $7.99/week" : "$39.99, Lifetime")
                        .font(.system(size: 14))
                        .foregroundColor(.gray)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding(.horizontal, 20)

                Spacer()
                    .frame(height: 30)

                // Plan Toggle - matching exact design
                HStack {
                    Text(isFreeTrialEnabled ? "Free Trial Enabled" : "Enable Free Trial")
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.white)

                    Spacer()

                    Toggle("", isOn: $isFreeTrialEnabled)
                        .toggleStyle(SwitchToggleStyle(tint: Color(red: 0.2, green: 0.8, blue: 0.6)))
                        .scaleEffect(1.1)
                        .onChange(of: isFreeTrialEnabled) { newValue in
                            selectedOption = newValue ? .weekly : .lifetime
                        }
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 25)

                // Payment info - dynamic based on toggle
                VStack(spacing: 15) {
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            if isFreeTrialEnabled {
                                Text("Due Today")
                                    .font(.system(size: 18, weight: .semibold))
                                    .foregroundColor(.white)

                                Text("Due \(dueDateString)")
                                    .font(.system(size: 14))
                                    .foregroundColor(.gray)
                            } else {
                                Text("One-time payment")
                                    .font(.system(size: 18, weight: .semibold))
                                    .foregroundColor(.white)

                                Text("Lifetime")
                                    .font(.system(size: 14))
                                    .foregroundColor(.gray)
                            }
                        }

                        Spacer()

                        VStack(alignment: .trailing, spacing: 4) {
                            if isFreeTrialEnabled {
                                HStack(spacing: 8) {
                                    Text("7 days Free")
                                        .font(.system(size: 18, weight: .semibold))
                                        .foregroundColor(Color(red: 0.2, green: 0.8, blue: 0.6))

                                    Text("$0.00")
                                        .font(.system(size: 18, weight: .semibold))
                                        .foregroundColor(.white)
                                }

                                Text("$7.99")
                                    .font(.system(size: 14))
                                    .foregroundColor(.gray)
                            } else {
                                HStack(spacing: 8) {
                                    Text("Save 70%")
                                        .font(.system(size: 18, weight: .semibold))
                                        .foregroundColor(Color(red: 0.2, green: 0.8, blue: 0.6))
                                }

                                Text("$39.99")
                                    .font(.system(size: 18, weight: .semibold))
                                    .foregroundColor(.white)
                            }
                        }
                    }

                    // Timeline indicator (only for free trial)
                    if isFreeTrialEnabled {
                        ZStack(alignment: .leading) {
                            // Background line
                            Rectangle()
                                .fill(Color.gray.opacity(0.3))
                                .frame(height: 3)
                                .cornerRadius(1.5)

                            // Dot indicator
                            Circle()
                                .fill(Color.white)
                                .frame(width: 10, height: 10)
                                .overlay(
                                    Circle()
                                        .stroke(Color(red: 0.2, green: 0.8, blue: 0.6), lineWidth: 2)
                                )
                        }
                    }
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 30)

                // Purchase button - dynamic based on toggle
                Button(action: {
                    // Handle subscription
                    isLoading = true

                    if let products = products {
                        // Choose product based on toggle
                        let productIdentifier = isFreeTrialEnabled ? "com.plutoai.chatbot.weekly" : "com.plutoai.chatbot.lifetime"
                        if let product = products.first(where: { $0.id == productIdentifier }) {
                            RevenueCatService.shared.purchase(product: product) { success, error in
                                isLoading = false
                                if success {
                                    dismiss()
                                } else if let error = error {
                                    errorMessage = error.localizedDescription
                                }
                            }
                        } else {
                            isLoading = false
                            errorMessage = "Could not find subscription product"
                        }
                    } else {
                        isLoading = false
                        errorMessage = "Could not find subscription products"
                    }
                }) {
                    HStack {
                        Text(isFreeTrialEnabled ? "Try For Free" : "Continue")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.white)

                        Spacer()

                        Image(systemName: "chevron.right")
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(.white)
                    }
                    .padding(.vertical, 18)
                    .padding(.horizontal, 20)
                    .background(Color(red: 0.2, green: 0.8, blue: 0.6))
                    .cornerRadius(14)
                }
                .padding(.horizontal, 20)
                .disabled(products == nil || isLoading)

                Spacer()
                    .frame(height: 20)

                // Secured with Apple
                HStack {
                    Image(systemName: "lock.fill")
                        .font(.system(size: 12))
                        .foregroundColor(.gray)

                    Text("Secured with Apple")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.gray)
                }
                .padding(.bottom, 20)

                // Footer with Privacy and Terms - matching exact design
                HStack {
                    Button(action: {
                        // Show privacy policy
                    }) {
                        Text("Privacy")
                            .font(.system(size: 14))
                            .foregroundColor(.gray)
                    }

                    Text("|")
                        .font(.system(size: 14))
                        .foregroundColor(.gray)

                    Button(action: {
                        // Show terms
                    }) {
                        Text("Terms")
                            .font(.system(size: 14))
                            .foregroundColor(.gray)
                    }

                    if !isFreeTrialEnabled {
                        Text("|")
                            .font(.system(size: 14))
                            .foregroundColor(.gray)

                        Button(action: {
                            // Show restore
                        }) {
                            Text("Restore")
                                .font(.system(size: 14))
                                .foregroundColor(.gray)
                        }
                    }
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 40)
            }

            // Error alert
            .alert(isPresented: Binding<Bool>(
                get: { errorMessage != nil },
                set: { if !$0 { errorMessage = nil } }
            )) {
                Alert(
                    title: Text("Error"),
                    message: Text(errorMessage ?? "An unknown error occurred"),
                    dismissButton: .default(Text("OK"))
                )
            }
        }
        .onAppear {
            // Load products when view appears
            isLoading = true
            RevenueCatService.shared.getOfferings { products in
                self.products = products
                isLoading = false
            }
        }
    }
}

#Preview {
    ProSubscriptionView()
        .preferredColorScheme(.dark)
}
