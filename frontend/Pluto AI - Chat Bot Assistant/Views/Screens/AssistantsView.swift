//
//  AssistantsView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.04.30.
//

import SwiftUI

struct AssistantsView: View {
    @Environment(\.presentationMode) var presentationMode
    @Environment(\.dismiss) private var dismiss
    @GestureState private var dragOffset = CGSize.zero
    @StateObject private var assistantManager = AssistantManager.shared

    var body: some View {
        ZStack(alignment: .top) {
            // Header with title and close button
            HStack {
                Button(action: {
                    // Dismiss
                    dismiss()
                }) {
                    Image(systemName: "xmark")
                        .font(.system(size: 20))
                        .foregroundColor(.white)
                        .padding()
                }

                Spacer()

                Text("Assistants")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)

                Spacer()

                But<PERSON>(action: {
                    // Show info
                }) {
                    Image(systemName: "info.circle")
                        .font(.system(size: 20))
                        .foregroundColor(.white)
                        .padding()
                }
            }
            .padding(.top, 8)
            .background(Color.black)
            .zIndex(1)

            // Main content
            ScrollView {
                VStack(spacing: 16) {
                    // Add padding to account for the header
                    Spacer()
                        .frame(height: 60)

                    LazyVGrid(columns: [
                        GridItem(.flexible(), spacing: 8),
                        GridItem(.flexible(), spacing: 8)
                    ], spacing: 12) {
                        CreateAssistantCardView()

                        ForEach(assistantManager.allAssistants) { assistant in
                            AssistantCardView(assistant: assistant)
                        }
                    }
                    .padding()
                }
            }
        }
        .background(Color.black.edgesIgnoringSafeArea(.all))
        .navigationBarHidden(true)
        // Enable swipe to dismiss
        .gesture(
            DragGesture().updating($dragOffset, body: { (value, state, transaction) in
                // Only allow right-to-left swipe
                if value.startLocation.x < 50 && value.translation.width > 0 {
                    state = value.translation
                }
            })
            .onEnded({ value in
                // Dismiss if swiped far enough
                if value.startLocation.x < 50 && value.translation.width > 100 {
                    dismiss()
                }
            })
        )
        // Apply the drag offset to move the view
        .offset(x: dragOffset.width > 0 ? dragOffset.width : 0)
    }
}

#Preview {
    NavigationView {
        AssistantsView()
    }
    .preferredColorScheme(.dark)
}
