//
//  TermsOfServiceView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.01.18.
//

import SwiftUI

struct TermsOfServiceView: View {
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // Header
                    VStack(alignment: .center, spacing: 10) {
                        Image(systemName: "doc.text.fill")
                            .font(.system(size: 60))
                            .foregroundColor(.blue)
                        
                        Text("Terms of Service")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .multilineTextAlignment(.center)
                        
                        Text("Please read these terms carefully")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding(.bottom, 20)
                    
                    // Terms Content
                    VStack(alignment: .leading, spacing: 20) {
                        TermsSection(
                            title: "Acceptance of Terms",
                            content: """
                            By using Pluto AI, you agree to these Terms of Service. If you do not agree to these terms, please do not use our service.
                            
                            These terms may be updated from time to time. Continued use of the app constitutes acceptance of any changes.
                            """
                        )
                        
                        TermsSection(
                            title: "Service Description",
                            content: """
                            Pluto AI is an AI-powered chatbot assistant that provides:
                            
                            • Conversational AI responses
                            • Image generation capabilities
                            • Specialized AI assistants
                            • Memory and personalization features
                            • Access to multiple AI models
                            
                            The service is provided "as is" and we strive to maintain high availability and quality.
                            """
                        )
                        
                        TermsSection(
                            title: "Subscription and Payments",
                            content: """
                            Pluto AI offers two subscription options:
                            
                            • Weekly subscription: $7.99/week
                            • Lifetime subscription: $39.99 (one-time payment)
                            
                            Payments are processed through Apple's App Store. Subscriptions are device-specific and non-transferable between devices.
                            """
                        )
                        
                        TermsSection(
                            title: "Acceptable Use",
                            content: """
                            You agree to use Pluto AI responsibly and not to:
                            
                            • Generate harmful, illegal, or inappropriate content
                            • Attempt to reverse engineer or hack the service
                            • Use the service for spam or malicious activities
                            • Violate any applicable laws or regulations
                            • Share or distribute inappropriate content
                            
                            We reserve the right to terminate access for violations of these terms.
                            """
                        )
                        
                        TermsSection(
                            title: "Intellectual Property",
                            content: """
                            • Pluto AI and its content are protected by intellectual property laws
                            • You retain ownership of content you create using our service
                            • AI-generated content may be subject to the terms of underlying AI providers
                            • You may not copy, modify, or distribute our proprietary technology
                            """
                        )
                        
                        TermsSection(
                            title: "Limitation of Liability",
                            content: """
                            Pluto AI is provided "as is" without warranties. We are not liable for:
                            
                            • Accuracy of AI-generated content
                            • Service interruptions or downtime
                            • Data loss or corruption
                            • Indirect or consequential damages
                            
                            Your use of the service is at your own risk.
                            """
                        )
                        
                        TermsSection(
                            title: "Contact Information",
                            content: """
                            For questions about these Terms of Service:
                            
                            Email: <EMAIL>
                            Website: oyu-intelligence.com
                            
                            Last updated: January 18, 2025
                            """
                        )
                    }
                }
                .padding()
            }
            .navigationTitle("Terms of Service")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(trailing: Button("Done") {
                presentationMode.wrappedValue.dismiss()
            })
        }
    }
}

struct TermsSection: View {
    let title: String
    let content: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text(title)
                .font(.title3)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            Text(content)
                .font(.body)
                .foregroundColor(.secondary)
                .fixedSize(horizontal: false, vertical: true)
        }
        .padding(.bottom, 10)
    }
}

#Preview {
    TermsOfServiceView()
}
