//
//  WhatsNewView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.01.18.
//

import SwiftUI

struct WhatsNewView: View {
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // Header
                    VStack(alignment: .center, spacing: 10) {
                        Image(systemName: "sparkles")
                            .font(.system(size: 60))
                            .foregroundColor(.blue)
                        
                        Text("What's New in Pluto AI")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .multilineTextAlignment(.center)
                        
                        Text("Discover the latest features and improvements")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding(.bottom, 20)
                    
                    // Version 1.0.0 Features
                    WhatsNewSection(
                        version: "Version 1.0.0",
                        date: "January 2025",
                        features: [
                            WhatsNewFeature(
                                icon: "brain.head.profile",
                                title: "AI Memory System",
                                description: "Pluto AI now remembers your preferences, goals, and important details for personalized conversations."
                            ),
                            WhatsNewFeature(
                                icon: "photo.artframe",
                                title: "AI Image Generation",
                                description: "Create stunning images with DALL-E 3 and GPT-Image-1. Edit existing images or generate from text prompts."
                            ),
                            WhatsNewFeature(
                                icon: "person.3.sequence.fill",
                                title: "20+ AI Assistants",
                                description: "Specialized assistants for writing, business, cooking, fitness, and more - each with unique expertise."
                            ),
                            WhatsNewFeature(
                                icon: "cpu",
                                title: "Multiple AI Models",
                                description: "Access GPT-4.1, Claude Sonnet 4, DeepSeek R1, Gemini 2.0 Flash, and more cutting-edge AI models."
                            ),
                            WhatsNewFeature(
                                icon: "bolt.fill",
                                title: "Real-time Streaming",
                                description: "Experience ChatGPT-level streaming performance with instant token display and sub-500ms response times."
                            ),
                            WhatsNewFeature(
                                icon: "iphone",
                                title: "Device-based Authentication",
                                description: "No account required! Each device gets independent access with secure device-based authentication."
                            )
                        ]
                    )
                    
                    // Coming Soon
                    WhatsNewSection(
                        version: "Coming Soon",
                        date: "Future Updates",
                        features: [
                            WhatsNewFeature(
                                icon: "mic.fill",
                                title: "Voice Chat",
                                description: "Talk to Pluto AI with natural voice conversations and speech-to-text capabilities."
                            ),
                            WhatsNewFeature(
                                icon: "doc.text.magnifyingglass",
                                title: "Document Analysis",
                                description: "Upload and analyze PDFs, documents, and files with AI-powered insights."
                            ),
                            WhatsNewFeature(
                                icon: "play.rectangle.fill",
                                title: "YouTube Summaries",
                                description: "Get instant summaries and insights from YouTube videos and online content."
                            )
                        ]
                    )
                }
                .padding()
            }
            .navigationTitle("What's New")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(trailing: Button("Done") {
                presentationMode.wrappedValue.dismiss()
            })
        }
    }
}

struct WhatsNewSection: View {
    let version: String
    let date: String
    let features: [WhatsNewFeature]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 15) {
            VStack(alignment: .leading, spacing: 5) {
                Text(version)
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text(date)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            VStack(spacing: 12) {
                ForEach(features, id: \.title) { feature in
                    WhatsNewFeatureRow(feature: feature)
                }
            }
        }
        .padding(.bottom, 30)
    }
}

struct WhatsNewFeatureRow: View {
    let feature: WhatsNewFeature
    
    var body: some View {
        HStack(alignment: .top, spacing: 15) {
            Image(systemName: feature.icon)
                .font(.title2)
                .foregroundColor(.blue)
                .frame(width: 30)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(feature.title)
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Text(feature.description)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .fixedSize(horizontal: false, vertical: true)
            }
            
            Spacer()
        }
        .padding(.vertical, 8)
    }
}

struct WhatsNewFeature {
    let icon: String
    let title: String
    let description: String
}

#Preview {
    WhatsNewView()
}
