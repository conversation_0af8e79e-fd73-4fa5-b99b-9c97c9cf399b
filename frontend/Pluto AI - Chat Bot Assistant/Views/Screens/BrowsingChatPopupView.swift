//
//  BrowsingChatPopupView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.05.01.
//

import SwiftUI

struct BrowsingChatPopupView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var questionText: String = ""
    @State private var showChatScreen = false

    var body: some View {
        ZStack {
            // Background color - semi-transparent black
            Color.black.opacity(0.9).edgesIgnoringSafeArea(.all)

            // Content
            VStack(spacing: 20) {
                // Handle indicator
                RoundedRectangle(cornerRadius: 2.5)
                    .fill(Color.gray.opacity(0.5))
                    .frame(width: 40, height: 5)
                    .padding(.top, 10)

                // Close button
                HStack {
                    Button(action: {
                        dismiss()
                    }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 18))
                            .foregroundColor(.white)
                    }
                    .padding(.leading, 10)

                    Spacer()

                    // Info button
                    Button(action: {
                        // Show info about browsing chat
                    }) {
                        Image(systemName: "info.circle")
                            .font(.system(size: 18))
                            .foregroundColor(.gray)
                    }
                    .padding(.trailing, 10)
                }
                .padding(.horizontal, 10)

                // Icon and title
                HStack {
                    Image(systemName: "globe")
                        .font(.system(size: 24))
                        .foregroundColor(Color(red: 0.6, green: 0.8, blue: 1.0))
                        .padding(4)

                    Text("Browsing Chat")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                }
                .padding(.top, 10)

                // Description
                Text("You can ask anything. Ask AI to search the web and prepare an answer for you.")
                    .font(.subheadline)
                    .foregroundColor(.gray)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
                    .padding(.top, -5)

                // Question input field
                ZStack(alignment: .topLeading) {
                    TextEditor(text: $questionText)
                        .padding()
                        .background(Color(UIColor.systemGray6).opacity(0.3))
                        .cornerRadius(12)
                        .foregroundColor(.white)
                        .frame(height: 150)
                        .scrollContentBackground(.hidden)

                    if questionText.isEmpty {
                        Text("Type your question here")
                            .foregroundColor(.gray)
                            .padding(.horizontal, 20)
                            .padding(.vertical, 16)
                    }
                }
                .padding(.horizontal, 20)
                .padding(.top, 10)

                Spacer()

                // Continue button
                Button(action: {
                    // Proceed to chat screen with the question
                    showChatScreen = true
                }) {
                    Text("Continue")
                        .font(.headline)
                        .foregroundColor(.black)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.white)
                        .cornerRadius(12)
                        .padding(.horizontal, 20)
                }
                .padding(.bottom, 30)
                .disabled(questionText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                .opacity(questionText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? 0.5 : 1.0)
            }
            .padding(.horizontal)
        }
        .fullScreenCover(isPresented: $showChatScreen) {
            NavigationView {
                // Create a suggestion for browsing chat
                let browsingSuggestion = Suggestion(
                    text: questionText,
                    icon: "globe",
                    category: "Browsing Chat",
                    examplePrompts: [
                        "Search for current information online",
                        "Find the latest news and updates",
                        "Browse the web for relevant content"
                    ]
                )

                // Pass the suggestion to ChatScreenView with autoSend set to true
                ChatScreenView(suggestion: browsingSuggestion, autoSend: true, showExamplesOnAppear: false, assistant: nil)
            }
        }
    }
}

#Preview {
    BrowsingChatPopupView()
        .preferredColorScheme(.dark)
}
