//
//  ContentView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025.04.30.
//  Updated by Augment on 2025.04.30.
//

import SwiftUI
import CoreData

struct ContentView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.colorScheme) private var colorScheme
    @State private var isInitialized = false

    var body: some View {
        Group {
            if isInitialized {
                // Main app interface
                NavigationView {
                    MainHomeView()
                }
                .navigationViewStyle(StackNavigationViewStyle())
                .preferredColorScheme(.dark)
                .environment(\.managedObjectContext, viewContext)
            } else {
                // Loading screen while initializing device
                ZStack {
                    Color.black.ignoresSafeArea()
                    VStack(spacing: 20) {
                        Image(systemName: "brain.head.profile")
                            .font(.system(size: 60))
                            .foregroundColor(.blue)

                        Text("Pluto AI")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .foregroundColor(.white)

                        Text("Initializing...")
                            .font(.headline)
                            .foregroundColor(.gray)

                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .blue))
                            .scaleEffect(1.2)
                    }
                }
            }
        }
        .onAppear {
            print("🔥 ContentView appeared - starting device initialization")
            NSLog("🔥 ContentView appeared - starting device initialization")
            initializeDevice()
        }
    }

    private func initializeDevice() {
        print("🔥 initializeDevice() called")
        NSLog("🔥 initializeDevice() called")
        Task {
            await DeviceService.shared.initializeDevice()
            await MainActor.run {
                print("🔥 Device initialization complete - setting isInitialized = true")
                NSLog("🔥 Device initialization complete - setting isInitialized = true")
                isInitialized = true
            }
        }
    }
}

#Preview {
    ContentView().environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
