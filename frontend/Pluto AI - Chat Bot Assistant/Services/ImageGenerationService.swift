//
//  ImageGenerationService.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.05.01.
//

import Foundation
import UIKit

class ImageGenerationService {
    // Singleton instance
    static let shared = ImageGenerationService()

    private init() {}

    // MARK: - API Methods

    func generateImage(prompt: String, model: String = "dall-e-3", size: String = "1024x1024", quality: String = "standard", completion: @escaping (Result<URL, Error>) -> Void) {
        // Get the API URL
        guard let url = URL(string: "\(APIConfig.backendBaseURL)/api/v1/image/generate") else {
            completion(.failure(ImageGenerationError.invalidURL))
            return
        }

        // Create request
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")

        // Add device-based authentication headers
        let deviceId = DeviceService.shared.deviceId
        let userId = DeviceService.shared.userId
        request.addValue(deviceId, forHTTPHeaderField: "X-Device-ID")
        request.addValue(userId, forHT<PERSON>HeaderField: "X-User-ID")

        // Create base request body
        var body: [String: Any] = [
            "prompt": prompt,
            "model": model,
            "size": size,
            "n": 1
        ]

        // Add quality parameter for DALL-E models
        if model.hasPrefix("dall-e") {
            body["quality"] = quality
        }

        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: body)
        } catch {
            completion(.failure(ImageGenerationError.requestFailed(error)))
            return
        }

        // Send request
        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                completion(.failure(ImageGenerationError.requestFailed(error)))
                return
            }

            guard let httpResponse = response as? HTTPURLResponse,
                  (200...299).contains(httpResponse.statusCode) else {
                completion(.failure(ImageGenerationError.invalidResponse))
                return
            }

            guard let data = data else {
                completion(.failure(ImageGenerationError.noData))
                return
            }

            do {
                // Parse the response
                let json = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any]

                if let imageData = json?["data"] as? [[String: Any]],
                   let firstImage = imageData.first,
                   let urlString = firstImage["url"] as? String,
                   let imageURL = URL(string: urlString) {
                    completion(.success(imageURL))
                } else {
                    completion(.failure(ImageGenerationError.invalidData))
                }
            } catch {
                completion(.failure(ImageGenerationError.decodingFailed(error)))
            }
        }.resume()
    }

    // Generate image using async/await
    func generateImage(prompt: String, model: String = "dall-e-3", size: String = "1024x1024", quality: String = "standard") async throws -> URL {
        return try await withCheckedThrowingContinuation { continuation in
            generateImage(prompt: prompt, model: model, size: size, quality: quality) { result in
                continuation.resume(with: result)
            }
        }
    }

    // MARK: - Image Editing Methods

    func editImage(image: UIImage, prompt: String, model: String = "dall-e-2", size: String = "1024x1024", completion: @escaping (Result<URL, Error>) -> Void) {
        // Get the API URL for image editing
        guard let url = URL(string: "\(APIConfig.backendBaseURL)/api/v1/image/edit") else {
            completion(.failure(ImageGenerationError.invalidURL))
            return
        }

        // Create multipart form data request
        var request = URLRequest(url: url)
        request.httpMethod = "POST"

        // Add device-based authentication headers
        let deviceId = DeviceService.shared.deviceId
        let userId = DeviceService.shared.userId
        request.addValue(deviceId, forHTTPHeaderField: "X-Device-ID")
        request.addValue(userId, forHTTPHeaderField: "X-User-ID")

        // Create multipart form data
        let boundary = UUID().uuidString
        request.addValue("multipart/form-data; boundary=\(boundary)", forHTTPHeaderField: "Content-Type")

        var body = Data()

        // Add image data
        if let imageData = image.jpegData(compressionQuality: 0.8) {
            body.append("--\(boundary)\r\n".data(using: .utf8)!)
            body.append("Content-Disposition: form-data; name=\"image\"; filename=\"image.jpg\"\r\n".data(using: .utf8)!)
            body.append("Content-Type: image/jpeg\r\n\r\n".data(using: .utf8)!)
            body.append(imageData)
            body.append("\r\n".data(using: .utf8)!)
        }

        // Add prompt
        body.append("--\(boundary)\r\n".data(using: .utf8)!)
        body.append("Content-Disposition: form-data; name=\"prompt\"\r\n\r\n".data(using: .utf8)!)
        body.append(prompt.data(using: .utf8)!)
        body.append("\r\n".data(using: .utf8)!)

        // Add model
        body.append("--\(boundary)\r\n".data(using: .utf8)!)
        body.append("Content-Disposition: form-data; name=\"model\"\r\n\r\n".data(using: .utf8)!)
        body.append(model.data(using: .utf8)!)
        body.append("\r\n".data(using: .utf8)!)

        // Add size
        body.append("--\(boundary)\r\n".data(using: .utf8)!)
        body.append("Content-Disposition: form-data; name=\"size\"\r\n\r\n".data(using: .utf8)!)
        body.append(size.data(using: .utf8)!)
        body.append("\r\n".data(using: .utf8)!)

        // Add n (number of images)
        body.append("--\(boundary)\r\n".data(using: .utf8)!)
        body.append("Content-Disposition: form-data; name=\"n\"\r\n\r\n".data(using: .utf8)!)
        body.append("1".data(using: .utf8)!)
        body.append("\r\n".data(using: .utf8)!)

        // Close boundary
        body.append("--\(boundary)--\r\n".data(using: .utf8)!)

        request.httpBody = body

        // Send request
        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                completion(.failure(ImageGenerationError.requestFailed(error)))
                return
            }

            guard let httpResponse = response as? HTTPURLResponse,
                  (200...299).contains(httpResponse.statusCode) else {
                completion(.failure(ImageGenerationError.invalidResponse))
                return
            }

            guard let data = data else {
                completion(.failure(ImageGenerationError.noData))
                return
            }

            do {
                // Parse the response
                let json = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any]

                if let imageData = json?["data"] as? [[String: Any]],
                   let firstImage = imageData.first,
                   let urlString = firstImage["url"] as? String,
                   let imageURL = URL(string: urlString) {
                    completion(.success(imageURL))
                } else {
                    completion(.failure(ImageGenerationError.invalidData))
                }
            } catch {
                completion(.failure(ImageGenerationError.decodingFailed(error)))
            }
        }.resume()
    }

    // Edit image using async/await
    func editImage(image: UIImage, prompt: String, model: String = "dall-e-2", size: String = "1024x1024") async throws -> URL {
        return try await withCheckedThrowingContinuation { continuation in
            editImage(image: image, prompt: prompt, model: model, size: size) { result in
                continuation.resume(with: result)
            }
        }
    }
}

// Error types
enum ImageGenerationError: Error {
    case invalidURL
    case requestFailed(Error)
    case invalidResponse
    case noData
    case invalidData
    case decodingFailed(Error)
}
