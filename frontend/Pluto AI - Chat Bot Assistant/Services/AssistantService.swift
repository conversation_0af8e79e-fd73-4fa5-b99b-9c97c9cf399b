//
//  AssistantService.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.05.01.
//

import Foundation
import Combine
import SwiftUI

class AssistantService {
    // Singleton instance
    static let shared = AssistantService()

    // Publishers
    private let assistantsSubject = CurrentValueSubject<[Assistant], Never>([])
    var assistants: AnyPublisher<[Assistant], Never> {
        return assistantsSubject.eraseToAnyPublisher()
    }

    private let toolsSubject = CurrentValueSubject<[Tool], Never>([])
    var tools: AnyPublisher<[Tool], Never> {
        return toolsSubject.eraseToAnyPublisher()
    }

    private let suggestionsSubject = CurrentValueSubject<[String: [Suggestion]], Never>([:])
    var suggestions: AnyPublisher<[String: [Suggestion]], Never> {
        return suggestionsSubject.eraseToAnyPublisher()
    }

    private let suggestionCategoriesSubject = CurrentValueSubject<[SuggestionCategory], Never>([])
    var suggestionCategories: AnyPublisher<[SuggestionCategory], Never> {
        return suggestionCategoriesSubject.eraseToAnyPublisher()
    }

    private init() {
        // Load initial data from local cache
        assistantsSubject.send(Assistant.assistants)
        toolsSubject.send(Tool.eliteTools)

        // Load suggestion categories and suggestions
        let categories = SuggestionCategory.categories
        suggestionCategoriesSubject.send(categories)

        var allSuggestions: [String: [Suggestion]] = [:]
        allSuggestions["E-Mail"] = Suggestion.emailSuggestions
        allSuggestions["Business & Marketing"] = Suggestion.businessSuggestions
        allSuggestions["Astrology"] = Suggestion.astrologySuggestions
        allSuggestions["Education"] = Suggestion.educationSuggestions
        allSuggestions["Art"] = Suggestion.artSuggestions
        allSuggestions["Travel"] = Suggestion.travelSuggestions
        allSuggestions["Daily Lifestyle"] = Suggestion.lifestyleSuggestions
        allSuggestions["Relationship"] = Suggestion.relationshipSuggestions
        allSuggestions["Fun"] = Suggestion.funSuggestions
        allSuggestions["Social"] = Suggestion.socialSuggestions
        allSuggestions["Career"] = Suggestion.careerSuggestions
        allSuggestions["Health & Nutrition"] = Suggestion.healthSuggestions
        allSuggestions["Greetings"] = Suggestion.greetingsSuggestions

        suggestionsSubject.send(allSuggestions)

        // Fetch data from backend
        Task {
            await fetchAssistants()
            await fetchTools()
            await fetchSuggestionCategories()
            await fetchAllSuggestions()
        }
    }

    // MARK: - API Methods

    func fetchAssistants() async {
        guard let url = URL(string: "\(APIConfig.backendBaseURL)/api/v1/assistant/assistants") else {
            print("Invalid URL for assistants")
            return
        }

        do {
            // Create request with authentication headers
            var request = URLRequest(url: url)
            request.addValue(DeviceService.shared.deviceId, forHTTPHeaderField: "X-Device-ID")
            request.addValue(DeviceService.shared.userId, forHTTPHeaderField: "X-User-ID")

            let (data, response) = try await URLSession.shared.data(for: request)

            guard let httpResponse = response as? HTTPURLResponse, (200...299).contains(httpResponse.statusCode) else {
                print("Invalid response from server")
                return
            }

            // Parse the response
            struct AssistantResponse: Decodable {
                struct ColorData: Decodable {
                    let red: Double
                    let green: Double
                    let blue: Double
                    let opacity: Double
                }

                struct SuggestedPromptData: Decodable {
                    let text: String
                    let icon: String
                    let description: String?
                }

                struct AssistantData: Decodable {
                    let id: String
                    let name: String
                    let description: String
                    let icon_name: String
                    let background_color: ColorData
                    let system_message: String
                    let suggested_prompts: [SuggestedPromptData]?
                }

                let assistants: [AssistantData]
            }

            let decodedResponse = try JSONDecoder().decode(AssistantResponse.self, from: data)

            // Convert to Assistant model
            let assistants = decodedResponse.assistants.map { data in
                let suggestedPrompts = data.suggested_prompts?.map { promptData in
                    SuggestedPrompt(
                        text: promptData.text,
                        icon: promptData.icon,
                        description: promptData.description
                    )
                }

                return Assistant(
                    name: data.name,
                    description: data.description,
                    iconName: data.icon_name,
                    backgroundColor: Color(
                        red: data.background_color.red,
                        green: data.background_color.green,
                        blue: data.background_color.blue,
                        opacity: data.background_color.opacity
                    ),
                    suggestedPrompts: suggestedPrompts
                )
            }

            // Update the subject
            DispatchQueue.main.async {
                self.assistantsSubject.send(assistants)
            }
        } catch {
            print("Error fetching assistants: \(error.localizedDescription)")
        }
    }

    func fetchTools() async {
        guard let url = URL(string: "\(APIConfig.backendBaseURL)/api/v1/assistant/tools") else {
            print("Invalid URL for tools")
            return
        }

        do {
            // Create request with authentication headers
            var request = URLRequest(url: url)
            request.addValue(DeviceService.shared.deviceId, forHTTPHeaderField: "X-Device-ID")
            request.addValue(DeviceService.shared.userId, forHTTPHeaderField: "X-User-ID")

            let (data, response) = try await URLSession.shared.data(for: request)

            guard let httpResponse = response as? HTTPURLResponse, (200...299).contains(httpResponse.statusCode) else {
                print("Invalid response from server")
                return
            }

            // Parse the response
            struct ToolResponse: Decodable {
                struct ColorData: Decodable {
                    let red: Double
                    let green: Double
                    let blue: Double
                    let opacity: Double
                }

                struct ToolData: Decodable {
                    let id: String
                    let name: String
                    let description: String
                    let icon: String
                    let is_new: Bool
                    let icon_background_color: ColorData
                    let system_message: String
                }

                let tools: [ToolData]
            }

            let decodedResponse = try JSONDecoder().decode(ToolResponse.self, from: data)

            // Convert to Tool model
            let tools = decodedResponse.tools.map { data in
                Tool(
                    name: data.name,
                    description: data.description,
                    icon: data.icon,
                    assetImageName: nil, // Backend tools don't have asset images
                    isNew: data.is_new,
                    iconBackgroundColor: Color(
                        red: data.icon_background_color.red,
                        green: data.icon_background_color.green,
                        blue: data.icon_background_color.blue,
                        opacity: data.icon_background_color.opacity
                    )
                )
            }

            // Update the subject
            DispatchQueue.main.async {
                self.toolsSubject.send(tools)
            }
        } catch {
            print("Error fetching tools: \(error.localizedDescription)")
        }
    }

    func fetchSuggestionCategories() async {
        guard let url = URL(string: "\(APIConfig.backendBaseURL)/api/v1/assistant/suggestion-categories") else {
            print("Invalid URL for suggestion categories")
            return
        }

        do {
            // Create request with authentication headers
            var request = URLRequest(url: url)
            request.addValue(DeviceService.shared.deviceId, forHTTPHeaderField: "X-Device-ID")
            request.addValue(DeviceService.shared.userId, forHTTPHeaderField: "X-User-ID")

            let (data, response) = try await URLSession.shared.data(for: request)

            guard let httpResponse = response as? HTTPURLResponse, (200...299).contains(httpResponse.statusCode) else {
                print("Invalid response from server")
                return
            }

            // Parse the response
            struct CategoryResponse: Decodable {
                struct ColorData: Decodable {
                    let red: Double
                    let green: Double
                    let blue: Double
                    let opacity: Double
                }

                struct CategoryData: Decodable {
                    let id: String
                    let name: String
                    let icon: String
                    let color: ColorData
                    let system_message: String
                }

                let categories: [CategoryData]
            }

            let decodedResponse = try JSONDecoder().decode(CategoryResponse.self, from: data)

            // Convert to SuggestionCategory model
            let categories = decodedResponse.categories.map { data in
                SuggestionCategory(
                    name: data.name,
                    icon: data.icon,
                    color: Color(
                        red: data.color.red,
                        green: data.color.green,
                        blue: data.color.blue,
                        opacity: data.color.opacity
                    ),
                    systemMessage: data.system_message,
                    description: "AI-powered assistance for \(data.name.lowercased()) tasks and queries."
                )
            }

            // Update the subject
            DispatchQueue.main.async {
                self.suggestionCategoriesSubject.send(categories)
            }
        } catch {
            print("Error fetching suggestion categories: \(error.localizedDescription)")
        }
    }

    func fetchSuggestions(for category: String) async -> [Suggestion] {
        guard let encodedCategory = category.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed),
              let url = URL(string: "\(APIConfig.backendBaseURL)/api/v1/assistant/suggestions?category=\(encodedCategory)") else {
            print("Invalid URL for suggestions")
            return []
        }

        do {
            // Create request with authentication headers
            var request = URLRequest(url: url)
            request.addValue(DeviceService.shared.deviceId, forHTTPHeaderField: "X-Device-ID")
            request.addValue(DeviceService.shared.userId, forHTTPHeaderField: "X-User-ID")

            let (data, response) = try await URLSession.shared.data(for: request)

            guard let httpResponse = response as? HTTPURLResponse, (200...299).contains(httpResponse.statusCode) else {
                print("Invalid response from server")
                return []
            }

            // Parse the response
            struct SuggestionResponse: Decodable {
                struct SuggestionData: Decodable {
                    let id: String
                    let text: String
                    let icon: String
                    let category: String
                    let system_message: String?
                }

                let suggestions: [SuggestionData]
            }

            let decodedResponse = try JSONDecoder().decode(SuggestionResponse.self, from: data)

            // Convert to Suggestion model
            return decodedResponse.suggestions.map { data in
                Suggestion(
                    text: data.text,
                    icon: data.icon,
                    category: data.category,
                    examplePrompts: [
                        "Ask me anything about \(data.category)",
                        "How can I learn more about \(data.category)?",
                        "What are the best resources for \(data.category)?"
                    ]
                )
            }
        } catch {
            print("Error fetching suggestions: \(error.localizedDescription)")
            return []
        }
    }

    func fetchAllSuggestions() async {
        var allSuggestions: [String: [Suggestion]] = [:]

        for category in suggestionCategoriesSubject.value {
            let suggestions = await fetchSuggestions(for: category.name)
            allSuggestions[category.name] = suggestions
        }

        // Update the subject
        DispatchQueue.main.async {
            self.suggestionsSubject.send(allSuggestions)
        }
    }

    func getSystemMessage(for assistantName: String) async -> String {
        guard let url = URL(string: "\(APIConfig.backendBaseURL)/api/v1/assistant/system-message?assistant_name=\(assistantName.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? "")") else {
            return "You are a helpful, creative, and friendly AI assistant named Pluto AI."
        }

        do {
            // Create request with authentication headers
            var request = URLRequest(url: url)
            request.addValue(DeviceService.shared.deviceId, forHTTPHeaderField: "X-Device-ID")
            request.addValue(DeviceService.shared.userId, forHTTPHeaderField: "X-User-ID")

            let (data, response) = try await URLSession.shared.data(for: request)

            guard let httpResponse = response as? HTTPURLResponse, (200...299).contains(httpResponse.statusCode) else {
                return "You are a helpful, creative, and friendly AI assistant named Pluto AI."
            }

            // Parse the response
            struct SystemMessageResponse: Decodable {
                let system_message: String
            }

            let decodedResponse = try JSONDecoder().decode(SystemMessageResponse.self, from: data)
            return decodedResponse.system_message
        } catch {
            print("Error fetching system message: \(error.localizedDescription)")
            return "You are a helpful, creative, and friendly AI assistant named Pluto AI."
        }
    }

    func fetchSuggestedPrompts(for assistantName: String) async -> [SuggestedPrompt] {
        guard let encodedName = assistantName.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed),
              let url = URL(string: "\(APIConfig.backendBaseURL)/api/v1/assistant/assistants/\(encodedName)/prompts") else {
            print("Invalid URL for assistant prompts")
            return []
        }

        do {
            // Create request with authentication headers
            var request = URLRequest(url: url)
            request.addValue(DeviceService.shared.deviceId, forHTTPHeaderField: "X-Device-ID")
            request.addValue(DeviceService.shared.userId, forHTTPHeaderField: "X-User-ID")

            let (data, response) = try await URLSession.shared.data(for: request)

            guard let httpResponse = response as? HTTPURLResponse, (200...299).contains(httpResponse.statusCode) else {
                print("Invalid response from server")
                return []
            }

            // Parse the response
            struct PromptResponse: Decodable {
                struct SuggestedPromptData: Decodable {
                    let text: String
                    let icon: String
                    let description: String?
                }

                let assistant_name: String
                let suggested_prompts: [SuggestedPromptData]
            }

            let decodedResponse = try JSONDecoder().decode(PromptResponse.self, from: data)

            // Convert to SuggestedPrompt model
            let prompts = decodedResponse.suggested_prompts.map { data in
                SuggestedPrompt(
                    text: data.text,
                    icon: data.icon,
                    description: data.description
                )
            }

            return prompts
        } catch {
            print("Error fetching suggested prompts: \(error.localizedDescription)")
            return []
        }
    }
}
