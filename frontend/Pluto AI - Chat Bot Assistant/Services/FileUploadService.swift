//
//  FileUploadService.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.06.18.
//

import Foundation
import SwiftUI
import UniformTypeIdentifiers

// Supported file types
enum SupportedFileType: String, CaseIterable {
    case pdf = "pdf"
    case txt = "txt"
    case doc = "doc"
    case docx = "docx"
    case rtf = "rtf"
    case md = "md"
    case csv = "csv"
    case json = "json"
    case xml = "xml"
    
    var displayName: String {
        switch self {
        case .pdf: return "PDF Document"
        case .txt: return "Text File"
        case .doc: return "Word Document (Legacy)"
        case .docx: return "Word Document"
        case .rtf: return "Rich Text Format"
        case .md: return "Markdown File"
        case .csv: return "CSV File"
        case .json: return "JSON File"
        case .xml: return "XML File"
        }
    }
    
    var utType: UTType {
        switch self {
        case .pdf: return .pdf
        case .txt: return .plainText
        case .doc: return UTType("com.microsoft.word.doc") ?? .data
        case .docx: return UTType("org.openxmlformats.wordprocessingml.document") ?? .data
        case .rtf: return .rtf
        case .md: return UTType("net.daringfireball.markdown") ?? .plainText
        case .csv: return UTType("public.comma-separated-values-text") ?? .plainText
        case .json: return .json
        case .xml: return .xml
        }
    }
}

// File information
struct UploadedFile: Identifiable {
    let id = UUID()
    let fileName: String
    let fileSize: Int64
    let fileType: SupportedFileType
    let mimeType: String
    let creationDate: Date?
    let modificationDate: Date?
    let url: URL
}

// File processing result
struct FileProcessingResult {
    let file: UploadedFile
    let extractedText: String
    let summary: String
    let keyPoints: [String]
    let topics: [String]
    let wordCount: Int
    let processingTime: TimeInterval
}

// File service errors
enum FileServiceError: LocalizedError {
    case fileNotFound
    case unsupportedFileType
    case fileTooLarge
    case textExtractionFailed
    case processingFailed(String)
    case permissionDenied
    
    var errorDescription: String? {
        switch self {
        case .fileNotFound:
            return "File not found"
        case .unsupportedFileType:
            return "Unsupported file type"
        case .fileTooLarge:
            return "File is too large to process"
        case .textExtractionFailed:
            return "Failed to extract text from file"
        case .processingFailed(let message):
            return "Processing failed: \(message)"
        case .permissionDenied:
            return "Permission denied to access file"
        }
    }
}

class FileUploadService: ObservableObject {
    // Singleton instance
    static let shared = FileUploadService()
    
    // Published properties for UI updates
    @Published var isProcessing = false
    @Published var processingProgress: Double = 0.0
    @Published var currentStep = ""
    @Published var lastResult: FileProcessingResult?
    @Published var errorMessage: String?
    @Published var uploadedFiles: [UploadedFile] = []
    
    // Services
    private let chatService = ChatService.shared
    private let pdfService = PDFProcessingService.shared
    
    // Configuration
    private let maxFileSize: Int64 = 50 * 1024 * 1024 // 50MB
    private let maxTextLength = 100000 // Limit text length for processing
    
    private init() {}
    
    // MARK: - Public Methods
    
    func processFile(at url: URL, model: AIModel = AIModel.models.first!) async throws -> FileProcessingResult {
        let startTime = CFAbsoluteTimeGetCurrent()
        
        DispatchQueue.main.async {
            self.isProcessing = true
            self.processingProgress = 0.0
            self.currentStep = "Analyzing file..."
            self.errorMessage = nil
        }
        
        do {
            // Step 1: Validate and analyze file
            let fileInfo = try analyzeFile(at: url)
            
            DispatchQueue.main.async {
                self.processingProgress = 0.2
                self.currentStep = "Extracting content..."
            }
            
            // Step 2: Extract text based on file type
            let extractedText = try await extractTextFromFile(fileInfo)
            
            DispatchQueue.main.async {
                self.processingProgress = 0.5
                self.currentStep = "Generating summary with AI..."
            }
            
            // Step 3: Generate summary
            let summary = try await generateSummary(text: extractedText, fileInfo: fileInfo, model: model)
            
            DispatchQueue.main.async {
                self.processingProgress = 0.7
                self.currentStep = "Extracting key points..."
            }
            
            // Step 4: Extract key points
            let keyPoints = try await extractKeyPoints(text: extractedText, model: model)
            
            DispatchQueue.main.async {
                self.processingProgress = 0.9
                self.currentStep = "Identifying topics..."
            }
            
            // Step 5: Extract topics
            let topics = try await extractTopics(text: extractedText, model: model)
            
            let processingTime = CFAbsoluteTimeGetCurrent() - startTime
            let wordCount = extractedText.components(separatedBy: .whitespacesAndNewlines).filter { !$0.isEmpty }.count
            
            let result = FileProcessingResult(
                file: fileInfo,
                extractedText: extractedText,
                summary: summary,
                keyPoints: keyPoints,
                topics: topics,
                wordCount: wordCount,
                processingTime: processingTime
            )
            
            DispatchQueue.main.async {
                self.processingProgress = 1.0
                self.currentStep = "Complete!"
                self.lastResult = result
                self.uploadedFiles.append(fileInfo)
                self.isProcessing = false
            }
            
            return result
            
        } catch {
            DispatchQueue.main.async {
                self.isProcessing = false
                self.errorMessage = error.localizedDescription
            }
            throw error
        }
    }
    
    func askQuestionAboutFile(_ question: String, fileResult: FileProcessingResult, model: AIModel = AIModel.models.first!) async throws -> String {
        let prompt = """
        Based on the following file content, please answer this question:
        
        Question: \(question)
        
        File: \(fileResult.file.fileName)
        Type: \(fileResult.file.fileType.displayName)
        Content:
        \(fileResult.extractedText.prefix(maxTextLength))
        
        Please provide a detailed answer based on the file content.
        """
        
        let messages = [
            Message(content: prompt, isUser: true, timestamp: Date())
        ]
        
        return try await chatService.sendChatRequest(
            messages: messages,
            model: model,
            systemMessage: "You are a helpful assistant that answers questions based on file content. Provide accurate answers based only on the information in the file."
        )
    }
    
    func getSupportedFileTypes() -> [UTType] {
        return SupportedFileType.allCases.map { $0.utType }
    }
    
    func clearUploadedFiles() {
        uploadedFiles.removeAll()
    }
    
    // MARK: - Private Methods
    
    private func analyzeFile(at url: URL) throws -> UploadedFile {
        // Check if file exists and is accessible
        guard url.startAccessingSecurityScopedResource() else {
            throw FileServiceError.permissionDenied
        }
        defer { url.stopAccessingSecurityScopedResource() }
        
        guard FileManager.default.fileExists(atPath: url.path) else {
            throw FileServiceError.fileNotFound
        }
        
        // Get file attributes
        let attributes = try FileManager.default.attributesOfItem(atPath: url.path)
        let fileSize = attributes[.size] as? Int64 ?? 0
        let creationDate = attributes[.creationDate] as? Date
        let modificationDate = attributes[.modificationDate] as? Date
        
        // Check file size
        guard fileSize <= maxFileSize else {
            throw FileServiceError.fileTooLarge
        }
        
        // Determine file type
        let fileName = url.lastPathComponent
        let fileExtension = url.pathExtension.lowercased()
        
        guard let fileType = SupportedFileType(rawValue: fileExtension) else {
            throw FileServiceError.unsupportedFileType
        }
        
        // Get MIME type
        let mimeType = getMimeType(for: fileType)
        
        return UploadedFile(
            fileName: fileName,
            fileSize: fileSize,
            fileType: fileType,
            mimeType: mimeType,
            creationDate: creationDate,
            modificationDate: modificationDate,
            url: url
        )
    }
    
    private func extractTextFromFile(_ fileInfo: UploadedFile) async throws -> String {
        switch fileInfo.fileType {
        case .pdf:
            // Use PDF service for PDF files
            let pdfResult = try await pdfService.processPDFFile(at: fileInfo.url)
            return pdfResult.extractedText
            
        case .txt, .md, .csv, .json, .xml, .rtf:
            // Read text files directly
            return try String(contentsOf: fileInfo.url, encoding: .utf8)
            
        case .doc, .docx:
            // For Word documents, we'll need to implement extraction
            // For now, return a placeholder
            return "Word document text extraction not yet implemented. Please convert to PDF or text format."
        }
    }
    
    private func getMimeType(for fileType: SupportedFileType) -> String {
        switch fileType {
        case .pdf: return "application/pdf"
        case .txt: return "text/plain"
        case .doc: return "application/msword"
        case .docx: return "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        case .rtf: return "application/rtf"
        case .md: return "text/markdown"
        case .csv: return "text/csv"
        case .json: return "application/json"
        case .xml: return "application/xml"
        }
    }
    
    private func generateSummary(text: String, fileInfo: UploadedFile, model: AIModel) async throws -> String {
        let prompt = """
        Please provide a comprehensive summary of this file.
        
        File: \(fileInfo.fileName)
        Type: \(fileInfo.fileType.displayName)
        Size: \(ByteCountFormatter.string(fromByteCount: fileInfo.fileSize, countStyle: .file))
        
        Content:
        \(text)
        
        Please provide:
        1. A brief overview of the file
        2. Main topics covered
        3. Key information or insights
        4. Important details
        
        Keep the summary comprehensive but well-organized.
        """
        
        let messages = [
            Message(content: prompt, isUser: true, timestamp: Date())
        ]
        
        return try await chatService.sendChatRequest(
            messages: messages,
            model: model,
            systemMessage: "You are a helpful assistant that creates comprehensive summaries of file content. Focus on the most important information and organize it clearly."
        )
    }
    
    private func extractKeyPoints(text: String, model: AIModel) async throws -> [String] {
        let prompt = """
        Extract the key points from this file content. Return them as a numbered list.
        
        Content:
        \(text)
        
        Please identify the most important points, insights, or takeaways from this file.
        """
        
        let messages = [
            Message(content: prompt, isUser: true, timestamp: Date())
        ]
        
        let response = try await chatService.sendChatRequest(
            messages: messages,
            model: model,
            systemMessage: "You are a helpful assistant that extracts key points from file content. Return only the key points as a numbered list."
        )
        
        return parseListFromResponse(response)
    }
    
    private func extractTopics(text: String, model: AIModel) async throws -> [String] {
        let prompt = """
        Identify the main topics covered in this file. Return them as a simple list.
        
        Content:
        \(text)
        
        Please identify the main subjects, themes, or topics discussed in this file.
        """
        
        let messages = [
            Message(content: prompt, isUser: true, timestamp: Date())
        ]
        
        let response = try await chatService.sendChatRequest(
            messages: messages,
            model: model,
            systemMessage: "You are a helpful assistant that identifies topics from file content. Return only the topics as a simple list."
        )
        
        return parseListFromResponse(response)
    }
    
    private func parseListFromResponse(_ response: String) -> [String] {
        let lines = response.components(separatedBy: .newlines)
        var items: [String] = []
        
        for line in lines {
            let trimmed = line.trimmingCharacters(in: .whitespacesAndNewlines)
            if !trimmed.isEmpty {
                // Remove numbering, bullets, or dashes
                let cleaned = trimmed.replacingOccurrences(of: "^[0-9]+\\.\\s*", with: "", options: .regularExpression)
                    .replacingOccurrences(of: "^[-•*]\\s*", with: "", options: .regularExpression)
                    .trimmingCharacters(in: .whitespacesAndNewlines)
                
                if !cleaned.isEmpty {
                    items.append(cleaned)
                }
            }
        }
        
        return items
    }
}
