//
//  APIConfig.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.04.30.
//

import Foundation

struct APIConfig {
    // Backend API - Using production Fly.io deployment
    static let backendBaseURL = "https://pluto-ai-chatbot.fly.dev" // Production URL

    // For local development:
    // static let backendBaseURL = "http://************:8000" // Development URL - Computer's IP address

    // Note: Supabase removed - using device-based authentication with RevenueCat

    // Get model ID from display name (now uses backend)
    static func getModelID(for modelName: String) async -> String {
        return await AIModelService.shared.getModelID(for: modelName)
    }

    // Synchronous version for backward compatibility
    static func getModelID(for modelName: String) -> String {
        // Default mapping for when async call isn't possible
        let defaultModelIDs: [String: String] = [
            // OpenAI Models
            "GPT-4.1 nano": "gpt-4.1-nano-2025-04-14",
            "GPT-4.1 mini": "gpt-4.1-mini-2025-04-14",
            "GPT-4.1": "gpt-4.1-2025-04-14",
            "o4 - Mini": "gpt-4o-mini",
            "GPT-4o": "gpt-4o",
            "GPT-4o mini": "gpt-4o-mini",
            "o3": "o3-mini",

            // DeepSeek Models
            "DeepSeek R1": "deepseek-chat",
            "DeepSeek V3": "deepseek-chat",

            // Claude Models
            "Claude 3.7 Sonnet": "claude-3-5-sonnet-20241022",
            "Claude 3.5 Haiku": "claude-3-haiku-20240307",

            // Gemini Models
            "Gemini 2.0 Flash": "gemini-2.0-flash-exp",
        ]
        return defaultModelIDs[modelName] ?? "gpt-4.1-nano-2025-04-14"  // Default to GPT-4.1 nano
    }
}
