//
//  PDFProcessingService.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.06.18.
//

import Foundation
import SwiftUI
import PDFKit

// PDF document information
struct PDFDocument: Identifiable {
    let id = UUID()
    let fileName: String
    let fileSize: Int64
    let pageCount: Int
    let title: String?
    let author: String?
    let subject: String?
    let creationDate: Date?
    let modificationDate: Date?
}

// PDF processing result
struct PDFProcessingResult {
    let document: PDFDocument
    let extractedText: String
    let summary: String
    let keyPoints: [String]
    let topics: [String]
    let wordCount: Int
    let processingTime: TimeInterval
}

// PDF service errors
enum PDFServiceError: LocalizedError {
    case fileNotFound
    case invalidPDFFile
    case textExtractionFailed
    case fileTooLarge
    case processingFailed(String)
    case unsupportedFormat
    
    var errorDescription: String? {
        switch self {
        case .fileNotFound:
            return "PDF file not found"
        case .invalidPDFFile:
            return "Invalid or corrupted PDF file"
        case .textExtractionFailed:
            return "Failed to extract text from PDF"
        case .fileTooLarge:
            return "PDF file is too large to process"
        case .processingFailed(let message):
            return "Processing failed: \(message)"
        case .unsupportedFormat:
            return "Unsupported file format"
        }
    }
}

class PDFProcessingService: ObservableObject {
    // Singleton instance
    static let shared = PDFProcessingService()
    
    // Published properties for UI updates
    @Published var isProcessing = false
    @Published var processingProgress: Double = 0.0
    @Published var currentStep = ""
    @Published var lastResult: PDFProcessingResult?
    @Published var errorMessage: String?
    
    // Services
    private let chatService = ChatService.shared
    
    // Configuration
    private let maxFileSize: Int64 = 50 * 1024 * 1024 // 50MB
    private let maxTextLength = 100000 // Limit text length for processing
    
    private init() {}
    
    // MARK: - Public Methods
    
    func processPDFFile(at url: URL, model: AIModel = AIModel.models.first!) async throws -> PDFProcessingResult {
        let startTime = CFAbsoluteTimeGetCurrent()
        
        DispatchQueue.main.async {
            self.isProcessing = true
            self.processingProgress = 0.0
            self.currentStep = "Loading PDF file..."
            self.errorMessage = nil
        }
        
        do {
            // Step 1: Validate file
            try validatePDFFile(at: url)
            
            DispatchQueue.main.async {
                self.processingProgress = 0.1
                self.currentStep = "Reading PDF document..."
            }
            
            // Step 2: Load PDF and extract metadata
            let pdfDocument = PDFKit.PDFDocument(url: url)
            guard let pdfDoc = pdfDocument else {
                throw PDFServiceError.invalidPDFFile
            }
            
            let documentInfo = extractDocumentInfo(from: pdfDoc, url: url)
            
            DispatchQueue.main.async {
                self.processingProgress = 0.3
                self.currentStep = "Extracting text content..."
            }
            
            // Step 3: Extract text
            let extractedText = try extractTextFromPDF(pdfDoc)
            
            DispatchQueue.main.async {
                self.processingProgress = 0.5
                self.currentStep = "Generating summary with AI..."
            }
            
            // Step 4: Generate summary
            let summary = try await generateSummary(text: extractedText, documentInfo: documentInfo, model: model)
            
            DispatchQueue.main.async {
                self.processingProgress = 0.7
                self.currentStep = "Extracting key points..."
            }
            
            // Step 5: Extract key points
            let keyPoints = try await extractKeyPoints(text: extractedText, model: model)
            
            DispatchQueue.main.async {
                self.processingProgress = 0.9
                self.currentStep = "Identifying topics..."
            }
            
            // Step 6: Extract topics
            let topics = try await extractTopics(text: extractedText, model: model)
            
            let processingTime = CFAbsoluteTimeGetCurrent() - startTime
            let wordCount = extractedText.components(separatedBy: .whitespacesAndNewlines).filter { !$0.isEmpty }.count
            
            let result = PDFProcessingResult(
                document: documentInfo,
                extractedText: extractedText,
                summary: summary,
                keyPoints: keyPoints,
                topics: topics,
                wordCount: wordCount,
                processingTime: processingTime
            )
            
            DispatchQueue.main.async {
                self.processingProgress = 1.0
                self.currentStep = "Complete!"
                self.lastResult = result
                self.isProcessing = false
            }
            
            return result
            
        } catch {
            DispatchQueue.main.async {
                self.isProcessing = false
                self.errorMessage = error.localizedDescription
            }
            throw error
        }
    }
    
    func askQuestionAboutPDF(_ question: String, pdfResult: PDFProcessingResult, model: AIModel = AIModel.models.first!) async throws -> String {
        let prompt = """
        Based on the following PDF document, please answer this question:
        
        Question: \(question)
        
        Document Title: \(pdfResult.document.title ?? pdfResult.document.fileName)
        Document Content:
        \(pdfResult.extractedText.prefix(maxTextLength))
        
        Please provide a detailed answer based on the document content.
        """
        
        let messages = [
            Message(content: prompt, isUser: true, timestamp: Date())
        ]
        
        return try await chatService.sendChatRequest(
            messages: messages,
            model: model,
            systemMessage: "You are a helpful assistant that answers questions based on document content. Provide accurate answers based only on the information in the document."
        )
    }
    
    // MARK: - Private Methods
    
    private func validatePDFFile(at url: URL) throws {
        // Check if file exists
        guard FileManager.default.fileExists(atPath: url.path) else {
            throw PDFServiceError.fileNotFound
        }
        
        // Check file size
        do {
            let attributes = try FileManager.default.attributesOfItem(atPath: url.path)
            if let fileSize = attributes[.size] as? Int64, fileSize > maxFileSize {
                throw PDFServiceError.fileTooLarge
            }
        } catch {
            throw PDFServiceError.fileNotFound
        }
        
        // Check file extension
        guard url.pathExtension.lowercased() == "pdf" else {
            throw PDFServiceError.unsupportedFormat
        }
    }
    
    private func extractDocumentInfo(from pdfDocument: PDFKit.PDFDocument, url: URL) -> PDFDocument {
        let fileName = url.lastPathComponent
        let fileSize = (try? FileManager.default.attributesOfItem(atPath: url.path)[.size] as? Int64) ?? 0
        let pageCount = pdfDocument.pageCount
        
        // Extract metadata
        let documentAttributes = pdfDocument.documentAttributes
        let title = documentAttributes?[PDFDocumentAttribute.titleAttribute] as? String
        let author = documentAttributes?[PDFDocumentAttribute.authorAttribute] as? String
        let subject = documentAttributes?[PDFDocumentAttribute.subjectAttribute] as? String
        let creationDate = documentAttributes?[PDFDocumentAttribute.creationDateAttribute] as? Date
        let modificationDate = documentAttributes?[PDFDocumentAttribute.modificationDateAttribute] as? Date
        
        return PDFDocument(
            fileName: fileName,
            fileSize: fileSize,
            pageCount: pageCount,
            title: title,
            author: author,
            subject: subject,
            creationDate: creationDate,
            modificationDate: modificationDate
        )
    }
    
    private func extractTextFromPDF(_ pdfDocument: PDFKit.PDFDocument) throws -> String {
        var extractedText = ""
        
        for pageIndex in 0..<pdfDocument.pageCount {
            guard let page = pdfDocument.page(at: pageIndex) else { continue }
            
            if let pageText = page.string {
                extractedText += pageText + "\n\n"
            }
        }
        
        guard !extractedText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            throw PDFServiceError.textExtractionFailed
        }
        
        // Limit text length for processing
        if extractedText.count > maxTextLength {
            extractedText = String(extractedText.prefix(maxTextLength))
        }
        
        return extractedText
    }
    
    private func generateSummary(text: String, documentInfo: PDFDocument, model: AIModel) async throws -> String {
        let prompt = """
        Please provide a comprehensive summary of this PDF document.
        
        Document: \(documentInfo.title ?? documentInfo.fileName)
        Author: \(documentInfo.author ?? "Unknown")
        Pages: \(documentInfo.pageCount)
        
        Content:
        \(text)
        
        Please provide:
        1. A brief overview of the document
        2. Main topics covered
        3. Key findings or conclusions
        4. Important details
        
        Keep the summary comprehensive but well-organized.
        """
        
        let messages = [
            Message(content: prompt, isUser: true, timestamp: Date())
        ]
        
        return try await chatService.sendChatRequest(
            messages: messages,
            model: model,
            systemMessage: "You are a helpful assistant that creates comprehensive summaries of documents. Focus on the most important information and organize it clearly."
        )
    }
    
    private func extractKeyPoints(text: String, model: AIModel) async throws -> [String] {
        let prompt = """
        Extract the key points from this document. Return them as a numbered list.
        
        Document Content:
        \(text)
        
        Please identify the most important points, insights, findings, or takeaways from this document.
        """
        
        let messages = [
            Message(content: prompt, isUser: true, timestamp: Date())
        ]
        
        let response = try await chatService.sendChatRequest(
            messages: messages,
            model: model,
            systemMessage: "You are a helpful assistant that extracts key points from documents. Return only the key points as a numbered list."
        )
        
        return parseListFromResponse(response)
    }
    
    private func extractTopics(text: String, model: AIModel) async throws -> [String] {
        let prompt = """
        Identify the main topics covered in this document. Return them as a simple list.
        
        Document Content:
        \(text)
        
        Please identify the main subjects, themes, or topics discussed in this document.
        """
        
        let messages = [
            Message(content: prompt, isUser: true, timestamp: Date())
        ]
        
        let response = try await chatService.sendChatRequest(
            messages: messages,
            model: model,
            systemMessage: "You are a helpful assistant that identifies topics from documents. Return only the topics as a simple list."
        )
        
        return parseListFromResponse(response)
    }
    
    private func parseListFromResponse(_ response: String) -> [String] {
        let lines = response.components(separatedBy: .newlines)
        var items: [String] = []
        
        for line in lines {
            let trimmed = line.trimmingCharacters(in: .whitespacesAndNewlines)
            if !trimmed.isEmpty {
                // Remove numbering, bullets, or dashes
                let cleaned = trimmed.replacingOccurrences(of: "^[0-9]+\\.\\s*", with: "", options: .regularExpression)
                    .replacingOccurrences(of: "^[-•*]\\s*", with: "", options: .regularExpression)
                    .trimmingCharacters(in: .whitespacesAndNewlines)
                
                if !cleaned.isEmpty {
                    items.append(cleaned)
                }
            }
        }
        
        return items
    }
}
