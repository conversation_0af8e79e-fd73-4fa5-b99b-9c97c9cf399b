//
//  YouTubeService.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.06.18.
//

import Foundation
import SwiftUI

// YouTube video information
struct YouTubeVideo: Identifiable {
    let id = UUID()
    let videoId: String
    let title: String
    let description: String
    let duration: String
    let thumbnailURL: String
    let channelName: String
    let viewCount: String
    let publishedDate: String
}

// YouTube processing result
struct YouTubeProcessingResult {
    let video: YouTubeVideo
    let transcript: String?
    let summary: String
    let keyPoints: [String]
    let topics: [String]
    let processingTime: TimeInterval
}

// YouTube service errors
enum YouTubeServiceError: LocalizedError {
    case invalidURL
    case videoNotFound
    case transcriptNotAvailable
    case apiError(String)
    case processingFailed(String)
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid YouTube URL"
        case .videoNotFound:
            return "Video not found or unavailable"
        case .transcriptNotAvailable:
            return "Transcript not available for this video"
        case .apiError(let message):
            return "API Error: \(message)"
        case .processingFailed(let message):
            return "Processing failed: \(message)"
        }
    }
}

class YouTubeService: ObservableObject {
    // Singleton instance
    static let shared = YouTubeService()
    
    // Published properties for UI updates
    @Published var isProcessing = false
    @Published var processingProgress: Double = 0.0
    @Published var currentStep = ""
    @Published var lastResult: YouTubeProcessingResult?
    @Published var errorMessage: String?
    
    // Services
    private let chatService = ChatService.shared
    
    private init() {}
    
    // MARK: - Public Methods
    
    func processYouTubeURL(_ urlString: String, model: AIModel = AIModel.models.first!) async throws -> YouTubeProcessingResult {
        let startTime = CFAbsoluteTimeGetCurrent()
        
        DispatchQueue.main.async {
            self.isProcessing = true
            self.processingProgress = 0.0
            self.currentStep = "Extracting video information..."
            self.errorMessage = nil
        }
        
        do {
            // Step 1: Extract video ID and get video info
            let videoId = try extractVideoId(from: urlString)
            
            DispatchQueue.main.async {
                self.processingProgress = 0.2
                self.currentStep = "Getting video details..."
            }
            
            let videoInfo = try await getVideoInfo(videoId: videoId)
            
            // Step 2: Get transcript
            DispatchQueue.main.async {
                self.processingProgress = 0.4
                self.currentStep = "Extracting transcript..."
            }
            
            let transcript = try await getVideoTranscript(videoId: videoId)
            
            // Step 3: Process with OpenAI
            DispatchQueue.main.async {
                self.processingProgress = 0.6
                self.currentStep = "Generating summary with AI..."
            }
            
            let summary = try await generateSummary(transcript: transcript, videoInfo: videoInfo, model: model)
            
            DispatchQueue.main.async {
                self.processingProgress = 0.8
                self.currentStep = "Extracting key points..."
            }
            
            let keyPoints = try await extractKeyPoints(transcript: transcript, model: model)
            
            DispatchQueue.main.async {
                self.processingProgress = 0.9
                self.currentStep = "Identifying topics..."
            }
            
            let topics = try await extractTopics(transcript: transcript, model: model)
            
            let processingTime = CFAbsoluteTimeGetCurrent() - startTime
            
            let result = YouTubeProcessingResult(
                video: videoInfo,
                transcript: transcript,
                summary: summary,
                keyPoints: keyPoints,
                topics: topics,
                processingTime: processingTime
            )
            
            DispatchQueue.main.async {
                self.processingProgress = 1.0
                self.currentStep = "Complete!"
                self.lastResult = result
                self.isProcessing = false
            }
            
            return result
            
        } catch {
            DispatchQueue.main.async {
                self.isProcessing = false
                self.errorMessage = error.localizedDescription
            }
            throw error
        }
    }
    
    func extractVideoId(from urlString: String) throws -> String {
        guard let url = URL(string: urlString) else {
            throw YouTubeServiceError.invalidURL
        }
        
        // Handle different YouTube URL formats
        if let host = url.host {
            if host.contains("youtube.com") {
                // Standard YouTube URL: https://www.youtube.com/watch?v=VIDEO_ID
                if let queryItems = URLComponents(url: url, resolvingAgainstBaseURL: false)?.queryItems,
                   let videoId = queryItems.first(where: { $0.name == "v" })?.value {
                    return videoId
                }
            } else if host.contains("youtu.be") {
                // Short YouTube URL: https://youtu.be/VIDEO_ID
                let videoId = url.lastPathComponent
                if !videoId.isEmpty {
                    return videoId
                }
            }
        }
        
        throw YouTubeServiceError.invalidURL
    }
    
    // MARK: - Private Methods
    
    private func getVideoInfo(videoId: String) async throws -> YouTubeVideo {
        // For now, we'll create a mock video info
        // In a real implementation, you would use YouTube Data API
        return YouTubeVideo(
            videoId: videoId,
            title: "YouTube Video",
            description: "Video description",
            duration: "Unknown",
            thumbnailURL: "https://img.youtube.com/vi/\(videoId)/maxresdefault.jpg",
            channelName: "Unknown Channel",
            viewCount: "Unknown",
            publishedDate: "Unknown"
        )
    }
    
    private func getVideoTranscript(videoId: String) async throws -> String {
        // For now, we'll return a placeholder
        // In a real implementation, you would use YouTube Transcript API or similar service
        // You could also use a backend service to extract transcripts
        
        // Simulate API call delay
        try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
        
        // Return placeholder transcript
        return """
        This is a placeholder transcript for the YouTube video. In a real implementation, 
        this would be the actual transcript extracted from the video using YouTube's 
        transcript API or a similar service. The transcript would contain the spoken 
        content of the video, which would then be processed by OpenAI to generate 
        summaries, key points, and topics.
        """
    }
    
    private func generateSummary(transcript: String, videoInfo: YouTubeVideo, model: AIModel) async throws -> String {
        let prompt = """
        Please provide a comprehensive summary of this YouTube video based on its transcript.
        
        Video Title: \(videoInfo.title)
        Channel: \(videoInfo.channelName)
        
        Transcript:
        \(transcript)
        
        Please provide:
        1. A brief overview (2-3 sentences)
        2. Main points discussed
        3. Key takeaways
        
        Keep the summary concise but informative.
        """
        
        let messages = [
            Message(content: prompt, isUser: true, timestamp: Date())
        ]
        
        return try await chatService.sendChatRequest(
            messages: messages,
            model: model,
            systemMessage: "You are a helpful assistant that creates clear, concise summaries of video content."
        )
    }
    
    private func extractKeyPoints(transcript: String, model: AIModel) async throws -> [String] {
        let prompt = """
        Extract the key points from this video transcript. Return them as a numbered list.
        
        Transcript:
        \(transcript)
        
        Please identify the most important points, insights, or takeaways from this content.
        """
        
        let messages = [
            Message(content: prompt, isUser: true, timestamp: Date())
        ]
        
        let response = try await chatService.sendChatRequest(
            messages: messages,
            model: model,
            systemMessage: "You are a helpful assistant that extracts key points from content. Return only the key points as a numbered list."
        )
        
        // Parse the response into an array of key points
        return parseListFromResponse(response)
    }
    
    private func extractTopics(transcript: String, model: AIModel) async throws -> [String] {
        let prompt = """
        Identify the main topics covered in this video transcript. Return them as a simple list.
        
        Transcript:
        \(transcript)
        
        Please identify the main subjects, themes, or topics discussed.
        """
        
        let messages = [
            Message(content: prompt, isUser: true, timestamp: Date())
        ]
        
        let response = try await chatService.sendChatRequest(
            messages: messages,
            model: model,
            systemMessage: "You are a helpful assistant that identifies topics from content. Return only the topics as a simple list."
        )
        
        // Parse the response into an array of topics
        return parseListFromResponse(response)
    }
    
    private func parseListFromResponse(_ response: String) -> [String] {
        let lines = response.components(separatedBy: .newlines)
        var items: [String] = []
        
        for line in lines {
            let trimmed = line.trimmingCharacters(in: .whitespacesAndNewlines)
            if !trimmed.isEmpty {
                // Remove numbering, bullets, or dashes
                let cleaned = trimmed.replacingOccurrences(of: "^[0-9]+\\.\\s*", with: "", options: .regularExpression)
                    .replacingOccurrences(of: "^[-•*]\\s*", with: "", options: .regularExpression)
                    .trimmingCharacters(in: .whitespacesAndNewlines)
                
                if !cleaned.isEmpty {
                    items.append(cleaned)
                }
            }
        }
        
        return items
    }
}
