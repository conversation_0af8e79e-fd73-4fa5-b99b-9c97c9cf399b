//
//  StoreKitService.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.05.01.
//

import Foundation
import StoreKit
import Combine

@MainActor
class StoreKitService: ObservableObject {
    // Singleton instance
    static let shared = StoreKitService()
    
    // Publishers
    @Published var isPremium: Bool = false
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    
    // Product IDs
    enum ProductID: String, CaseIterable {
        case weekly = "com.plutoai.chatbot.weekly"
        case lifetime = "com.plutoai.chatbot.lifetime"
    }
    
    // Available products
    @Published var products: [Product] = []
    
    private var updateListenerTask: Task<Void, Error>? = nil
    
    private init() {
        // Start listening for transaction updates
        updateListenerTask = listenForTransactions()
        
        // Load products and check subscription status
        Task {
            await loadProducts()
            await checkSubscriptionStatus()
        }
    }
    
    deinit {
        updateListenerTask?.cancel()
    }
    
    // Load available products from App Store
    func loadProducts() async {
        print("StoreKit: Loading products...")
        isLoading = true
        errorMessage = nil
        
        do {
            let productIDs = ProductID.allCases.map { $0.rawValue }
            let storeProducts = try await Product.products(for: productIDs)
            
            DispatchQueue.main.async {
                self.products = storeProducts
                self.isLoading = false
                print("StoreKit: Loaded \(storeProducts.count) products")
                for product in storeProducts {
                    print("StoreKit: Product - \(product.id): \(product.displayName) - \(product.displayPrice)")
                }
            }
        } catch {
            DispatchQueue.main.async {
                self.errorMessage = "Failed to load products: \(error.localizedDescription)"
                self.isLoading = false
                print("StoreKit Error: \(error.localizedDescription)")
            }
        }
    }
    
    // Purchase a product
    func purchase(_ product: Product) async -> Bool {
        print("StoreKit: Attempting to purchase \(product.id)")
        isLoading = true
        errorMessage = nil
        
        do {
            let result = try await product.purchase()
            
            switch result {
            case .success(let verification):
                let transaction = try Self.checkVerified(verification)
                
                // Verify with backend
                await verifyWithBackend(transaction: transaction)
                
                // Finish the transaction
                await transaction.finish()
                
                // Update subscription status
                await checkSubscriptionStatus()
                
                DispatchQueue.main.async {
                    self.isLoading = false
                }
                
                print("StoreKit: Purchase successful for \(product.id)")
                return true
                
            case .userCancelled:
                DispatchQueue.main.async {
                    self.isLoading = false
                }
                print("StoreKit: Purchase cancelled by user")
                return false
                
            case .pending:
                DispatchQueue.main.async {
                    self.isLoading = false
                    self.errorMessage = "Purchase is pending approval"
                }
                print("StoreKit: Purchase pending")
                return false
                
            @unknown default:
                DispatchQueue.main.async {
                    self.isLoading = false
                    self.errorMessage = "Unknown purchase result"
                }
                print("StoreKit: Unknown purchase result")
                return false
            }
        } catch {
            DispatchQueue.main.async {
                self.errorMessage = "Purchase failed: \(error.localizedDescription)"
                self.isLoading = false
            }
            print("StoreKit Purchase Error: \(error.localizedDescription)")
            return false
        }
    }
    
    // Restore purchases
    func restorePurchases() async -> Bool {
        print("StoreKit: Restoring purchases...")
        isLoading = true
        errorMessage = nil
        
        do {
            try await AppStore.sync()
            await checkSubscriptionStatus()
            
            DispatchQueue.main.async {
                self.isLoading = false
            }
            
            print("StoreKit: Purchases restored successfully")
            return isPremium
        } catch {
            DispatchQueue.main.async {
                self.errorMessage = "Failed to restore purchases: \(error.localizedDescription)"
                self.isLoading = false
            }
            print("StoreKit Restore Error: \(error.localizedDescription)")
            return false
        }
    }
    
    // Check current subscription status
    func checkSubscriptionStatus() async {
        print("StoreKit: Checking subscription status...")
        
        var hasActiveSubscription = false
        
        // Check for active subscriptions
        for await result in Transaction.currentEntitlements {
            do {
                let transaction = try Self.checkVerified(result)
                
                // Check if this is one of our subscription products
                if ProductID.allCases.map({ $0.rawValue }).contains(transaction.productID) {
                    print("StoreKit: Found active subscription: \(transaction.productID)")
                    hasActiveSubscription = true
                    break
                }
            } catch {
                print("StoreKit: Failed to verify transaction: \(error.localizedDescription)")
            }
        }
        
        DispatchQueue.main.async {
            self.isPremium = hasActiveSubscription
            print("StoreKit: Is Premium: \(hasActiveSubscription)")
        }
    }
    
    // Listen for transaction updates
    private func listenForTransactions() -> Task<Void, Error> {
        return Task { [weak self] in
            guard let self = self else { return }

            for await result in Transaction.updates {
                do {
                    let transaction = try Self.checkVerified(result)

                    // Verify with backend
                    await self.verifyWithBackend(transaction: transaction)

                    // Update subscription status
                    await self.checkSubscriptionStatus()

                    // Finish the transaction
                    await transaction.finish()
                } catch {
                    print("StoreKit: Transaction update error: \(error.localizedDescription)")
                }
            }
        }
    }
    
    // Verify transaction
    private static func checkVerified<T>(_ result: VerificationResult<T>) throws -> T {
        switch result {
        case .unverified:
            throw StoreError.failedVerification
        case .verified(let safe):
            return safe
        }
    }
    
    // Verify with backend
    private func verifyWithBackend(transaction: Transaction) async {
        print("StoreKit: Verifying transaction with backend...")
        
        // Get the API URL
        guard let url = URL(string: "\(APIConfig.backendBaseURL)/api/v1/subscription/verify") else {
            print("StoreKit: Invalid backend URL")
            return
        }
        
        // Create request
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        
        // Add device-based authentication headers
        let deviceInfo = DeviceService.shared.getDeviceInfo()
        if let deviceId = deviceInfo["device_id"], let userId = deviceInfo["user_id"] {
            request.addValue(deviceId, forHTTPHeaderField: "X-Device-ID")
            request.addValue(userId, forHTTPHeaderField: "X-User-ID")
        }
        
        // Create request body
        let body: [String: Any] = [
            "transaction_id": String(transaction.id),
            "product_id": transaction.productID,
            "purchase_date": ISO8601DateFormatter().string(from: transaction.purchaseDate)
        ]
        
        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: body)
            
            let (_, response) = try await URLSession.shared.data(for: request)
            
            if let httpResponse = response as? HTTPURLResponse,
               (200...299).contains(httpResponse.statusCode) {
                print("StoreKit: Transaction verified with backend")
            } else {
                print("StoreKit: Backend verification failed")
            }
        } catch {
            print("StoreKit: Backend verification error: \(error.localizedDescription)")
        }
    }
}

// Custom errors
enum StoreError: Error {
    case failedVerification
}
