//
//  TextToSpeechService.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.06.18.
//

import Foundation
import AVFoundation
import SwiftUI

// Voice model for different voice options
struct VoiceModel: Identifiable, Codable {
    let id: UUID
    let name: String
    let identifier: String
    let language: String
    let gender: String
    let description: String

    init(name: String, identifier: String, language: String, gender: String, description: String) {
        self.id = UUID()
        self.name = name
        self.identifier = identifier
        self.language = language
        self.gender = gender
        self.description = description
    }
    
    static let availableVoices: [VoiceModel] = [
        VoiceModel(name: "<PERSON>", identifier: "com.apple.ttsbundle.Samantha-compact", language: "en-US", gender: "Female", description: "Warm and friendly female voice"),
        VoiceModel(name: "Aurora", identifier: "com.apple.ttsbundle.siri_female_en-US_compact", language: "en-US", gender: "Female", description: "Clear and professional female voice"),
        VoiceModel(name: "<PERSON>", identifier: "com.apple.ttsbundle.Alex-compact", language: "en-US", gender: "Male", description: "Deep and confident male voice"),
        VoiceModel(name: "<PERSON><PERSON>", identifier: "com.apple.ttsbundle.siri_female_en-GB_compact", language: "en-GB", gender: "Female", description: "British accent female voice"),
        VoiceModel(name: "Zephyr", identifier: "com.apple.ttsbundle.siri_male_en-US_compact", language: "en-US", gender: "Male", description: "Smooth and articulate male voice")
    ]
}

class TextToSpeechService: NSObject, ObservableObject {
    // Singleton instance
    static let shared = TextToSpeechService()
    
    // Published properties for UI updates
    @Published var isSpeaking = false
    @Published var currentText = ""
    @Published var selectedVoice: VoiceModel = VoiceModel.availableVoices[0]
    @Published var speechRate: Float = 0.5
    @Published var speechPitch: Float = 1.0
    @Published var speechVolume: Float = 1.0
    
    // Speech synthesizer
    private let speechSynthesizer = AVSpeechSynthesizer()
    private var currentUtterance: AVSpeechUtterance?
    
    // Voice settings
    private let voiceSettingsKey = "SelectedVoiceSettings"
    
    override init() {
        super.init()
        speechSynthesizer.delegate = self
        loadVoiceSettings()
        configureAudioSession()
    }
    
    // MARK: - Public Methods
    
    func speak(text: String, completion: (() -> Void)? = nil) {
        // Stop any current speech
        stopSpeaking()
        
        // Clean and prepare text
        let cleanedText = cleanTextForSpeech(text)
        guard !cleanedText.isEmpty else {
            completion?()
            return
        }
        
        // Create utterance
        let utterance = AVSpeechUtterance(string: cleanedText)
        
        // Configure utterance with selected voice
        if let voice = findVoice(for: selectedVoice) {
            utterance.voice = voice
        } else {
            // Fallback to default voice
            utterance.voice = AVSpeechSynthesisVoice(language: selectedVoice.language)
        }
        
        // Set speech parameters
        utterance.rate = speechRate
        utterance.pitchMultiplier = speechPitch
        utterance.volume = speechVolume
        
        // Store current utterance and text
        currentUtterance = utterance
        currentText = cleanedText
        
        // Start speaking
        speechSynthesizer.speak(utterance)
        isSpeaking = true
        
        // Store completion for later use
        if let completion = completion {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                // We'll call completion when speech finishes
                self.speechCompletionHandler = completion
            }
        }
    }
    
    func stopSpeaking() {
        if speechSynthesizer.isSpeaking {
            speechSynthesizer.stopSpeaking(at: .immediate)
        }
        isSpeaking = false
        currentText = ""
        currentUtterance = nil
        speechCompletionHandler = nil
    }
    
    func pauseSpeaking() {
        if speechSynthesizer.isSpeaking {
            speechSynthesizer.pauseSpeaking(at: .word)
        }
    }
    
    func continueSpeaking() {
        if speechSynthesizer.isPaused {
            speechSynthesizer.continueSpeaking()
        }
    }
    
    func setVoice(_ voice: VoiceModel) {
        selectedVoice = voice
        saveVoiceSettings()
    }
    
    func setSpeechRate(_ rate: Float) {
        speechRate = max(0.1, min(1.0, rate))
        saveVoiceSettings()
    }
    
    func setSpeechPitch(_ pitch: Float) {
        speechPitch = max(0.5, min(2.0, pitch))
        saveVoiceSettings()
    }
    
    func setSpeechVolume(_ volume: Float) {
        speechVolume = max(0.0, min(1.0, volume))
        saveVoiceSettings()
    }
    
    // MARK: - Private Methods
    
    private var speechCompletionHandler: (() -> Void)?
    
    private func configureAudioSession() {
        do {
            try AVAudioSession.sharedInstance().setCategory(.playback, mode: .spokenAudio, options: [.duckOthers])
            try AVAudioSession.sharedInstance().setActive(true)
        } catch {
            print("Failed to configure audio session for TTS: \(error)")
        }
    }
    
    private func findVoice(for voiceModel: VoiceModel) -> AVSpeechSynthesisVoice? {
        // Try to find the exact voice by identifier
        if let voice = AVSpeechSynthesisVoice(identifier: voiceModel.identifier) {
            return voice
        }
        
        // Fallback to finding by language and quality
        let voices = AVSpeechSynthesisVoice.speechVoices()
        
        // First try to find enhanced quality voices
        for voice in voices {
            if voice.language == voiceModel.language && voice.quality == .enhanced {
                return voice
            }
        }
        
        // Then try default quality voices
        for voice in voices {
            if voice.language == voiceModel.language {
                return voice
            }
        }
        
        // Final fallback
        return AVSpeechSynthesisVoice(language: "en-US")
    }
    
    private func cleanTextForSpeech(_ text: String) -> String {
        var cleanedText = text
        
        // Remove markdown formatting
        cleanedText = cleanedText.replacingOccurrences(of: "**", with: "")
        cleanedText = cleanedText.replacingOccurrences(of: "*", with: "")
        cleanedText = cleanedText.replacingOccurrences(of: "_", with: "")
        cleanedText = cleanedText.replacingOccurrences(of: "`", with: "")
        
        // Remove URLs
        let urlPattern = "https?://[^\\s]+"
        cleanedText = cleanedText.replacingOccurrences(of: urlPattern, with: "link", options: .regularExpression)
        
        // Clean up extra whitespace
        cleanedText = cleanedText.trimmingCharacters(in: .whitespacesAndNewlines)
        cleanedText = cleanedText.replacingOccurrences(of: "\\s+", with: " ", options: .regularExpression)
        
        return cleanedText
    }
    
    private func saveVoiceSettings() {
        let settings = [
            "voiceName": selectedVoice.name,
            "speechRate": speechRate,
            "speechPitch": speechPitch,
            "speechVolume": speechVolume
        ] as [String : Any]
        
        UserDefaults.standard.set(settings, forKey: voiceSettingsKey)
    }
    
    private func loadVoiceSettings() {
        guard let settings = UserDefaults.standard.dictionary(forKey: voiceSettingsKey) else { return }
        
        if let voiceName = settings["voiceName"] as? String,
           let voice = VoiceModel.availableVoices.first(where: { $0.name == voiceName }) {
            selectedVoice = voice
        }
        
        if let rate = settings["speechRate"] as? Float {
            speechRate = rate
        }
        
        if let pitch = settings["speechPitch"] as? Float {
            speechPitch = pitch
        }
        
        if let volume = settings["speechVolume"] as? Float {
            speechVolume = volume
        }
    }
}

// MARK: - AVSpeechSynthesizerDelegate

extension TextToSpeechService: AVSpeechSynthesizerDelegate {
    func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didStart utterance: AVSpeechUtterance) {
        DispatchQueue.main.async {
            self.isSpeaking = true
        }
    }
    
    func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didFinish utterance: AVSpeechUtterance) {
        DispatchQueue.main.async {
            self.isSpeaking = false
            self.currentText = ""
            self.currentUtterance = nil
            self.speechCompletionHandler?()
            self.speechCompletionHandler = nil
        }
    }
    
    func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didCancel utterance: AVSpeechUtterance) {
        DispatchQueue.main.async {
            self.isSpeaking = false
            self.currentText = ""
            self.currentUtterance = nil
            self.speechCompletionHandler = nil
        }
    }
    
    func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didPause utterance: AVSpeechUtterance) {
        // Speech is paused but still considered "speaking"
    }
    
    func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didContinue utterance: AVSpeechUtterance) {
        // Speech continues
    }
}
