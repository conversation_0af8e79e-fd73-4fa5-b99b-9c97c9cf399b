//
//  DeviceService.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.05.24.
//

import Foundation
import UIKit

class DeviceService {
    static let shared = DeviceService()

    private let deviceIdKey = "PlutoAI_DeviceID"
    private let userIdKey = "PlutoAI_UserID"

    private init() {}

    // Get or create device ID
    var deviceId: String {
        if let existingId = UserDefaults.standard.string(forKey: deviceIdKey) {
            return existingId
        }

        let newId = generateDeviceId()
        UserDefaults.standard.set(newId, forKey: deviceIdKey)
        return newId
    }

    // Get or create user ID (for backend communication)
    var userId: String {
        if let existingId = UserDefaults.standard.string(forKey: userIdKey) {
            return existingId
        }

        let newId = "user_\(deviceId)"
        UserDefaults.standard.set(newId, forKey: userIdKey)
        return newId
    }

    // Initialize device and register with backend if needed
    func initializeDevice() async {
        let currentDeviceId = deviceId
        let currentUserId = userId

        print("DeviceService: Initializing device with ID: \(currentDeviceId)")
        print("DeviceService: User ID: \(currentUserId)")

        // Register device with backend (optional - for analytics/tracking)
        await registerDeviceWithBackend()
    }

    // Generate a unique device ID
    private func generateDeviceId() -> String {
        // Use a combination of UUID and device info for uniqueness
        let uuid = UUID().uuidString
        let deviceModel = UIDevice.current.model
        let systemVersion = UIDevice.current.systemVersion

        // Create a hash-like ID
        let combined = "\(uuid)_\(deviceModel)_\(systemVersion)"
        let hash = combined.hash

        return "device_\(abs(hash))"
    }

    // Register device with backend (optional)
    private func registerDeviceWithBackend() async {
        do {
            let deviceInfo = [
                "device_id": deviceId,
                "user_id": userId,
                "device_model": UIDevice.current.model,
                "system_version": UIDevice.current.systemVersion,
                "app_version": Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0"
            ]

            // This is optional - you can remove this if you don't want to track devices
            print("DeviceService: Device registered locally - \(deviceInfo)")

            // If you want to register with your backend, uncomment this:
            /*
            guard let url = URL(string: "\(APIConfig.baseURL)/api/v1/device/register") else { return }

            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            request.setValue("application/json", forHTTPHeaderField: "Content-Type")
            request.httpBody = try JSONSerialization.data(withJSONObject: deviceInfo)

            let (_, response) = try await URLSession.shared.data(for: request)

            if let httpResponse = response as? HTTPURLResponse {
                print("DeviceService: Device registration response: \(httpResponse.statusCode)")
            }
            */
        } catch {
            print("DeviceService: Error registering device: \(error)")
        }
    }

    // Reset device (for testing or if user wants to start fresh)
    func resetDevice() {
        UserDefaults.standard.removeObject(forKey: deviceIdKey)
        UserDefaults.standard.removeObject(forKey: userIdKey)
        print("DeviceService: Device reset")
    }

    // Get device info for debugging
    func getDeviceInfo() -> [String: String] {
        return [
            "device_id": deviceId,
            "user_id": userId,
            "device_model": UIDevice.current.model,
            "system_version": UIDevice.current.systemVersion,
            "app_version": Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0"
        ]
    }
}
