//
//  RevenueCatService.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.05.01.
//

import Foundation
import StoreKit
import Combine

class RevenueCatService {
    // Singleton instance
    static let shared = RevenueCatService()

    // Publishers
    private let isPremiumSubject = CurrentValueSubject<Bool, Never>(false)
    var isPremium: AnyPublisher<Bool, Never> {
        return isPremiumSubject.eraseToAnyPublisher()
    }



    private init() {
        print("RevenueCat: Initializing with native StoreKit...")

        // Use StoreKit service instead of RevenueCat
        Task {
            await StoreKitService.shared.checkSubscriptionStatus()

            // Subscribe to StoreKit updates
            await MainActor.run {
                StoreKitService.shared.$isPremium
                    .receive(on: DispatchQueue.main)
                    .sink { [weak self] isPremium in
                        self?.isPremiumSubject.send(isPremium)
                    }
                    .store(in: &cancellables)
            }
        }
    }

    private var cancellables = Set<AnyCancellable>()

    // Get available packages
    func getOfferings(completion: @escaping ([Product]?) -> Void) {
        Task {
            await StoreKitService.shared.loadProducts()
            let products = await MainActor.run {
                StoreKitService.shared.products
            }
            completion(products.isEmpty ? nil : products)
        }
    }

    // Purchase a package
    func purchase(product: Product, completion: @escaping (Bool, Error?) -> Void) {
        Task {
            let success = await StoreKitService.shared.purchase(product)
            completion(success, nil)
        }
    }

    // Restore purchases
    func restorePurchases(completion: @escaping (Bool, Error?) -> Void) {
        Task {
            let success = await StoreKitService.shared.restorePurchases()
            completion(success, nil)
        }
    }

    // Check subscription status
    func checkSubscriptionStatus() {
        Task {
            await StoreKitService.shared.checkSubscriptionStatus()
        }
    }

    // Get current subscription status (async version)
    func getCurrentSubscriptionStatus() async -> Bool {
        return await MainActor.run {
            StoreKitService.shared.isPremium
        }
    }

    // Get current subscription status (synchronous version using the published value)
    var currentSubscriptionStatus: Bool {
        return isPremiumSubject.value
    }
}
