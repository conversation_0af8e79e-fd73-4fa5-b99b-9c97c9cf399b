//
//  ChatService.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.04.30.
//

import Foundation
import Combine

// High-performance streaming delegate
@MainActor
protocol ChatStreamingDelegate {
    func didReceiveStreamingToken(_ token: String)
    func didCompleteStreaming()
    func didFailStreaming(with error: Error)
}

class ChatService {
    // Singleton instance
    static let shared = ChatService()

    // Streaming delegate with thread-safe access
    private var _streamingDelegate: ChatStreamingDelegate?
    private let delegateQueue = DispatchQueue(label: "com.plutoai.chatservice.delegate", attributes: .concurrent)

    var streamingDelegate: ChatStreamingDelegate? {
        get {
            return delegateQueue.sync { _streamingDelegate }
        }
        set {
            delegateQueue.async(flags: .barrier) {
                self._streamingDelegate = newValue
            }
        }
    }

    // Connection status for UI feedback
    @Published var connectionStatus: ConnectionStatus = .connecting

    enum ConnectionStatus {
        case connecting
        case connected
        case streaming
        case error
    }

    // Ultra-optimized URLSession for ChatGPT-level streaming
    private lazy var streamingSession: URLSession = {
        let config = URLSessionConfiguration.default

        // Aggressive optimization for sub-500ms response times
        config.timeoutIntervalForRequest = 10.0  // Faster timeout for quicker failures
        config.timeoutIntervalForResource = 300.0  // 5 minutes for entire stream
        config.httpMaximumConnectionsPerHost = 20  // More connections for better throughput
        config.requestCachePolicy = .reloadIgnoringLocalCacheData
        config.urlCache = nil  // No caching overhead

        // Network optimizations
        config.httpShouldUsePipelining = true
        config.httpShouldSetCookies = false
        config.allowsCellularAccess = true
        config.allowsConstrainedNetworkAccess = true
        config.allowsExpensiveNetworkAccess = true

        // Reduce latency
        config.httpAdditionalHeaders = [
            "Connection": "keep-alive",
            "Accept-Encoding": "gzip, deflate",
            "Cache-Control": "no-cache"
        ]

        // Optimize for streaming
        config.waitsForConnectivity = false  // Don't wait, fail fast

        return URLSession(configuration: config)
    }()

    private init() {}

    // Fast model ID lookup (local cache for performance)
    private func getModelIDFast(for modelName: String) -> String {
        switch modelName {
        // OpenAI Models
        case "GPT-4.1 nano": return "gpt-4.1-nano-2025-04-14"
        case "GPT-4.1 mini": return "gpt-4.1-mini-2025-04-14"
        case "GPT-4.1": return "gpt-4.1-2025-04-14"
        case "o4 - Mini": return "gpt-4o-mini"
        case "GPT-4o": return "gpt-4o"
        case "GPT-4o mini": return "gpt-4o-mini"
        case "o3": return "o3-mini"

        // DeepSeek Models
        case "DeepSeek R1": return "deepseek-chat"
        case "DeepSeek V3": return "deepseek-chat"

        // Claude Models
        case "Claude 3.7 Sonnet": return "claude-3-5-sonnet-20241022"
        case "Claude 3.5 Haiku": return "claude-3-haiku-20240307"

        // Gemini Models
        case "Gemini 2.0 Flash": return "gemini-2.0-flash-exp"

        default: return "gpt-4.1-nano-2025-04-14" // Safe default (GPT-4.1 nano)
        }
    }

    // Message role enum
    enum MessageRole: String, Codable {
        case system
        case user
        case assistant
    }

    // Chat message structure for API
    struct ChatMessage: Codable {
        let role: String
        let content: String
    }

    // Request structure
    struct ChatCompletionRequest: Codable {
        let model: String
        let messages: [ChatMessage]
        var temperature: Float
        let max_tokens: Int?
        let max_completion_tokens: Int?
        let stream: Bool?

        // Custom initializer to handle reasoning models
        init(model: String, messages: [ChatMessage], temperature: Float = 0.7, maxTokens: Int = 1000, stream: Bool = false) {
            self.model = model
            self.messages = messages
            self.temperature = temperature
            self.stream = stream

            // Use max_completion_tokens for reasoning models (o1, o3, o4)
            let isReasoningModel = model.lowercased().contains("o1") ||
                                 model.lowercased().contains("o3") ||
                                 model.lowercased().contains("o4")

            if isReasoningModel {
                self.temperature = 1.0  // Reasoning models only support default temperature
                self.max_completion_tokens = maxTokens
                self.max_tokens = nil
            } else {
                self.temperature = temperature  // Regular models can use custom temperature
                self.max_tokens = maxTokens
                self.max_completion_tokens = nil
            }
        }
    }

    // Response structure
    struct ChatCompletionResponse: Codable {
        struct Choice: Codable {
            struct Message: Codable {
                let role: String
                let content: String
            }
            let message: Message
            let finish_reason: String?
            let index: Int
        }

        struct Usage: Codable {
            let prompt_tokens: Int
            let completion_tokens: Int
            let total_tokens: Int
        }

        let id: String
        let object: String
        let created: Int
        let model: String
        let choices: [Choice]
        let usage: Usage
    }

    // Streaming response structure
    struct StreamingChatResponse: Codable {
        struct Choice: Codable {
            struct Delta: Codable {
                let content: String?
                let role: String?
            }
            let delta: Delta
            let index: Int
            let finish_reason: String?
        }

        let id: String
        let object: String
        let created: Int
        let model: String
        let choices: [Choice]
    }

    // Error handling
    enum ChatServiceError: Error {
        case invalidURL
        case requestFailed(Error)
        case invalidResponse
        case decodingFailed(Error)

        var localizedDescription: String {
            switch self {
            case .invalidURL:
                return "Invalid URL"
            case .requestFailed(let error):
                return "Request failed: \(error.localizedDescription)"
            case .invalidResponse:
                return "Invalid response from server"
            case .decodingFailed(let error):
                return "Failed to decode response: \(error.localizedDescription)"
            }
        }
    }

    // Convert app messages to API format with custom system message
    private func convertToAPIMessages(_ messages: [Message], systemMessage: String? = nil) -> [ChatMessage] {
        var apiMessages: [ChatMessage] = [
            // Add a system message to set the assistant's behavior
            ChatMessage(
                role: "system",
                content: systemMessage ?? "You are a helpful, creative, and friendly AI assistant named Pluto AI."
            )
        ]

        // Add user and assistant messages
        for message in messages {
            let role = message.isUser ? "user" : "assistant"
            apiMessages.append(ChatMessage(role: role, content: message.content))
        }

        return apiMessages
    }

    // Legacy method kept for compatibility - now redirects to streaming
    func sendChatRequest(messages: [Message], model: AIModel, systemMessage: String? = nil, assistantName: String? = nil) async throws -> String {
        print("ChatService: Legacy sendChatRequest called - redirecting to direct API")
        return try await sendChatRequestDirect(messages: messages, model: model, systemMessage: systemMessage)
    }

    // Send streaming chat request via backend
    func sendStreamingChatRequest(messages: [Message], model: AIModel, systemMessage: String? = nil, assistantName: String? = nil) async {
        print("ChatService: Starting streaming chat request via backend with model: \(model.name)")
        print("ChatService: Delegate is set: \(streamingDelegate != nil)")

        // Use backend streaming endpoint
        await sendStreamingChatRequestViaBackend(messages: messages, model: model, systemMessage: systemMessage)
    }



    // Send chat request directly to OpenAI (temporary solution)
    private func sendChatRequestDirect(messages: [Message], model: AIModel, systemMessage: String?) async throws -> String {
        // Get the model ID from the display name
        let modelID = await APIConfig.getModelID(for: model.name)

        // Convert messages to API format with the custom system message
        let apiMessages = convertToAPIMessages(messages, systemMessage: systemMessage)

        // Create the request
        let requestBody = ChatCompletionRequest(
            model: modelID,
            messages: apiMessages,
            temperature: 0.7,
            maxTokens: 1000,
            stream: false
        )

        // Use OpenAI API directly (temporary)
        guard let url = URL(string: "https://api.openai.com/v1/chat/completions") else {
            throw ChatServiceError.invalidURL
        }

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        // Use a working OpenAI API key (you'll need to replace this with your key)
        request.addValue("Bearer YOUR_OPENAI_API_KEY_HERE", forHTTPHeaderField: "Authorization")

        do {
            request.httpBody = try JSONEncoder().encode(requestBody)
        } catch {
            throw ChatServiceError.requestFailed(error)
        }

        // Send the request
        do {
            let (data, response) = try await URLSession.shared.data(for: request)

            // Check response status
            guard let httpResponse = response as? HTTPURLResponse,
                  (200...299).contains(httpResponse.statusCode) else {
                throw ChatServiceError.invalidResponse
            }

            // Decode the OpenAI response
            do {
                let decodedResponse = try JSONDecoder().decode(ChatCompletionResponse.self, from: data)
                if let firstChoice = decodedResponse.choices.first {
                    return firstChoice.message.content
                } else {
                    return "I couldn't generate a response. Please try again."
                }
            } catch {
                throw ChatServiceError.decodingFailed(error)
            }
        } catch {
            throw ChatServiceError.requestFailed(error)
        }
    }



    // MARK: - Backend Chat History API Methods

    // Create a new chat session
    func createChat(assistantId: String, title: String) async throws -> String {
        print("ChatService: Creating new chat with assistant: \(assistantId)")
        print("ChatService: Backend URL: \(APIConfig.backendBaseURL)")
        print("ChatService: Device ID: \(DeviceService.shared.deviceId)")
        print("ChatService: User ID: \(DeviceService.shared.userId)")

        let url = URL(string: "\(APIConfig.backendBaseURL)/api/v1/chat")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue(DeviceService.shared.deviceId, forHTTPHeaderField: "X-Device-ID")
        request.setValue(DeviceService.shared.userId, forHTTPHeaderField: "X-User-ID")

        let requestBody: [String: Any] = [
            "assistant_id": assistantId,
            "title": title
        ]

        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            print("ChatService: Invalid HTTP response")
            throw ChatServiceError.invalidResponse
        }

        print("ChatService: Create chat response status: \(httpResponse.statusCode)")

        guard httpResponse.statusCode == 200 else {
            print("ChatService: Error response body: \(String(data: data, encoding: .utf8) ?? "nil")")
            let error = NSError(domain: "ChatService", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: "HTTP \(httpResponse.statusCode)"])
            throw ChatServiceError.requestFailed(error)
        }

        guard let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
              let chatId = json["id"] as? String else {
            print("ChatService: Failed to parse chat response: \(String(data: data, encoding: .utf8) ?? "nil")")
            let error = NSError(domain: "ChatService", code: -1, userInfo: [NSLocalizedDescriptionKey: "Failed to parse chat response"])
            throw ChatServiceError.decodingFailed(error)
        }

        print("ChatService: Created chat with ID: \(chatId)")
        return chatId
    }

    // Send message to existing chat and get AI response
    func sendMessageToChat(chatId: String, message: String) async throws -> String {
        print("ChatService: Sending message to chat: \(chatId)")
        print("ChatService: Message content: \(message)")
        print("ChatService: Device ID: \(DeviceService.shared.deviceId)")
        print("ChatService: User ID: \(DeviceService.shared.userId)")

        let url = URL(string: "\(APIConfig.backendBaseURL)/api/v1/chat/\(chatId)/messages")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue(DeviceService.shared.deviceId, forHTTPHeaderField: "X-Device-ID")
        request.setValue(DeviceService.shared.userId, forHTTPHeaderField: "X-User-ID")

        let requestBody: [String: Any] = [
            "content": message
        ]

        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            print("ChatService: Invalid HTTP response for message")
            throw ChatServiceError.invalidResponse
        }

        print("ChatService: Send message response status: \(httpResponse.statusCode)")

        guard httpResponse.statusCode == 200 else {
            print("ChatService: Error response body: \(String(data: data, encoding: .utf8) ?? "nil")")
            let error = NSError(domain: "ChatService", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: "HTTP \(httpResponse.statusCode)"])
            throw ChatServiceError.requestFailed(error)
        }

        guard let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
              let content = json["content"] as? String else {
            print("ChatService: Failed to parse message response: \(String(data: data, encoding: .utf8) ?? "nil")")
            let error = NSError(domain: "ChatService", code: -1, userInfo: [NSLocalizedDescriptionKey: "Failed to parse message response"])
            throw ChatServiceError.decodingFailed(error)
        }

        print("ChatService: Received AI response: \(content)")
        return content
    }

    // Get user's chat list
    func getUserChats() async throws -> [[String: Any]] {
        print("🔥 ChatService: Loading user chats")
        print("🔥 ChatService: URL: \(APIConfig.backendBaseURL)/api/v1/chat")
        print("🔥 ChatService: Device ID: \(DeviceService.shared.deviceId)")
        print("🔥 ChatService: User ID: \(DeviceService.shared.userId)")

        let url = URL(string: "\(APIConfig.backendBaseURL)/api/v1/chat")!
        var request = URLRequest(url: url)
        request.setValue(DeviceService.shared.deviceId, forHTTPHeaderField: "X-Device-ID")
        request.setValue(DeviceService.shared.userId, forHTTPHeaderField: "X-User-ID")

        print("🔥 ChatService: Making request to backend...")
        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            print("🔥 ChatService: Invalid HTTP response")
            throw ChatServiceError.invalidResponse
        }

        print("🔥 ChatService: Response status: \(httpResponse.statusCode)")

        guard httpResponse.statusCode == 200 else {
            print("🔥 ChatService: Error response body: \(String(data: data, encoding: .utf8) ?? "nil")")
            throw ChatServiceError.invalidResponse
        }

        print("🔥 ChatService: Response data length: \(data.count) bytes")
        print("🔥 ChatService: Response data: \(String(data: data, encoding: .utf8) ?? "nil")")

        guard let chats = try JSONSerialization.jsonObject(with: data) as? [[String: Any]] else {
            print("🔥 ChatService: Failed to parse JSON response")
            throw ChatServiceError.invalidResponse
        }

        print("🔥 ChatService: Successfully loaded \(chats.count) chats")
        return chats
    }

    // Get specific chat with messages
    func getChat(chatId: String) async throws -> [String: Any] {
        print("ChatService: Loading chat: \(chatId)")

        let url = URL(string: "\(APIConfig.backendBaseURL)/api/v1/chat/\(chatId)")!
        var request = URLRequest(url: url)
        request.setValue(DeviceService.shared.deviceId, forHTTPHeaderField: "X-Device-ID")
        request.setValue(DeviceService.shared.userId, forHTTPHeaderField: "X-User-ID")

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw ChatServiceError.invalidResponse
        }

        guard let chat = try JSONSerialization.jsonObject(with: data) as? [String: Any] else {
            throw ChatServiceError.invalidResponse
        }

        print("ChatService: Loaded chat with \((chat["messages"] as? [[String: Any]])?.count ?? 0) messages")
        return chat
    }

    // Send streaming chat request via backend API
    private func sendStreamingChatRequestViaBackend(messages: [Message], model: AIModel, systemMessage: String?) async {
        let startTime = CFAbsoluteTimeGetCurrent()
        print("ChatService: Starting backend streaming request at \(startTime)")
        print("ChatService: Backend URL: \(APIConfig.backendBaseURL)")
        print("ChatService: Model: \(model.name)")
        print("ChatService: Messages count: \(messages.count)")

        guard !messages.isEmpty else {
            print("ChatService: ERROR - No messages found")
            await MainActor.run {
                streamingDelegate?.didFailStreaming(with: ChatServiceError.invalidResponse)
            }
            return
        }

        // Create conversation request body with all messages
        let conversationMessages = messages.map { message in
            return [
                "content": message.content,
                "is_user": message.isUser
            ]
        }

        let conversationRequest: [String: Any] = [
            "messages": conversationMessages
        ]

        // Use backend streaming endpoint with model parameter
        var urlComponents = URLComponents(string: "\(APIConfig.backendBaseURL)/api/v1/chat/stream")!
        urlComponents.queryItems = [
            URLQueryItem(name: "model_name", value: model.name)
        ]

        guard let url = urlComponents.url else {
            print("ChatService: ERROR - Failed to create URL")
            await MainActor.run {
                streamingDelegate?.didFailStreaming(with: ChatServiceError.invalidURL)
            }
            return
        }

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")

        // Add device-based authentication headers
        request.addValue(DeviceService.shared.deviceId, forHTTPHeaderField: "X-Device-ID")
        request.addValue(DeviceService.shared.userId, forHTTPHeaderField: "X-User-ID")
        request.addValue("text/event-stream", forHTTPHeaderField: "Accept")
        request.addValue("no-cache", forHTTPHeaderField: "Cache-Control")
        request.timeoutInterval = 60.0

        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: conversationRequest)
        } catch {
            print("ChatService: ERROR - Failed to serialize request body: \(error)")
            await MainActor.run {
                streamingDelegate?.didFailStreaming(with: ChatServiceError.requestFailed(error))
            }
            return
        }

        // Send the streaming request using a fresh URLSession
        print("ChatService: About to send request to: \(url.absoluteString)")
        print("ChatService: Request headers: \(request.allHTTPHeaderFields ?? [:])")
        print("ChatService: Request body: \(String(data: request.httpBody ?? Data(), encoding: .utf8) ?? "nil")")

        // Create optimized URLSession for streaming
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 60.0
        config.timeoutIntervalForResource = 120.0
        config.waitsForConnectivity = false
        config.allowsCellularAccess = true
        config.httpMaximumConnectionsPerHost = 1
        config.requestCachePolicy = .reloadIgnoringLocalAndRemoteCacheData
        config.urlCache = nil

        // Optimize for streaming
        config.httpAdditionalHeaders = [
            "Accept": "text/event-stream",
            "Cache-Control": "no-cache",
            "Connection": "keep-alive"
        ]

        let session = URLSession(configuration: config)

        do {
            // Update connection status
            await MainActor.run {
                connectionStatus = .connecting
            }

            let requestTime = CFAbsoluteTimeGetCurrent()
            let (asyncBytes, response) = try await session.bytes(for: request)
            let responseTime = CFAbsoluteTimeGetCurrent()
            print("ChatService: Backend request completed in \(responseTime - requestTime) seconds")
            print("ChatService: Response: \(response)")

            // Update to connected status
            await MainActor.run {
                connectionStatus = .connected
            }

            // Check response status
            guard let httpResponse = response as? HTTPURLResponse else {
                print("ChatService: ERROR - Invalid HTTP response")
                await MainActor.run {
                    streamingDelegate?.didFailStreaming(with: ChatServiceError.invalidResponse)
                }
                return
            }

            print("ChatService: HTTP Status Code: \(httpResponse.statusCode)")
            print("ChatService: Response Headers: \(httpResponse.allHeaderFields)")

            guard (200...299).contains(httpResponse.statusCode) else {
                print("ChatService: ERROR - HTTP error status: \(httpResponse.statusCode)")
                await MainActor.run {
                    streamingDelegate?.didFailStreaming(with: ChatServiceError.invalidResponse)
                }
                return
            }

            print("ChatService: Starting to process backend streaming response")
            var firstTokenReceived = false
            var lineCount = 0

            // Process each line immediately as it arrives
            for try await line in asyncBytes.lines {
                lineCount += 1
                print("ChatService: Line \(lineCount): '\(line)'")

                // Check for data lines
                guard line.hasPrefix("data: ") else {
                    print("ChatService: Skipping non-data line: \(line)")
                    continue
                }

                let dataStartIndex = line.index(line.startIndex, offsetBy: 6)
                let data = String(line[dataStartIndex...])
                print("ChatService: Processing data: '\(data)'")

                // End of stream check
                if data.trimmingCharacters(in: .whitespacesAndNewlines) == "[DONE]" {
                    print("ChatService: Stream completed normally")
                    await MainActor.run {
                        streamingDelegate?.didCompleteStreaming()
                    }
                    return
                }

                // Skip empty data
                guard !data.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
                    print("ChatService: Skipping empty data")
                    continue
                }

                // Clean and parse JSON data
                let cleanedData = data.trimmingCharacters(in: .whitespacesAndNewlines)
                guard let jsonData = cleanedData.data(using: .utf8) else {
                    print("ChatService: Failed to convert data to UTF8: '\(cleanedData)'")
                    continue
                }

                do {
                    if let json = try JSONSerialization.jsonObject(with: jsonData) as? [String: Any] {
                        print("ChatService: Parsed JSON: \(json)")

                        // Handle different message types
                        if let messageType = json["type"] as? String {
                            switch messageType {
                            case "start":
                                print("ChatService: Stream started")
                                // Update to streaming status
                                await MainActor.run {
                                    connectionStatus = .streaming
                                }
                                // Log first token timing for connection
                                if !firstTokenReceived {
                                    let firstTokenTime = CFAbsoluteTimeGetCurrent()
                                    print("ChatService: Stream connection established in \(firstTokenTime - startTime) seconds")
                                }

                            case "token":
                                if let content = json["content"] as? String, !content.isEmpty {
                                    print("ChatService: Extracted token: '\(content)'")

                                    // Log first actual token timing
                                    if !firstTokenReceived {
                                        let firstTokenTime = CFAbsoluteTimeGetCurrent()
                                        print("ChatService: First token received in \(firstTokenTime - startTime) seconds")
                                        firstTokenReceived = true
                                    }

                                    // Send token to delegate immediately
                                    await MainActor.run {
                                        print("ChatService: Calling delegate with token: '\(content)'")
                                        print("ChatService: Delegate exists: \(streamingDelegate != nil)")
                                        streamingDelegate?.didReceiveStreamingToken(content)
                                        print("ChatService: Delegate call completed")
                                    }
                                }

                            case "done":
                                print("ChatService: Stream completed via done message")
                                await MainActor.run {
                                    streamingDelegate?.didCompleteStreaming()
                                }
                                return

                            case "error":
                                if let errorMsg = json["error"] as? String {
                                    print("ChatService: Received error from backend: \(errorMsg)")
                                    await MainActor.run {
                                        streamingDelegate?.didFailStreaming(with: ChatServiceError.invalidResponse)
                                    }
                                    return
                                }

                            default:
                                print("ChatService: Unknown message type: \(messageType)")
                            }
                        } else {
                            // Fallback for old format
                            if let content = json["content"] as? String, !content.isEmpty {
                                print("ChatService: Extracted content (legacy): '\(content)'")

                                // Log first token timing
                                if !firstTokenReceived {
                                    let firstTokenTime = CFAbsoluteTimeGetCurrent()
                                    print("ChatService: First token received in \(firstTokenTime - startTime) seconds")
                                    firstTokenReceived = true
                                }

                                // Send token to delegate immediately
                                await MainActor.run {
                                    print("ChatService: Calling delegate with token: '\(content)'")
                                    print("ChatService: Delegate exists: \(streamingDelegate != nil)")
                                    streamingDelegate?.didReceiveStreamingToken(content)
                                    print("ChatService: Delegate call completed")
                                }
                            } else if let error = json["error"] as? String {
                                print("ChatService: Received error from backend: \(error)")
                                await MainActor.run {
                                    streamingDelegate?.didFailStreaming(with: ChatServiceError.invalidResponse)
                                }
                                return
                            } else {
                                print("ChatService: JSON missing expected fields: \(json)")
                            }
                        }
                    } else {
                        print("ChatService: Failed to parse JSON: '\(data)'")
                    }
                } catch {
                    print("ChatService: JSON parsing error: \(error) for data: '\(data)'")
                    print("ChatService: Raw data bytes: \(Array(data.utf8))")
                    print("ChatService: Data length: \(data.count)")

                    // Try to identify the issue
                    if data.contains("\"") {
                        print("ChatService: Data contains quotes")
                    }
                    if data.contains("\n") {
                        print("ChatService: Data contains newlines")
                    }
                    if data.contains("\\") {
                        print("ChatService: Data contains backslashes")
                    }
                    continue
                }
            }

            print("ChatService: Stream ended without [DONE] signal")
            await MainActor.run {
                streamingDelegate?.didCompleteStreaming()
            }

        } catch {
            print("ChatService: Request failed with error: \(error)")
            print("ChatService: Error type: \(type(of: error))")
            print("ChatService: Error details: \(error.localizedDescription)")
            await MainActor.run {
                streamingDelegate?.didFailStreaming(with: ChatServiceError.requestFailed(error))
            }
        }
    }

    // Test method to verify streaming is working
    func testStreaming() async {
        print("ChatService: Testing streaming connection...")

        let testMessage = Message(content: "Test", isUser: true, timestamp: Date())
        let testModel = AIModel(name: "GPT-4.1 nano", isPro: false, iconName: "openai")

        await sendStreamingChatRequestViaBackend(messages: [testMessage], model: testModel, systemMessage: nil)
    }
}
