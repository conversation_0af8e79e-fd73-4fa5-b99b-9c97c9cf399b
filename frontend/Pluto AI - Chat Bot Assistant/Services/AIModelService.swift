//
//  AIModelService.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.05.01.
//

import Foundation
import Combine

class AIModelService {
    // Singleton instance
    static let shared = AIModelService()

    // Publishers
    private let modelsSubject = CurrentValueSubject<[AIModel], Never>([])
    var models: AnyPublisher<[AIModel], Never> {
        return modelsSubject.eraseToAnyPublisher()
    }

    private init() {
        // Load initial data from local cache
        modelsSubject.send(AIModel.models)

        // Fetch data from backend
        Task {
            await fetchModels()
        }
    }

    // MARK: - API Methods

    func fetchModels() async {
        guard let url = URL(string: "\(APIConfig.backendBaseURL)/api/v1/ai-model/models") else {
            print("Invalid URL for AI models")
            return
        }

        do {
            // Create request with authentication headers
            var request = URLRequest(url: url)
            request.addValue(DeviceService.shared.deviceId, forHTTPHeaderField: "X-Device-ID")
            request.addValue(DeviceService.shared.userId, forHTTPHeaderField: "X-User-ID")

            let (data, response) = try await URLSession.shared.data(for: request)

            guard let httpResponse = response as? HTTPURLResponse, (200...299).contains(httpResponse.statusCode) else {
                print("Invalid response from server")
                return
            }

            // Parse the response
            struct AIModelResponse: Decodable {
                struct AIModelData: Decodable {
                    let id: String
                    let name: String
                    let is_pro: Bool
                    let icon_name: String
                }

                let models: [AIModelData]
            }

            let decodedResponse = try JSONDecoder().decode(AIModelResponse.self, from: data)

            // Convert to AIModel
            let models = decodedResponse.models.map { data in
                AIModel(
                    name: data.name,
                    isPro: data.is_pro,
                    iconName: data.icon_name
                )
            }

            // Update the subject
            DispatchQueue.main.async {
                self.modelsSubject.send(models)
            }
        } catch {
            print("Error fetching AI models: \(error.localizedDescription)")
        }
    }

    func getModelID(for modelName: String) async -> String {
        guard let encodedName = modelName.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed),
              let url = URL(string: "\(APIConfig.backendBaseURL)/api/v1/ai-model/model-id/\(encodedName)") else {
            return "gpt-4.1-nano-2025-04-14" // Default if URL creation fails (GPT-4.1 nano)
        }

        do {
            // Create request with authentication headers
            var request = URLRequest(url: url)
            request.addValue(DeviceService.shared.deviceId, forHTTPHeaderField: "X-Device-ID")
            request.addValue(DeviceService.shared.userId, forHTTPHeaderField: "X-User-ID")

            let (data, response) = try await URLSession.shared.data(for: request)

            guard let httpResponse = response as? HTTPURLResponse, (200...299).contains(httpResponse.statusCode) else {
                return "gpt-4.1-nano-2025-04-14" // Default if response is invalid (GPT-4.1 nano)
            }

            // Parse the response
            struct ModelIDResponse: Decodable {
                let model_id: String
            }

            let decodedResponse = try JSONDecoder().decode(ModelIDResponse.self, from: data)
            return decodedResponse.model_id
        } catch {
            print("Error fetching model ID: \(error.localizedDescription)")
            return "gpt-4.1-nano-2025-04-14" // Default if there's an error (GPT-4.1 nano)
        }
    }
}
