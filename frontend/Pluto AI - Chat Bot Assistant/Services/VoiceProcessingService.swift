//
//  VoiceProcessingService.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.06.18.
//

import Foundation
import SwiftUI
import Combine

// Voice chat state
enum VoiceChatState: Equatable {
    case idle
    case listening
    case processing
    case speaking
    case error(String)

    static func == (lhs: VoiceChatState, rhs: VoiceChatState) -> Bool {
        switch (lhs, rhs) {
        case (.idle, .idle), (.listening, .listening), (.processing, .processing), (.speaking, .speaking):
            return true
        case (.error(let lhsMessage), .error(let rhsMessage)):
            return lhsMessage == rhsMessage
        default:
            return false
        }
    }
}

// Voice chat message
struct VoiceChatMessage: Identifiable {
    let id = UUID()
    let content: String
    let isUser: Bool
    let timestamp: Date
    let audioTranscript: String?
    
    init(content: String, isUser: Bool, audioTranscript: String? = nil) {
        self.content = content
        self.isUser = isUser
        self.timestamp = Date()
        self.audioTranscript = audioTranscript
    }
}

class VoiceProcessingService: ObservableObject {
    // Singleton instance
    static let shared = VoiceProcessingService()
    
    // Published properties for UI updates
    @Published var state: VoiceChatState = .idle
    @Published var messages: [VoiceChatMessage] = []
    @Published var isActive = false
    @Published var currentTranscript = ""
    @Published var errorMessage: String?
    @Published var selectedModel: AIModel = AIModel.models.first!
    
    // Services
    private let speechRecognitionService = SpeechRecognitionService.shared
    private let textToSpeechService = TextToSpeechService.shared
    private let chatService = ChatService.shared
    
    // Cancellables for Combine
    private var cancellables = Set<AnyCancellable>()
    
    // Voice chat settings
    @Published var autoSpeak = true
    @Published var continuousListening = true
    @Published var voiceActivationThreshold: Float = 0.5
    
    private init() {
        setupBindings()
    }
    
    // MARK: - Public Methods
    
    func startVoiceChat(with model: AIModel? = nil) {
        guard !isActive else { return }
        
        // Set the model
        if let model = model {
            selectedModel = model
        }
        
        // Reset state
        messages.removeAll()
        errorMessage = nil
        isActive = true
        state = .idle
        
        // Start with a greeting
        addWelcomeMessage()
        
        // Start listening if continuous listening is enabled
        if continuousListening {
            startListening()
        }
    }
    
    func stopVoiceChat() {
        guard isActive else { return }
        
        // Stop all services
        speechRecognitionService.stopRecording()
        textToSpeechService.stopSpeaking()
        
        // Reset state
        isActive = false
        state = .idle
        currentTranscript = ""
        errorMessage = nil
    }
    
    func startListening() {
        guard isActive && state != .listening && state != .processing else { return }
        
        state = .listening
        currentTranscript = ""
        
        speechRecognitionService.startRecording { [weak self] recognizedText in
            self?.handleSpeechRecognition(recognizedText)
        }
    }
    
    func stopListening() {
        guard state == .listening else { return }
        
        speechRecognitionService.stopRecording()
        state = .idle
    }
    
    func sendTextMessage(_ text: String) {
        guard isActive && !text.isEmpty else { return }
        
        // Add user message
        let userMessage = VoiceChatMessage(content: text, isUser: true)
        messages.append(userMessage)
        
        // Process the message
        processUserMessage(text)
    }
    
    func toggleListening() {
        switch state {
        case .idle:
            startListening()
        case .listening:
            stopListening()
        case .speaking:
            textToSpeechService.stopSpeaking()
            state = .idle
        default:
            break
        }
    }
    
    func setModel(_ model: AIModel) {
        selectedModel = model
    }
    
    func clearConversation() {
        messages.removeAll()
        addWelcomeMessage()
    }
    
    // MARK: - Private Methods
    
    private func setupBindings() {
        // Listen to TTS state changes
        textToSpeechService.$isSpeaking
            .sink { [weak self] isSpeaking in
                if !isSpeaking && self?.state == .speaking {
                    self?.handleSpeechFinished()
                }
            }
            .store(in: &cancellables)
        
        // Listen to speech recognition errors
        speechRecognitionService.$errorMessage
            .compactMap { $0 }
            .sink { [weak self] error in
                self?.handleError(error)
            }
            .store(in: &cancellables)
    }
    
    private func addWelcomeMessage() {
        let welcomeText = "Hello! I'm Pluto AI. You can speak to me naturally, and I'll respond with voice. How can I help you today?"
        let welcomeMessage = VoiceChatMessage(content: welcomeText, isUser: false)
        messages.append(welcomeMessage)
        
        // Speak the welcome message if auto-speak is enabled
        if autoSpeak {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                self.speakMessage(welcomeText)
            }
        }
    }
    
    private func handleSpeechRecognition(_ recognizedText: String) {
        guard !recognizedText.isEmpty else {
            state = .idle
            return
        }
        
        currentTranscript = recognizedText
        
        // Add user message
        let userMessage = VoiceChatMessage(content: recognizedText, isUser: true, audioTranscript: recognizedText)
        messages.append(userMessage)
        
        // Process the message
        processUserMessage(recognizedText)
    }
    
    private func processUserMessage(_ text: String) {
        state = .processing

        // Convert messages to Message format for ChatService
        let chatMessages = messages.map { message in
            Message(
                content: message.content,
                isUser: message.isUser,
                timestamp: message.timestamp
            )
        }

        // Send to AI service
        Task {
            do {
                let response = try await generateAIResponse(messages: chatMessages)

                DispatchQueue.main.async {
                    // Add AI response
                    let aiMessage = VoiceChatMessage(content: response, isUser: false)
                    self.messages.append(aiMessage)

                    // Speak the response if auto-speak is enabled
                    if self.autoSpeak {
                        self.speakMessage(response)
                    } else {
                        self.state = .idle

                        // Start listening again if continuous listening is enabled
                        if self.continuousListening {
                            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                                self.startListening()
                            }
                        }
                    }
                }
            } catch {
                DispatchQueue.main.async {
                    self.handleError("Failed to get AI response: \(error.localizedDescription)")
                }
            }
        }
    }
    
    private func generateAIResponse(messages: [Message]) async throws -> String {
        // Use the existing ChatService to generate response
        return try await chatService.sendChatRequest(
            messages: messages,
            model: selectedModel,
            systemMessage: "You are Pluto AI, a helpful voice assistant. Keep your responses conversational and concise since they will be spoken aloud. Aim for 1-3 sentences unless more detail is specifically requested."
        )
    }
    
    private func speakMessage(_ text: String) {
        state = .speaking
        
        textToSpeechService.speak(text: text) { [weak self] in
            self?.handleSpeechFinished()
        }
    }
    
    private func handleSpeechFinished() {
        state = .idle
        
        // Start listening again if continuous listening is enabled
        if continuousListening && isActive {
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                self.startListening()
            }
        }
    }
    
    private func handleError(_ error: String) {
        errorMessage = error
        state = .error(error)
        
        // Reset to idle after a delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
            self.errorMessage = nil
            self.state = .idle
        }
    }
}

// MARK: - Voice Chat Settings

extension VoiceProcessingService {
    func setAutoSpeak(_ enabled: Bool) {
        autoSpeak = enabled
    }
    
    func setContinuousListening(_ enabled: Bool) {
        continuousListening = enabled
    }
    
    func setVoiceActivationThreshold(_ threshold: Float) {
        voiceActivationThreshold = max(0.0, min(1.0, threshold))
    }
}
