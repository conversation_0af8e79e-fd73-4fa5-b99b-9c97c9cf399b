//
//  URLProcessingService.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.06.18.
//

import Foundation
import SwiftUI

// Web page information
struct WebPage: Identifiable {
    let id = UUID()
    let url: String
    let title: String
    let description: String?
    let author: String?
    let publishedDate: Date?
    let siteName: String?
    let imageURL: String?
    let contentType: String
}

// URL processing result
struct URLProcessingResult {
    let webpage: WebPage
    let extractedText: String
    let summary: String
    let keyPoints: [String]
    let topics: [String]
    let wordCount: Int
    let processingTime: TimeInterval
}

// URL service errors
enum URLServiceError: LocalizedError {
    case invalidURL
    case networkError(String)
    case contentExtractionFailed
    case unsupportedContentType
    case contentTooLarge
    case processingFailed(String)
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid URL format"
        case .networkError(let message):
            return "Network error: \(message)"
        case .contentExtractionFailed:
            return "Failed to extract content from webpage"
        case .unsupportedContentType:
            return "Unsupported content type"
        case .contentTooLarge:
            return "Content is too large to process"
        case .processingFailed(let message):
            return "Processing failed: \(message)"
        }
    }
}

class URLProcessingService: ObservableObject {
    // Singleton instance
    static let shared = URLProcessingService()
    
    // Published properties for UI updates
    @Published var isProcessing = false
    @Published var processingProgress: Double = 0.0
    @Published var currentStep = ""
    @Published var lastResult: URLProcessingResult?
    @Published var errorMessage: String?
    
    // Services
    private let chatService = ChatService.shared
    
    // Configuration
    private let maxContentLength = 100000 // Limit content length for processing
    private let timeoutInterval: TimeInterval = 30.0
    
    private init() {}
    
    // MARK: - Public Methods
    
    func processURL(_ urlString: String, model: AIModel = AIModel.models.first!) async throws -> URLProcessingResult {
        let startTime = CFAbsoluteTimeGetCurrent()
        
        DispatchQueue.main.async {
            self.isProcessing = true
            self.processingProgress = 0.0
            self.currentStep = "Validating URL..."
            self.errorMessage = nil
        }
        
        do {
            // Step 1: Validate URL
            guard let url = URL(string: urlString), url.scheme != nil else {
                throw URLServiceError.invalidURL
            }
            
            DispatchQueue.main.async {
                self.processingProgress = 0.2
                self.currentStep = "Fetching webpage content..."
            }
            
            // Step 2: Fetch webpage content
            let (htmlContent, response) = try await fetchWebpageContent(url: url)
            
            DispatchQueue.main.async {
                self.processingProgress = 0.4
                self.currentStep = "Extracting text content..."
            }
            
            // Step 3: Extract text and metadata
            let webpageInfo = extractWebpageInfo(from: htmlContent, url: urlString, response: response)
            let extractedText = extractTextFromHTML(htmlContent)
            
            DispatchQueue.main.async {
                self.processingProgress = 0.6
                self.currentStep = "Generating summary with AI..."
            }
            
            // Step 4: Generate summary
            let summary = try await generateSummary(text: extractedText, webpageInfo: webpageInfo, model: model)
            
            DispatchQueue.main.async {
                self.processingProgress = 0.8
                self.currentStep = "Extracting key points..."
            }
            
            // Step 5: Extract key points
            let keyPoints = try await extractKeyPoints(text: extractedText, model: model)
            
            DispatchQueue.main.async {
                self.processingProgress = 0.9
                self.currentStep = "Identifying topics..."
            }
            
            // Step 6: Extract topics
            let topics = try await extractTopics(text: extractedText, model: model)
            
            let processingTime = CFAbsoluteTimeGetCurrent() - startTime
            let wordCount = extractedText.components(separatedBy: .whitespacesAndNewlines).filter { !$0.isEmpty }.count
            
            let result = URLProcessingResult(
                webpage: webpageInfo,
                extractedText: extractedText,
                summary: summary,
                keyPoints: keyPoints,
                topics: topics,
                wordCount: wordCount,
                processingTime: processingTime
            )
            
            DispatchQueue.main.async {
                self.processingProgress = 1.0
                self.currentStep = "Complete!"
                self.lastResult = result
                self.isProcessing = false
            }
            
            return result
            
        } catch {
            DispatchQueue.main.async {
                self.isProcessing = false
                self.errorMessage = error.localizedDescription
            }
            throw error
        }
    }
    
    func askQuestionAboutURL(_ question: String, urlResult: URLProcessingResult, model: AIModel = AIModel.models.first!) async throws -> String {
        let prompt = """
        Based on the following webpage content, please answer this question:
        
        Question: \(question)
        
        Webpage: \(urlResult.webpage.title)
        URL: \(urlResult.webpage.url)
        Content:
        \(urlResult.extractedText.prefix(maxContentLength))
        
        Please provide a detailed answer based on the webpage content.
        """
        
        let messages = [
            Message(content: prompt, isUser: true, timestamp: Date())
        ]
        
        return try await chatService.sendChatRequest(
            messages: messages,
            model: model,
            systemMessage: "You are a helpful assistant that answers questions based on webpage content. Provide accurate answers based only on the information from the webpage."
        )
    }
    
    // MARK: - Private Methods
    
    private func fetchWebpageContent(url: URL) async throws -> (String, URLResponse) {
        var request = URLRequest(url: url)
        request.timeoutInterval = timeoutInterval
        request.setValue("Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1", forHTTPHeaderField: "User-Agent")
        
        do {
            let (data, response) = try await URLSession.shared.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse,
                  200...299 ~= httpResponse.statusCode else {
                throw URLServiceError.networkError("HTTP \((response as? HTTPURLResponse)?.statusCode ?? 0)")
            }
            
            guard let htmlString = String(data: data, encoding: .utf8) else {
                throw URLServiceError.contentExtractionFailed
            }
            
            return (htmlString, response)
            
        } catch {
            if error is URLServiceError {
                throw error
            } else {
                throw URLServiceError.networkError(error.localizedDescription)
            }
        }
    }
    
    private func extractWebpageInfo(from html: String, url: String, response: URLResponse) -> WebPage {
        // Extract title
        let title = extractMetaTag(from: html, property: "title") ??
                   extractHTMLTag(from: html, tag: "title") ??
                   "Untitled"
        
        // Extract description
        let description = extractMetaTag(from: html, property: "description") ??
                         extractMetaTag(from: html, property: "og:description")
        
        // Extract author
        let author = extractMetaTag(from: html, property: "author") ??
                    extractMetaTag(from: html, property: "article:author")
        
        // Extract site name
        let siteName = extractMetaTag(from: html, property: "og:site_name") ??
                      URL(string: url)?.host
        
        // Extract image
        let imageURL = extractMetaTag(from: html, property: "og:image")
        
        // Extract content type
        let contentType = response.mimeType ?? "text/html"
        
        return WebPage(
            url: url,
            title: title,
            description: description,
            author: author,
            publishedDate: nil, // Could be extracted from meta tags
            siteName: siteName,
            imageURL: imageURL,
            contentType: contentType
        )
    }
    
    private func extractMetaTag(from html: String, property: String) -> String? {
        let patterns = [
            "<meta\\s+name=\"\(property)\"\\s+content=\"([^\"]*)\">",
            "<meta\\s+property=\"\(property)\"\\s+content=\"([^\"]*)\">",
            "<meta\\s+content=\"([^\"]*)\"\\s+name=\"\(property)\">",
            "<meta\\s+content=\"([^\"]*)\"\\s+property=\"\(property)\">"
        ]
        
        for pattern in patterns {
            if let regex = try? NSRegularExpression(pattern: pattern, options: .caseInsensitive),
               let match = regex.firstMatch(in: html, options: [], range: NSRange(html.startIndex..., in: html)),
               let range = Range(match.range(at: 1), in: html) {
                return String(html[range])
            }
        }
        
        return nil
    }
    
    private func extractHTMLTag(from html: String, tag: String) -> String? {
        let pattern = "<\(tag)[^>]*>([^<]*)</\(tag)>"
        
        if let regex = try? NSRegularExpression(pattern: pattern, options: .caseInsensitive),
           let match = regex.firstMatch(in: html, options: [], range: NSRange(html.startIndex..., in: html)),
           let range = Range(match.range(at: 1), in: html) {
            return String(html[range]).trimmingCharacters(in: .whitespacesAndNewlines)
        }
        
        return nil
    }
    
    private func extractTextFromHTML(_ html: String) -> String {
        var text = html
        
        // Remove script and style tags
        text = text.replacingOccurrences(of: "<script[^>]*>[\\s\\S]*?</script>", with: "", options: .regularExpression)
        text = text.replacingOccurrences(of: "<style[^>]*>[\\s\\S]*?</style>", with: "", options: .regularExpression)
        
        // Remove HTML tags
        text = text.replacingOccurrences(of: "<[^>]+>", with: " ", options: .regularExpression)
        
        // Decode HTML entities
        text = text.replacingOccurrences(of: "&nbsp;", with: " ")
        text = text.replacingOccurrences(of: "&amp;", with: "&")
        text = text.replacingOccurrences(of: "&lt;", with: "<")
        text = text.replacingOccurrences(of: "&gt;", with: ">")
        text = text.replacingOccurrences(of: "&quot;", with: "\"")
        text = text.replacingOccurrences(of: "&#39;", with: "'")
        
        // Clean up whitespace
        text = text.replacingOccurrences(of: "\\s+", with: " ", options: .regularExpression)
        text = text.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // Limit text length
        if text.count > maxContentLength {
            text = String(text.prefix(maxContentLength))
        }
        
        return text
    }
    
    private func generateSummary(text: String, webpageInfo: WebPage, model: AIModel) async throws -> String {
        let prompt = """
        Please provide a comprehensive summary of this webpage.
        
        Title: \(webpageInfo.title)
        URL: \(webpageInfo.url)
        Site: \(webpageInfo.siteName ?? "Unknown")
        
        Content:
        \(text)
        
        Please provide:
        1. A brief overview of the webpage
        2. Main topics covered
        3. Key information or insights
        4. Important details
        
        Keep the summary comprehensive but well-organized.
        """
        
        let messages = [
            Message(content: prompt, isUser: true, timestamp: Date())
        ]
        
        return try await chatService.sendChatRequest(
            messages: messages,
            model: model,
            systemMessage: "You are a helpful assistant that creates comprehensive summaries of web content. Focus on the most important information and organize it clearly."
        )
    }
    
    private func extractKeyPoints(text: String, model: AIModel) async throws -> [String] {
        let prompt = """
        Extract the key points from this webpage content. Return them as a numbered list.
        
        Content:
        \(text)
        
        Please identify the most important points, insights, or takeaways from this webpage.
        """
        
        let messages = [
            Message(content: prompt, isUser: true, timestamp: Date())
        ]
        
        let response = try await chatService.sendChatRequest(
            messages: messages,
            model: model,
            systemMessage: "You are a helpful assistant that extracts key points from web content. Return only the key points as a numbered list."
        )
        
        return parseListFromResponse(response)
    }
    
    private func extractTopics(text: String, model: AIModel) async throws -> [String] {
        let prompt = """
        Identify the main topics covered in this webpage. Return them as a simple list.
        
        Content:
        \(text)
        
        Please identify the main subjects, themes, or topics discussed on this webpage.
        """
        
        let messages = [
            Message(content: prompt, isUser: true, timestamp: Date())
        ]
        
        let response = try await chatService.sendChatRequest(
            messages: messages,
            model: model,
            systemMessage: "You are a helpful assistant that identifies topics from web content. Return only the topics as a simple list."
        )
        
        return parseListFromResponse(response)
    }
    
    private func parseListFromResponse(_ response: String) -> [String] {
        let lines = response.components(separatedBy: .newlines)
        var items: [String] = []
        
        for line in lines {
            let trimmed = line.trimmingCharacters(in: .whitespacesAndNewlines)
            if !trimmed.isEmpty {
                // Remove numbering, bullets, or dashes
                let cleaned = trimmed.replacingOccurrences(of: "^[0-9]+\\.\\s*", with: "", options: .regularExpression)
                    .replacingOccurrences(of: "^[-•*]\\s*", with: "", options: .regularExpression)
                    .trimmingCharacters(in: .whitespacesAndNewlines)
                
                if !cleaned.isEmpty {
                    items.append(cleaned)
                }
            }
        }
        
        return items
    }
}
