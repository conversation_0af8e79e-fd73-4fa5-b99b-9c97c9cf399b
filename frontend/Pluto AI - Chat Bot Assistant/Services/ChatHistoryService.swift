//
//  ChatHistoryService.swift
//  Pluto AI - Cha<PERSON> Assistant
//
//  Created by Augment on 2025.06.19.
//

import Foundation

class ChatHistoryService: ObservableObject {
    static let shared = ChatHistoryService()
    
    @Published var chats: [BackendChat] = []
    @Published var isLoading = false
    
    private init() {}
    
    // MARK: - Backend Chat Management
    
    // Create a new chat session
    func createChat(assistantId: String, title: String) async throws -> String {
        print("ChatHistoryService: Creating new chat with assistant: \(assistantId), title: \(title)")
        do {
            let chatId = try await ChatService.shared.createChat(assistantId: assistantId, title: title)
            print("ChatHistoryService: Successfully created chat with ID: \(chatId)")
            return chatId
        } catch {
            print("ChatHistoryService: Failed to create chat: \(error)")
            throw error
        }
    }
    
    // Send message to existing chat
    func sendMessage(chatId: String, message: String) async throws -> String {
        print("ChatHistoryService: Sending message to chat: \(chatId), message: \(message)")
        do {
            let response = try await ChatService.shared.sendMessageToChat(chatId: chatId, message: message)
            print("ChatHistoryService: Successfully sent message, AI response: \(response)")
            return response
        } catch {
            print("ChatHistoryService: Failed to send message: \(error)")
            throw error
        }
    }
    
    // Load user's chat list from backend
    func loadChats() async {
        print("🔥 ChatHistoryService: Starting to load chats from backend")
        print("🔥 ChatHistoryService: Backend URL: \(APIConfig.backendBaseURL)")
        print("🔥 ChatHistoryService: Device ID: \(DeviceService.shared.deviceId)")
        print("🔥 ChatHistoryService: User ID: \(DeviceService.shared.userId)")

        await MainActor.run {
            isLoading = true
        }

        do {
            print("🔥 ChatHistoryService: Calling ChatService.shared.getUserChats()")
            let chatData = try await ChatService.shared.getUserChats()
            print("🔥 ChatHistoryService: Received \(chatData.count) raw chat objects")

            let backendChats = chatData.compactMap { BackendChat.fromJSON($0) }
            print("🔥 ChatHistoryService: Converted to \(backendChats.count) BackendChat objects")

            await MainActor.run {
                self.chats = backendChats.sorted { $0.createdAt > $1.createdAt }
                isLoading = false
            }

            print("🔥 ChatHistoryService: Successfully loaded \(backendChats.count) chats")
            for (index, chat) in backendChats.enumerated() {
                print("🔥 ChatHistoryService: Chat \(index + 1): \(chat.title) (\(chat.id))")
            }
        } catch {
            print("🔥 ChatHistoryService: ERROR loading chats: \(error)")
            if let urlError = error as? URLError {
                print("🔥 ChatHistoryService: URL Error code: \(urlError.code.rawValue)")
                print("🔥 ChatHistoryService: URL Error description: \(urlError.localizedDescription)")
            }
            await MainActor.run {
                isLoading = false
            }
        }
    }
    
    // Load specific chat with messages
    func loadChat(chatId: String) async throws -> BackendChat {
        print("ChatHistoryService: Loading chat: \(chatId)")
        let chatData = try await ChatService.shared.getChat(chatId: chatId)
        
        guard let chat = BackendChat.fromJSON(chatData) else {
            throw ChatService.ChatServiceError.invalidResponse
        }
        
        return chat
    }
    
    // Convert backend chat to local Message format
    func convertToMessages(_ chat: BackendChat) -> [Message] {
        return chat.messages.map { backendMessage in
            Message(
                content: backendMessage.content,
                isUser: backendMessage.isUser,
                timestamp: backendMessage.createdAt
            )
        }
    }
}

// MARK: - Backend Chat Models

struct BackendChat: Identifiable, Codable {
    let id: String
    let userId: String
    let assistantId: String
    let title: String
    let createdAt: Date
    let messages: [BackendMessage]
    
    static func fromJSON(_ json: [String: Any]) -> BackendChat? {
        guard let id = json["id"] as? String,
              let userId = json["user_id"] as? String,
              let assistantId = json["assistant_id"] as? String,
              let title = json["title"] as? String,
              let createdAtString = json["created_at"] as? String else {
            return nil
        }
        
        let dateFormatter = ISO8601DateFormatter()
        guard let createdAt = dateFormatter.date(from: createdAtString) else {
            return nil
        }
        
        let messages: [BackendMessage]
        if let messagesData = json["messages"] as? [[String: Any]] {
            messages = messagesData.compactMap { BackendMessage.fromJSON($0) }
        } else {
            messages = []
        }
        
        return BackendChat(
            id: id,
            userId: userId,
            assistantId: assistantId,
            title: title,
            createdAt: createdAt,
            messages: messages
        )
    }
}

struct BackendMessage: Identifiable, Codable {
    let id: String
    let content: String
    let isUser: Bool
    let createdAt: Date
    
    static func fromJSON(_ json: [String: Any]) -> BackendMessage? {
        guard let id = json["id"] as? String,
              let content = json["content"] as? String,
              let isUser = json["is_user"] as? Bool,
              let createdAtString = json["created_at"] as? String else {
            return nil
        }
        
        let dateFormatter = ISO8601DateFormatter()
        guard let createdAt = dateFormatter.date(from: createdAtString) else {
            return nil
        }
        
        return BackendMessage(
            id: id,
            content: content,
            isUser: isUser,
            createdAt: createdAt
        )
    }
}
