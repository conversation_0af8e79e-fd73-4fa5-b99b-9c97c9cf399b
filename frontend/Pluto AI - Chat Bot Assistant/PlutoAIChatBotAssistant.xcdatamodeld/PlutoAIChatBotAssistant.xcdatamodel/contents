<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<model type="com.apple.IDECoreDataModeler.DataModel" documentVersion="1.0" lastSavedToolsVersion="1" systemVersion="11A491" minimumToolsVersion="Automatic" sourceLanguage="Swift" usedWithCloudKit="false" userDefinedModelVersionIdentifier="">
    <entity name="ChatHistoryEntity" representedClassName="ChatHistoryEntity" syncable="YES" codeGenerationType="class">
        <attribute name="date" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="id" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="isSaved" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="modelName" attributeType="String" defaultValueString="GPT-4.1 nano"/>
        <attribute name="title" attributeType="String"/>
        <relationship name="messages" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="MessageEntity" inverseName="chatHistory" inverseEntity="MessageEntity"/>
    </entity>
    <entity name="Item" representedClassName="Item" syncable="YES" codeGenerationType="class">
        <attribute name="timestamp" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
    </entity>
    <entity name="MessageEntity" representedClassName="MessageEntity" syncable="YES" codeGenerationType="class">
        <attribute name="content" attributeType="String"/>
        <attribute name="id" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="isUser" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="timestamp" attributeType="Date" usesScalarValueType="NO"/>
        <relationship name="chatHistory" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="ChatHistoryEntity" inverseName="messages" inverseEntity="ChatHistoryEntity"/>
    </entity>
    <elements>
        <element name="Item" positionX="-63" positionY="-18" width="128" height="44"/>
        <element name="ChatHistoryEntity" positionX="-63" positionY="0" width="128" height="89"/>
        <element name="MessageEntity" positionX="-54" positionY="36" width="128" height="104"/>
    </elements>
</model>
