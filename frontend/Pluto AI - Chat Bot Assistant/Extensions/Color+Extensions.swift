//
//  Color+Extensions.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.04.30.
//

import SwiftUI

extension Color {
    static let systemGray6 = Color(red: 28/255, green: 28/255, blue: 30/255)
    static let systemGray5 = Color(red: 44/255, green: 44/255, blue: 46/255)
    static let systemGray4 = Color(red: 58/255, green: 58/255, blue: 60/255)
    static let systemGray3 = Color(red: 72/255, green: 72/255, blue: 74/255)
    static let systemGray2 = Color(red: 99/255, green: 99/255, blue: 102/255)
    static let systemGray = Color(red: 142/255, green: 142/255, blue: 147/255)
    
    static let systemBackground = Color.black
    static let secondarySystemBackground = Color(red: 28/255, green: 28/255, blue: 30/255)
}
