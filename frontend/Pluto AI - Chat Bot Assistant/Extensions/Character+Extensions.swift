//
//  Character+Extensions.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.05.01.
//

import Foundation

extension Character {
    /// Check if the character is an emoji
    var isEmoji: Bool {
        // Swift's native way to check for emoji
        if let firstScalar = unicodeScalars.first, firstScalar.properties.isEmoji {
            return true
        }
        return false
    }
}
