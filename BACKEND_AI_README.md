# Pluto AI Backend - AI Services

This document explains how all AI-related functionality has been moved from the frontend to the backend for better performance, security, and maintainability.

## Overview

The backend now handles all AI-related functionality:

1. Chat completions with OpenAI
2. AI model selection and mapping
3. Image generation with DALL-E
4. System messages for assistants and suggestions
5. Subscription-based access to premium models

## API Endpoints

### Chat

- `POST /api/v1/chat/{chat_id}/messages` - Send a message and get an AI response

### AI Models

- `GET /api/v1/ai-model/models` - Get all available AI models
- `GET /api/v1/ai-model/model-id/{model_name}` - Get the actual model ID for a display name

### Image Generation

- `POST /api/v1/image/generate` - Generate an image using AI

### Assistants and Suggestions

- `GET /api/v1/assistant/assistants` - Get all available assistants
- `GET /api/v1/assistant/tools` - Get all available tools
- `GET /api/v1/assistant/suggestion-categories` - Get all suggestion categories
- `GET /api/v1/assistant/suggestions` - Get suggestions, optionally filtered by category
- `GET /api/v1/assistant/system-message` - Get system message for an assistant, tool, category, or suggestion

## Benefits of Backend AI Processing

1. **Security**: API keys are stored securely on the server, not in the client app
2. **Performance**: The backend can handle complex AI requests more efficiently
3. **Cost Control**: The backend can implement rate limiting and usage tracking
4. **Consistency**: All clients use the same AI models and configurations
5. **Easier Updates**: Adding new AI models or features only requires backend changes

## Implementation Details

### Chat Service

The backend's `ChatService` handles all communication with OpenAI's API:

```python
async def generate_response(messages: List[ChatMessage], model: str = "gpt-4o-mini") -> str:
    """Generate a response using OpenAI's API"""
    
    # Create the request
    request_data = ChatCompletionRequest(
        model=model,
        messages=[{"role": msg.role, "content": msg.content} for msg in messages],
        temperature=0.7,
        max_tokens=1000,
        stream=False
    )
    
    # Send request to OpenAI
    # ...
```

### Image Service

The backend's `ImageService` handles image generation with DALL-E:

```python
async def generate_image(
    prompt: str,
    model: str = "dall-e-3",
    size: str = "1024x1024",
    quality: str = "standard",
    n: int = 1
) -> List[Dict[str, str]]:
    """Generate an image using OpenAI's DALL-E API"""
    
    # Create the request
    request_data = {
        "prompt": prompt,
        "model": model,
        "size": size,
        "quality": quality,
        "n": n
    }
    
    # Send request to OpenAI
    # ...
```

### AI Model Management

The backend maintains a list of available AI models and maps display names to actual model IDs:

```python
# Define all AI models
AI_MODELS = [
    AIModel(name="GPT-4.1 nano", is_pro=False, icon_name="openai"),
    AIModel(name="GPT-4.1 mini", is_pro=True, icon_name="openai"),
    # ...
]

# Map display names to actual model IDs
MODEL_IDS = {
    "GPT-4o mini": "gpt-4o-mini",
    "GPT-4o": "gpt-4o",
    # ...
}
```

## Frontend Changes

The frontend has been updated to use the backend for all AI-related functionality:

1. **Removed Direct API Calls**: The frontend no longer makes direct calls to OpenAI
2. **New Services**: Added `AIModelService` and `ImageGenerationService` to communicate with the backend
3. **Updated ChatService**: Modified to always use the backend for chat completions
4. **Removed API Keys**: Removed all API keys from the frontend code

### Example: Chat Service

```swift
// Send a chat completion request
func sendChatRequest(messages: [Message], model: AIModel, systemMessage: String? = nil, assistantName: String? = nil) async throws -> String {
    // If we have an assistant name but no system message, get the system message from the backend
    var finalSystemMessage = systemMessage
    if let assistantName = assistantName, systemMessage == nil {
        finalSystemMessage = await getSystemMessage(for: assistantName)
    }
    
    // Always use the backend API
    if let authToken = UserDefaults.standard.string(forKey: "authToken") {
        return try await sendChatRequestViaBackend(messages: messages, model: model, systemMessage: finalSystemMessage, authToken: authToken)
    } else {
        throw ChatServiceError.authenticationRequired
    }
}
```

### Example: Image Generation Service

```swift
func generateImage(prompt: String) async throws -> URL {
    return try await withCheckedThrowingContinuation { continuation in
        generateImage(prompt: prompt) { result in
            continuation.resume(with: result)
        }
    }
}
```

## How to Use

### Chat Completions

To get a chat completion from the backend:

1. Create a chat if needed: `POST /api/v1/chat`
2. Send a message: `POST /api/v1/chat/{chat_id}/messages`
3. The backend will:
   - Process the message
   - Generate a response using the appropriate AI model
   - Store both the message and response
   - Return the AI response

### Image Generation

To generate an image:

1. Send a request to `POST /api/v1/image/generate` with:
   - `prompt`: The description of the image to generate
   - `model`: The model to use (default: "dall-e-3")
   - `size`: The image size (default: "1024x1024")
   - `quality`: The image quality (default: "standard")
   - `n`: The number of images to generate (default: 1)
2. The backend will:
   - Process the request
   - Generate the image using DALL-E
   - Return the image URL(s)

### AI Models

To get available AI models:

1. Send a request to `GET /api/v1/ai-model/models`
2. The backend will return a list of available models with:
   - `name`: The display name
   - `is_pro`: Whether the model requires a pro subscription
   - `icon_name`: The icon to display

## Testing

You can test the API endpoints using curl or Postman:

```bash
# Get AI models
curl -X GET http://localhost:8000/api/v1/ai-model/models \
  -H "Authorization: Bearer YOUR_AUTH_TOKEN"

# Generate an image
curl -X POST http://localhost:8000/api/v1/image/generate \
  -H "Authorization: Bearer YOUR_AUTH_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"prompt": "A beautiful sunset over mountains"}'
```

## Troubleshooting

### Common Issues

1. **Authentication Errors**: Make sure you're including a valid auth token in your requests
2. **Model Not Found**: Check that you're using a valid model name from the `/ai-model/models` endpoint
3. **Image Generation Fails**: Ensure your prompt is appropriate and not too complex

### Logs and Debugging

- Check the backend logs for any errors during API calls
- Use the `/health` endpoint to verify the backend is running correctly
- Test API endpoints directly before integrating with the frontend
