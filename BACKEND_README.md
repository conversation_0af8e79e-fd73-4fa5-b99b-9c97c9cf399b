# Pluto AI Backend - Assistants and Suggestions

This document explains how the backend now handles assistants, tools, and suggestions, moving the AI logic from the frontend to the backend for better performance.

## Overview

The backend now provides:

1. A catalog of assistants with their system messages
2. A catalog of tools with their system messages
3. Suggestion categories with their system messages
4. Suggestions for each category with their system messages
5. Endpoints to retrieve system messages for any assistant, tool, category, or suggestion

## API Endpoints

### Assistants

- `GET /api/v1/assistant/assistants` - Get all available assistants
- `GET /api/v1/assistant/assistants/{assistant_id}` - Get a specific assistant by ID or name

### Tools

- `GET /api/v1/assistant/tools` - Get all available tools
- `GET /api/v1/assistant/tools/{tool_id}` - Get a specific tool by ID or name

### Suggestion Categories

- `GET /api/v1/assistant/suggestion-categories` - Get all suggestion categories

### Suggestions

- `GET /api/v1/assistant/suggestions` - Get all suggestions
- `GET /api/v1/assistant/suggestions?category={category_name}` - Get suggestions for a specific category

### System Messages

- `GET /api/v1/assistant/system-message?assistant_name={name}` - Get system message for an assistant
- `GET /api/v1/assistant/system-message?tool_name={name}` - Get system message for a tool
- `GET /api/v1/assistant/system-message?category_name={name}` - Get system message for a category
- `GET /api/v1/assistant/system-message?suggestion_text={text}` - Get system message for a suggestion

## Data Models

### Assistant

```json
{
  "id": "uuid",
  "name": "Assistant Name",
  "description": "Description of the assistant",
  "icon_name": "icon.name",
  "background_color": {
    "red": 0.5,
    "green": 0.5,
    "blue": 0.5,
    "opacity": 1.0
  },
  "system_message": "You are a helpful assistant..."
}
```

### Tool

```json
{
  "id": "uuid",
  "name": "Tool Name",
  "description": "Description of the tool",
  "icon": "icon.name",
  "is_new": true,
  "icon_background_color": {
    "red": 0.5,
    "green": 0.5,
    "blue": 0.5,
    "opacity": 1.0
  },
  "system_message": "You are an expert on this tool..."
}
```

### Suggestion Category

```json
{
  "id": "uuid",
  "name": "Category Name",
  "icon": "🔍",
  "color": {
    "red": 0.5,
    "green": 0.5,
    "blue": 0.5,
    "opacity": 1.0
  },
  "system_message": "You are an expert in this category..."
}
```

### Suggestion

```json
{
  "id": "uuid",
  "text": "Suggestion text",
  "icon": "🔍",
  "category": "Category Name",
  "system_message": "You are an expert in this topic..."
}
```

## How It Works

1. The frontend fetches assistants, tools, and suggestions from the backend
2. When a user selects an assistant or suggestion, the frontend requests the appropriate system message from the backend
3. The system message is used to guide the AI's responses, making them more relevant and specialized

## Benefits

1. **Faster Performance**: The backend can process AI requests more efficiently
2. **Consistent Responses**: System messages are managed centrally
3. **Easier Updates**: Adding or modifying assistants and suggestions only requires backend changes
4. **Reduced Frontend Complexity**: The frontend doesn't need to manage complex AI logic

## Implementation Details

### Backend

The backend defines all assistants, tools, suggestion categories, and suggestions in the `assistant.py` and `assistant_endpoints.py` files. Each entity has a system message that guides the AI's responses.

When a chat request is received, the backend:
1. Looks up the appropriate system message based on the assistant or suggestion
2. Uses this system message to guide the AI's response
3. Returns the AI-generated content to the frontend

### Frontend

The frontend now uses the `AssistantService` to fetch assistants, tools, and suggestions from the backend. When sending a chat request, it includes the assistant name, and the backend handles finding the appropriate system message.

## How to Add New Assistants or Suggestions

To add a new assistant:
1. Add it to the `ASSISTANTS` list in `backend/app/routers/assistant.py`
2. Provide a descriptive system message that guides the AI's responses

To add a new suggestion category:
1. Add it to the `SUGGESTION_CATEGORIES` list in `backend/app/routers/assistant.py`
2. Provide a descriptive system message that guides the AI's responses

To add new suggestions:
1. Add them to the appropriate category list in `backend/app/routers/assistant_endpoints.py`
2. They will automatically use the system message from their category

## Testing

You can test the API endpoints using curl or a tool like Postman:

```bash
# Get all assistants
curl http://localhost:8000/api/v1/assistant/assistants

# Get a specific assistant
curl http://localhost:8000/api/v1/assistant/assistants/Logo%20Designer

# Get suggestions for a category
curl http://localhost:8000/api/v1/assistant/suggestions?category=E-Mail

# Get a system message
curl http://localhost:8000/api/v1/assistant/system-message?assistant_name=Logo%20Designer
```
