#!/bin/bash

# <PERSON><PERSON>t to update Contents.json files for assistant images
ASSETS_DIR="frontend/Pluto AI - Chat <PERSON>t Assistant/Assets.xcassets/assistant"

# Loop through all imageset directories
for imageset_dir in "$ASSETS_DIR"/*.imageset; do
    # Get the base name of the directory
    dir_name=$(basename "$imageset_dir" .imageset)
    
    # Find the jpg file in the directory
    jpg_file=$(find "$imageset_dir" -name "*.jpg" -type f | head -n 1)
    
    if [ -n "$jpg_file" ]; then
        # Get the jpg filename
        jpg_name=$(basename "$jpg_file")
        
        # Create new Contents.json content
        cat > "$imageset_dir/Contents.json" << EOF
{
  "images" : [
    {
      "filename" : "$jpg_name",
      "idiom" : "universal",
      "scale" : "1x"
    },
    {
      "idiom" : "universal",
      "scale" : "2x"
    },
    {
      "idiom" : "universal",
      "scale" : "3x"
    }
  ],
  "info" : {
    "author" : "xcode",
    "version" : 1
  }
}
EOF
        echo "Updated $imageset_dir/Contents.json with reference to $jpg_name"
    else
        echo "No JPG file found in $imageset_dir"
    fi
done

echo "All Contents.json files have been updated."
