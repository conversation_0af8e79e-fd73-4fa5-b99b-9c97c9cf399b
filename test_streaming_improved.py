#!/usr/bin/env python3
"""
Test script to verify the improved streaming chat functionality
"""

import requests
import json
import time

def test_streaming():
    url = "http://localhost:8000/api/v1/chat/stream"
    headers = {
        "Content-Type": "application/json",
        "X-Device-ID": "test-device",
        "X-User-ID": "test-user"
    }
    
    data = {
        "messages": [
            {"content": "Tell me a very short joke about programming", "is_user": True}
        ]
    }
    
    params = {"model_name": "GPT-4.1 nano"}
    
    print("🚀 Testing improved streaming chat...")
    print(f"URL: {url}")
    print(f"Data: {json.dumps(data, indent=2)}")
    print("\n" + "="*50)
    
    try:
        start_time = time.time()
        response = requests.post(url, headers=headers, json=data, params=params, stream=True)
        
        print(f"Status Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        print("\nStreaming content:")
        print("-" * 30)
        
        first_token_time = None
        token_count = 0
        full_response = ""
        
        for line in response.iter_lines(decode_unicode=True):
            if line:
                print(f"Raw line: {repr(line)}")
                
                if line.startswith("data: "):
                    data_part = line[6:]  # Remove "data: " prefix
                    
                    try:
                        parsed = json.loads(data_part)
                        print(f"Parsed JSON: {parsed}")
                        
                        message_type = parsed.get("type", "unknown")
                        
                        if message_type == "start":
                            connection_time = time.time() - start_time
                            print(f"✅ Stream started in {connection_time:.3f}s")
                            
                        elif message_type == "token":
                            if "content" in parsed:
                                token = parsed["content"]
                                if first_token_time is None:
                                    first_token_time = time.time() - start_time
                                    print(f"⚡ First token in {first_token_time:.3f}s")
                                
                                token_count += 1
                                full_response += token
                                print(f"Token {token_count}: '{token}'")
                                
                        elif message_type == "done":
                            total_time = time.time() - start_time
                            print(f"✅ Stream completed in {total_time:.3f}s")
                            print(f"📊 Total tokens: {token_count}")
                            print(f"📝 Full response: '{full_response}'")
                            break
                            
                        elif message_type == "error":
                            error_msg = parsed.get("error", "Unknown error")
                            print(f"❌ Error: {error_msg}")
                            break
                            
                    except json.JSONDecodeError as e:
                        print(f"JSON decode error: {e}")
                        print(f"Raw data: {repr(data_part)}")
                        
    except Exception as e:
        print(f"❌ Request failed: {e}")

if __name__ == "__main__":
    test_streaming()
